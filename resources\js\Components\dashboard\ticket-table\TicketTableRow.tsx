import React from 'react';
import { TableCell, TableRow } from '@/Components/ui/table';
import { Checkbox } from '@/Components/ui/checkbox';
import { Button } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import { Link } from '@inertiajs/react';
import { 
  MoreHorizontal, 
  Eye, 
  Trash2, 
  UserPlus, 
  User,
  MessageSquare,
  Calendar
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/Components/ui/dropdown-menu';
import { StatusUpdateDropdown } from '../status-update-dropdown';
import { getPriorityColor } from '@/Utils/utils';
import { route } from 'ziggy-js';

interface Ticket {
  id: number;
  title: string;
  status: string;
  priority: string;
  created_at: string;
  assignee?: {
    id: number;
    name: string;
    profile_photo_url?: string;
  };
  department?: {
    id: number;
    name: string;
  };
  comments_count: number;
}

interface AssignableUser {
  id: number;
  name: string;
  email: string;
  profile_photo_url?: string;
}

interface TicketTableRowProps {
  ticket: Ticket;
  isSelected: boolean;
  canAssignPosts: boolean;
  canDeleteAnyPosts: boolean;
  assignableUsers: AssignableUser[];
  onSelect: (ticketId: number, checked: boolean) => void;
  onAssign: (ticketId: number, assigneeId: number) => void;
}

export default function TicketTableRow({
  ticket,
  isSelected,
  canAssignPosts,
  canDeleteAnyPosts,
  assignableUsers,
  onSelect,
  onAssign,
}: TicketTableRowProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    const colors = {
      open: 'bg-blue-100 text-blue-800',
      in_progress: 'bg-yellow-100 text-yellow-800',
      resolved: 'bg-green-100 text-green-800',
      closed: 'bg-gray-100 text-gray-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <TableRow 
      className={`hover:bg-muted/50 ${!ticket.assignee ? 'bg-orange-50/30 border-l-2 border-l-orange-400' : ''}`}
    >
      {/* Checkbox */}
      <TableCell>
        <Checkbox
          checked={isSelected}
          onCheckedChange={(checked) => onSelect(ticket.id, checked as boolean)}
        />
      </TableCell>

      {/* Title */}
      <TableCell>
        <div className="flex flex-col">
          <Link 
            href={`/admin/tickets/${ticket.id}`}
            className="font-medium hover:underline"
          >
            {ticket.title}
          </Link>
          <span className="text-xs text-muted-foreground">#{ticket.id}</span>
        </div>
      </TableCell>

      {/* Status */}
      <TableCell>
        <StatusUpdateDropdown 
          currentStatus={ticket.status}
          ticketId={ticket.id}
        />
      </TableCell>

      {/* Priority */}
      <TableCell>
        <Badge 
          variant="outline" 
          className={getPriorityColor(ticket.priority)}
        >
          {ticket.priority}
        </Badge>
      </TableCell>

      {/* Assignee */}
      <TableCell>
        <div className="flex items-center gap-2">
          {ticket.assignee ? (
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6">
                <AvatarImage src={ticket.assignee.profile_photo_url || ''} />
                <AvatarFallback>{ticket.assignee.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <span className="text-sm font-medium">{ticket.assignee.name}</span>
                <span className="text-xs text-green-600 bg-green-50 px-1 rounded">Đã giao</span>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <div className="h-6 w-6 rounded-full bg-gray-100 flex items-center justify-center">
                <User className="h-3 w-3 text-gray-400" />
              </div>
              <div className="flex flex-col">
                <span className="text-sm text-muted-foreground">Chưa giao</span>
                <span className="text-xs text-orange-600 bg-orange-50 px-1 rounded">Cần giao việc</span>
              </div>
            </div>
          )}
          
          {canAssignPosts && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-7 w-7 p-0 hover:bg-blue-50 border border-blue-200"
                  title="Giao việc cho nhân viên"
                >
                  <UserPlus className="h-3 w-3 text-blue-600" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Giao cho</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {assignableUsers.length > 0 ? (
                  assignableUsers.map(assignee => (
                    <DropdownMenuItem
                      key={assignee.id}
                      onClick={() => onAssign(ticket.id, assignee.id)}
                      className="cursor-pointer"
                    >
                      <Avatar className="h-5 w-5 mr-2">
                        <AvatarImage src={assignee.profile_photo_url || ''} alt={assignee.name} />
                        <AvatarFallback className="text-xs">{assignee.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{assignee.name}</span>
                    </DropdownMenuItem>
                  ))
                ) : (
                  <div className="px-2 py-1.5 text-sm text-muted-foreground">
                    Không có nhân viên
                  </div>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </TableCell>

      {/* Department */}
      <TableCell>
        {ticket.department ? (
          <Badge variant="secondary">{ticket.department.name}</Badge>
        ) : (
          <span className="text-sm text-muted-foreground">-</span>
        )}
      </TableCell>

      {/* Comments */}
      <TableCell>
        <div className="flex items-center gap-1">
          <MessageSquare className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{ticket.comments_count}</span>
        </div>
      </TableCell>

      {/* Created Date */}
      <TableCell>
        <div className="flex items-center gap-1">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{formatDate(ticket.created_at)}</span>
        </div>
      </TableCell>

      {/* Actions */}
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem asChild>
              <Link href={`/admin/tickets/${ticket.id}`}>
                <Eye className="h-4 w-4 mr-2" />
                Xem
              </Link>
            </DropdownMenuItem>

            {canDeleteAnyPosts && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild className='text-red-600'>
                  <Link href={route('posts.destroy', ticket.id)}>
                    <Trash2 className="h-4 w-4 mr-2" />
                    Xóa
                  </Link>
                </DropdownMenuItem>
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  );
}
