<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;

class RateLimitService
{
    /**
     * Rate limit tiers configuration
     */
    private const TIER_CONFIGS = [
        'standard' => [
            'per_minute' => 60,
            'burst' => 10,
            'daily_limit' => 10000,
        ],
        'premium' => [
            'per_minute' => 120,
            'burst' => 20,
            'daily_limit' => 50000,
        ],
        'enterprise' => [
            'per_minute' => 300,
            'burst' => 50,
            'daily_limit' => 200000,
        ],
    ];

    /**
     * Check if user can make a request
     */
    public function canMakeRequest(User $user): array
    {
        if (!$user->isSystemUser()) {
            return ['allowed' => true, 'reason' => null];
        }

        // Check if user is active
        if (!$user->is_active) {
            return ['allowed' => false, 'reason' => 'User account is inactive'];
        }

        $config = $this->getUserRateLimitConfig($user);
        
        // Check daily limit
        $dailyCheck = $this->checkDailyLimit($user, $config['daily_limit']);
        if (!$dailyCheck['allowed']) {
            return $dailyCheck;
        }

        // Check burst limit
        $burstCheck = $this->checkBurstLimit($user, $config['burst']);
        if (!$burstCheck['allowed']) {
            return $burstCheck;
        }

        // Check per-minute limit with gradual backoff
        $minuteCheck = $this->checkMinuteLimit($user, $config['per_minute']);
        if (!$minuteCheck['allowed']) {
            return $minuteCheck;
        }

        return ['allowed' => true, 'reason' => null];
    }

    /**
     * Record a successful request
     */
    public function recordRequest(User $user): void
    {
        if (!$user->isSystemUser()) {
            return;
        }

        $userId = $user->id;
        $today = now()->format('Y-m-d');

        // Increment daily counter
        $dailyKey = "rate_limit:daily:{$userId}:{$today}";
        Cache::increment($dailyKey, 1);
        Cache::put($dailyKey, Cache::get($dailyKey, 0), now()->endOfDay());

        // Increment hourly counter for statistics
        $hourlyKey = "rate_limit:hourly:{$userId}:" . now()->format('Y-m-d-H');
        Cache::increment($hourlyKey, 1);
        Cache::put($hourlyKey, Cache::get($hourlyKey, 0), now()->addHours(2));

        // Reset violation counter on successful request
        $violationKey = "rate_limit:violations:{$userId}";
        Cache::forget($violationKey);
    }

    /**
     * Record a rate limit violation
     */
    public function recordViolation(User $user, string $type): void
    {
        $violationKey = "rate_limit:violations:{$user->id}";
        $violations = Cache::get($violationKey, 0) + 1;
        
        // Store violations for 1 hour
        Cache::put($violationKey, $violations, now()->addHour());

        // Apply gradual backoff if too many violations
        if ($violations >= 5) {
            $this->applyGradualBackoff($user, $violations);
        }
    }

    /**
     * Get rate limit status for user
     */
    public function getRateLimitStatus(User $user): array
    {
        if (!$user->isSystemUser()) {
            return ['tier' => 'none', 'limits' => []];
        }

        $config = $this->getUserRateLimitConfig($user);
        $userId = $user->id;
        $today = now()->format('Y-m-d');

        // Get current usage
        $dailyUsage = Cache::get("rate_limit:daily:{$userId}:{$today}", 0);
        $minuteKey = "rate_limit:user:{$userId}:minute";
        $burstKey = "rate_limit:user:{$userId}:burst";

        $minuteRemaining = RateLimiter::remaining($minuteKey, $config['per_minute']);
        $burstRemaining = RateLimiter::remaining($burstKey, $config['burst']);

        return [
            'tier' => $user->rate_limit_tier,
            'limits' => [
                'per_minute' => $config['per_minute'],
                'burst' => $config['burst'],
                'daily' => $config['daily_limit'],
            ],
            'usage' => [
                'daily' => $dailyUsage,
                'minute_remaining' => $minuteRemaining,
                'burst_remaining' => $burstRemaining,
            ],
            'reset_times' => [
                'minute_reset' => now()->addSeconds(RateLimiter::availableIn($minuteKey))->timestamp,
                'burst_reset' => now()->addSeconds(RateLimiter::availableIn($burstKey))->timestamp,
                'daily_reset' => now()->endOfDay()->timestamp,
            ],
            'violations' => Cache::get("rate_limit:violations:{$userId}", 0),
            'backoff_until' => Cache::get("rate_limit:backoff:{$userId}"),
        ];
    }

    /**
     * Get user's rate limit configuration
     */
    private function getUserRateLimitConfig(User $user): array
    {
        $tier = $user->rate_limit_tier ?? 'standard';
        $baseConfig = self::TIER_CONFIGS[$tier] ?? self::TIER_CONFIGS['standard'];

        // Allow user-specific overrides
        return [
            'per_minute' => $user->rate_limit_per_minute ?? $baseConfig['per_minute'],
            'burst' => $user->rate_limit_burst ?? $baseConfig['burst'],
            'daily_limit' => $baseConfig['daily_limit'],
        ];
    }

    /**
     * Check daily limit
     */
    private function checkDailyLimit(User $user, int $dailyLimit): array
    {
        $today = now()->format('Y-m-d');
        $dailyKey = "rate_limit:daily:{$user->id}:{$today}";
        $dailyUsage = Cache::get($dailyKey, 0);

        if ($dailyUsage >= $dailyLimit) {
            return [
                'allowed' => false,
                'reason' => 'Daily rate limit exceeded',
                'retry_after' => now()->endOfDay()->diffInSeconds(),
                'limit_type' => 'daily',
            ];
        }

        return ['allowed' => true];
    }

    /**
     * Check burst limit
     */
    private function checkBurstLimit(User $user, int $burstLimit): array
    {
        $burstKey = "rate_limit:user:{$user->id}:burst";
        
        if (!RateLimiter::tooManyAttempts($burstKey, $burstLimit)) {
            return ['allowed' => true];
        }

        $retryAfter = RateLimiter::availableIn($burstKey);
        $this->recordViolation($user, 'burst');

        return [
            'allowed' => false,
            'reason' => 'Burst rate limit exceeded',
            'retry_after' => $retryAfter,
            'limit_type' => 'burst',
        ];
    }

    /**
     * Check per-minute limit with gradual backoff
     */
    private function checkMinuteLimit(User $user, int $perMinute): array
    {
        // Check if user is in backoff period
        $backoffKey = "rate_limit:backoff:{$user->id}";
        $backoffUntil = Cache::get($backoffKey);
        
        if ($backoffUntil && now()->timestamp < $backoffUntil) {
            return [
                'allowed' => false,
                'reason' => 'Rate limit backoff period active',
                'retry_after' => $backoffUntil - now()->timestamp,
                'limit_type' => 'backoff',
            ];
        }

        $minuteKey = "rate_limit:user:{$user->id}:minute";
        
        if (!RateLimiter::tooManyAttempts($minuteKey, $perMinute)) {
            return ['allowed' => true];
        }

        $retryAfter = RateLimiter::availableIn($minuteKey);
        $this->recordViolation($user, 'minute');

        return [
            'allowed' => false,
            'reason' => 'Per-minute rate limit exceeded',
            'retry_after' => $retryAfter,
            'limit_type' => 'minute',
        ];
    }

    /**
     * Apply gradual backoff for repeated violations
     */
    private function applyGradualBackoff(User $user, int $violations): void
    {
        // Exponential backoff: 2^(violations-4) minutes, max 60 minutes
        $backoffMinutes = min(pow(2, $violations - 4), 60);
        $backoffUntil = now()->addMinutes($backoffMinutes)->timestamp;
        
        $backoffKey = "rate_limit:backoff:{$user->id}";
        Cache::put($backoffKey, $backoffUntil, now()->addMinutes($backoffMinutes + 5));
    }
}
