import React, { useEffect, useState } from "react";
import { useForm } from "@inertiajs/react";
import { Button } from "@/Components/ui/button";
import { Input } from "@/Components/ui/input";
import { Textarea } from "@/Components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/Components/ui/dialog";
import { Upload, X } from "lucide-react";
import { route } from "ziggy-js";

interface CategoryDialogFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  category?: any; // editing data
}

export default function CategoryDialogForm({ open, onOpenChange, category }: CategoryDialogFormProps) {
  const [logoPreview, setLogoPreview] = useState<string | null>(category?.logo || null);

  const { data, setData, post, put, processing, errors, reset } = useForm({
    title: category?.title || "",
    slug: category?.slug || "",
    description: category?.description || "",
    logo: null as File | null,
  });

  useEffect(() => {
    if (open) {
      reset();
      setLogoPreview(category?.logo || null);
      setData({
        title: category?.title || "",
        slug: category?.slug || "",
        description: category?.description || "",
        logo: null,
      });
    }
  }, [open, category]);

  const generateSlug = (title: string) => {
    const slug = title
      .normalize("NFD")
      .replace(/[\u0300-\u036f]/g, "")
      .toLowerCase()
      .replace(/[^\w\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .replace(/^-+|-+$/g, "");
    setData("slug", slug);
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setData("logo", file);
      setLogoPreview(URL.createObjectURL(file));
    }
  };

  const submit = (e: React.FormEvent) => {
    e.preventDefault();
    if (category) {
      post(route("admin.categories.update", category.id), { method: "put" });
    } else {
      post(route("admin.categories.store"));
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>{category ? "Chỉnh sửa danh mục" : "Thêm danh mục"}</DialogTitle>
        </DialogHeader>
        <form onSubmit={submit} className="space-y-4">
          <div>
            <label>Tiêu đề</label>
            <Input
              value={data.title}
              onChange={(e) => {
                setData("title", e.target.value);
                generateSlug(e.target.value);
              }}
            />
            {errors.title && <div className="text-red-500 text-sm">{errors.title}</div>}
          </div>
          <div>
            <label>Slug</label>
            <Input value={data.slug} onChange={(e) => setData("slug", e.target.value)} />
            {errors.slug && <div className="text-red-500 text-sm">{errors.slug}</div>}
          </div>
          <div>
            <label>Mô tả</label>
            <Textarea value={data.description} onChange={(e) => setData("description", e.target.value)} />
            {errors.description && <div className="text-red-500 text-sm">{errors.description}</div>}
          </div>
          <div>
            <label>Biểu tượng</label>
            {logoPreview ? (
              <div className="relative inline-block">
                <img src={logoPreview} alt="Logo" className="w-32 h-32 object-cover rounded-lg border" />
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                  onClick={() => {
                    setLogoPreview(null);
                    setData("logo", null);
                  }}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ) : (
              <div className="border-2 border-dashed p-4 text-center">
                <Upload className="mx-auto h-8 w-8 text-gray-400" />
                <input type="file" className="mt-2" onChange={handleLogoChange} />
              </div>
            )}
            {errors.logo && <div className="text-red-500 text-sm">{errors.logo}</div>}
          </div>
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Hủy
            </Button>
            <Button type="submit" disabled={processing}>
              {processing ? "Đang xử lý..." : category ? "Cập nhật" : "Tạo mới"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
