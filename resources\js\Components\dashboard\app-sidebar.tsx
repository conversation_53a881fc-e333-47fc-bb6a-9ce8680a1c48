import * as React from 'react';
import { usePage, Link } from '@inertiajs/react';
import {
  ArrowUpCircleIcon,
  BarChartIcon,
  FileTextIcon,
  ContactRound,
  SettingsIcon,
  LayoutDashboardIcon,
} from 'lucide-react';
import { NavMain } from '@/Components/dashboard/nav-main';
import { NavSecondary } from '@/Components/dashboard/nav-secondary';
import { NavUser } from '@/Components/dashboard/nav-user';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/Components/ui/sidebar';
import { NavProjects } from './nav-projects';
import useTypedPage from '@/Hooks/useTypedPage';

type MenuItem = {
  title?: string;
  name?: string;
  url: string;
  icon?: any;
  allowedRoles?: string[];
  items?: MenuItem[];
};
// Menu data có thêm allowedRoles
const data = {
  user: {
    name: 'shadcn',
    email: '<EMAIL>',
    avatar: '/avatars/shadcn.jpg',
  },

  dashboard: [
    {
      name: 'Quản Trị',
      url: '/admin',
      icon: LayoutDashboardIcon,
      allowedRoles: ['Admin', 'Manager', 'Employee'],
    }
  ],

  singleNavItems: [
    {
      title: 'Reports',
      url: '/admin/report',
      icon: BarChartIcon,
      allowedRoles: ['Admin', 'Manager'],
    },
  ],

  navMain: [
    {
      title: 'Ticket',
      url: '#',
      icon: FileTextIcon,
      allowedRoles: ['Admin', 'Manager', 'Employee'],
      items: [
        { title: 'Ticket', url: '/admin/posts', allowedRoles: ['Admin', 'Manager', 'Employee'] },
        { title: 'Danh Mục', url: '/admin/categories', allowedRoles: ['Admin', 'Manager', 'Employee'] },
        { title: 'Nhãn', url: '/admin/tags',  allowedRoles: ['Admin', 'Manager', 'Employee']},
        { title: 'Ticket Đã xóa', url: '/admin/posts/trash', allowedRoles: ['admin'] },
      ],
    },
    {
      title: 'Quản lý',
      url: '#',
      icon: ContactRound,
      allowedRoles: ['Admin', 'Manager'],
      items: [
        { title: 'Người dùng', url: '/admin/users', allowedRoles: ['Admin'] },
        { title: 'Quyền và Vai trò', url: '/admin/roles-permissions', allowedRoles: ['Admin'] },
        { title: 'Phòng ban', url: '/departments', allowedRoles: ['Admin', 'Manager'] },
        { title: 'Hệ thống người dùng', url: '/admin/system-users', allowedRoles: ['Admin'] },
        { title: 'Quản lý Token', url: '/admin/tokens', allowedRoles: ['Admin'] },
        
      ],
    },
    {
      title: 'Cài đặt',
      url: '#',
      icon: SettingsIcon,
      allowedRoles: ['Admin'],
      items: [
        { title: 'Quy tắc', url: '/admin/automation-rules', allowedRoles: ['Admin'] },
      ],
    },
    {
      title: 'Giám sát',
      url: '#',
      icon: BarChartIcon,
      allowedRoles: ['Admin'],
      items: [
        { title: 'Dashboard API', url: '/admin/api-dashboard', allowedRoles: ['Admin'] },
        { title: 'Nhật ký API', url: '/admin/audit/api-logs', allowedRoles: ['Admin'] },
        { title: 'Nhật ký Token', url: '/admin/audit/token-logs', allowedRoles: ['Admin'] },
        { title: 'Cảnh báo bảo mật', url: '/admin/audit/security-alerts', allowedRoles: ['Admin'] },
      ],
    },
  ],
};

// Hàm filter menu
function filterMenuByRole(menu: MenuItem[], roles: string[]): MenuItem[] {
  return menu
    .filter(
      item =>
        !item.allowedRoles ||
        item.allowedRoles.some(r => roles.includes(r))
    )
    .map(item => ({
      ...item,
      items: item.items ? filterMenuByRole(item.items, roles) : undefined,
    }));
}

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const auth = useTypedPage();
  const roles = auth?.props.auth.user?.roles.map(r => r.name) || [];

  const filteredDashboard = filterMenuByRole(data.dashboard, roles);
  const filteredNavMain = filterMenuByRole(data.navMain, roles);
  const filteredSingleNavItems = filterMenuByRole(data.singleNavItems, roles);

  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:p-1.5!">
              <Link href="/admin">
                <ArrowUpCircleIcon className="h-5 w-5" />
                <span className="text-base font-semibold">Support payment</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavProjects
          items={filteredDashboard.map(item => ({
            title: item.title ?? item.name ?? 'Dashboard',
            name: item.name ?? item.title ?? 'Dashboard',
            url: item.url,
            icon: (item.icon || LayoutDashboardIcon) as any,
          }))}
        />
        <NavMain
          items={filteredNavMain.map(item => ({
            title: item.title ?? '',
            url: item.url,
            icon: item.icon as any,
            items: (item.items || []).map(sub => ({
              title: sub.title ?? '',
              url: sub.url,
            })),
          }))}
        />
        <NavSecondary
          items={filteredSingleNavItems.map(item => ({
            title: item.title ?? '',
            url: item.url,
            icon: item.icon as any,
          }))}
          className="mt-auto"
        />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}
