<?php

namespace App\Http\Controllers;

use App\Data\Ticket\CreateTicketData;
use App\Models\Ticket;
use App\Services\TicketService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use App\Models\Category;

class PostController extends Controller
{
    use AuthorizesRequests;

    protected TicketService $ticketService;

    public function __construct(TicketService $ticketService)
    {
        $this->ticketService = $ticketService;
    }

    public function index(Request $request): \Inertia\Response
    {
        $data = $this->ticketService->getTicketsForIndex($request);

        return Inertia::render('Dashboard', $data);
    }

    public function getAllTicket(Request $request): \Inertia\Response
    {
        // Ai cũng có thể xem danh sách tickets

        $data = $this->ticketService->getTicketsForIndex($request);

        return Inertia::render('Tickets/Index', $data);
    }

    public function getMyTickets(Request $request): \Inertia\Response
    {
        $data = $this->ticketService->getMyTickets($request);

        // Add search suggestions
        $data['searchSuggestions'] = $this->getSearchSuggestions();

        return Inertia::render('Ticket/MyTickets', $data);
    }

    /**
     * Get my tickets data for AJAX requests
     */
    public function getMyTicketsData(Request $request): \Illuminate\Http\JsonResponse
    {
        $data = $this->ticketService->getMyTickets($request);
        return response()->json(['props' => $data]);
    }


    public function store(CreateTicketData $ticketData): \Illuminate\Http\RedirectResponse
    {
        // Không cần kiểm tra quyền - ai cũng có thể tạo tickets
        // $this->authorize('create', Ticket::class);

        $result = $this->ticketService->storeTicket($ticketData);

        if (! $result['success']) {
            return redirect()->back()->withErrors($result['errors'])->withInput();
        }

        return redirect()->back()->with('success', $result['message']);
    }

    public function show(string $slug): \Inertia\Response
    {
        $ticket = Ticket::getTicketBySlug($slug);

        // Ai cũng có thể xem chi tiết ticket

        $data = $this->ticketService->prepareTicketData($ticket);

        return Inertia::render('Ticket/TicketDetail', $data);
    }

    public function edit(string $slug): \Inertia\Response|\Illuminate\Http\RedirectResponse
    {
        $result = $this->ticketService->getEditTicketData($slug);

        if (! $result['success']) {
            return redirect()->route('/')->with('error', $result['error']);
        }

        // Chỉ admin hoặc chủ sở hữu mới edit được
        $ticket = Ticket::where('slug', $slug)->firstOrFail();
        if (!auth()->user()->can('update-any-tickets') && auth()->id() !== $ticket->user_id) {
            abort(403);
        }

        return Inertia::render('Tickets/EditTicket', $result);
    }

    public function update(CreateTicketData $request, Ticket $ticket): \Illuminate\Http\RedirectResponse
    {
        // Chỉ admin hoặc chủ sở hữu mới update được
        if (!auth()->user()->can('update-any-tickets') && auth()->id() !== $ticket->user_id) {
            abort(403);
        }

        $ticketData = CreateTicketData::from($request);
        $result = $this->ticketService->updateTicket($ticketData, $ticket);

        if (! $result['success']) {
            return redirect()->route('/')->with('error', $result['error']);
        }

        return redirect()->back()->with('success', $result['message']);
    }

   public function destroy(Ticket $ticket)
{
    $user = Auth::user();

    if (
        $user->hasRole('Admin') ||
        $user->hasRole('Manager') ||
        $user->can('delete-any-tickets') ||
        $user->id === $ticket->user_id
    ) {
        $result = $this->ticketService->deleteTicket($ticket);
        return redirect()->back()->with('success', 'Delete ticket successfully.');
    }

    abort(403, 'Unauthorized action.');
}

    public function filterTicketByCategory(Request $request, $categorySlug): \Inertia\Response
    {
        $data = $this->ticketService->getTicketsByCategorySlug($categorySlug);

        return Inertia::render('Tickets/Category', $data);
    }

    public function filterTicketByTag(Request $request, $tagsSlug): \Inertia\Response
    {
        $data = $this->ticketService->getTicketsByTagSlug($tagsSlug);

        return Inertia::render('Tickets/Tags', $data);
    }

    public function search(Request $request): \Inertia\Response
    {
        $data = $this->ticketService->searchTickets($request);

        return Inertia::render('Tickets/Search', $data);
    }

    public function getTopVoteTickets(Request $request): \Illuminate\Http\JsonResponse
    {
        $limit = $request->query('limit', 5);
        $tickets = $this->ticketService->getTopVoteTickets($limit);

        return response()->json($tickets);
    }

    public function getCountTicket(): \Illuminate\Http\JsonResponse
    {
        $ticketCount = $this->ticketService->getTicketCount();

        return response()->json($ticketCount);
    }

    public function topVotedTickets(Request $request): \Illuminate\Http\JsonResponse
    {
        $limit = $request->query('limit', 5);
        $tickets = $this->ticketService->getTopVotedTickets($limit);

        return response()->json(['data' => $tickets]);
    }

    public function showById($id)
    {
        $ticket = Ticket::with(['user', 'categories', 'tags',  'comments', 'upvotes'])->findOrFail($id);

        //        $this->authorize('viewDepartmentTickets', $ticket->department);

        $data = $this->ticketService->prepareTicketData($ticket);

        return response()->json($data['ticket']);
    }

    public function transfer(Request $request)
    {
        $apikey = $request->header('X-API-KEY');
        // Kiểm tra API key
        if ($apikey !== config('services.external_api.key')) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        // Xác thực nguồn
        $request->validate([
            'title' => 'required|string',
            'content' => 'required|string',
            'user_id' => 'required|exists:users,id',
            'tags' => 'nullable|array',
            'categories' => 'nullable|array',
        ]);
        // Tạo ticket
        $result = $this->ticketService->storeTransferredTicket($request->all());

        if (! $result['success']) {
            return response()->json(['error' => $result['errors']], 422);
        }

        return response()->json(['success' => true, 'ticket' => $result['ticket']]);
    }

    /**
     * @throws AuthorizationException
     */
    public function undoDelete(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate([
            'ticket_id' => 'required',
        ]);
        $result = $this->ticketService->undoDeleteTicket($request->ticket_id);

        return response()->json($result, $result['success'] ? 200 : 400);
    }

    public function updateStatus(Request $request, Ticket $ticket): \Illuminate\Http\RedirectResponse
    {
        $this->authorize('update', $ticket);
        
        if ($ticket->user_id !== auth()->id()) {
            throw new AuthorizationException('You are not authorized to update this ticket!');
        }

        $request->validate([
            'is_published' => 'required|boolean',
        ]);
        $ticket->update([
            'is_published' => $request->boolean('is_published'),
        ]);

        return redirect()->back()->with('success', 'change status successfully.');
    }

    public function restore($id)
    {
        $ticket = Ticket::withTrashed()->findOrFail($id);

        if ($ticket->trashed()) {
            $ticket->restore();

            return redirect()->back()->with('success', 'Ticket restored successfully.');
        }

        return redirect()->back()->with('error', 'Ticket cannot be restored.');
    }

    public function getTrash()
    {
        $tickets = Ticket::onlyTrashed()->get();

        return Inertia::render('Tickets/Trash', [
            'tickets' => $tickets,
        ]);
    }

    /**
     * Get search suggestions from various sources
     */
    private function getSearchSuggestions(): array
    {
        // Get popular search terms from user's tickets
        $userId = auth()->id();
        $userTerms = \DB::table('tickets')
            ->select(\DB::raw('LOWER(title) as term'))
            ->where('user_id', $userId)
            ->where('is_published', true)
            ->whereNotNull('title')
            ->get()
            ->pluck('term')
            ->flatMap(function ($title) {
                // Extract meaningful words from titles
                $words = preg_split('/[\s\-_]+/', $title);
                return array_filter($words, function ($word) {
                    return strlen($word) >= 3 && !in_array(strtolower($word), [
                        'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was',
                        'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now',
                        'old', 'see', 'two', 'who', 'boy', 'did', 'she', 'use', 'way', 'why', 'oil', 'sit', 'set'
                    ]);
                });
            })
            ->countBy()
            ->sortDesc()
            ->take(5)
            ->keys()
            ->toArray();

        // Get category names as suggestions
        $categoryTerms = \App\Models\Category::select('title')
            ->get()
            ->pluck('title')
            ->map(fn($title) => strtolower($title))
            ->toArray();

        // Get common terms for personal tickets
        $personalTerms = [
            'my issue', 'my request', 'my problem', 'my question',
            'urgent', 'help needed', 'follow up', 'update',
            'resolved', 'pending', 'in progress'
        ];

        // Combine and deduplicate
        $allSuggestions = array_unique(array_merge($userTerms, $categoryTerms, $personalTerms));

        // Return top 12 suggestions
        return array_slice($allSuggestions, 0, 12);
    }
}
