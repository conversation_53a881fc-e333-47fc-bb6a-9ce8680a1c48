<?php

namespace App\Services;

use App\Models\TokenAuditLog;
use App\Models\User;
use Illuminate\Support\Collection;
use <PERSON>vel\Sanctum\PersonalAccessToken;

class TokenManagementService
{
    /**
     * Create a new token for a system user
     */
    public function createToken(
        User $user,
        string $name,
        array $abilities = ['*'],
        ?string $description = null,
        ?string $expiresAt = null,
        string $tokenType = 'api',
        ?array $allowedIps = null,
        ?int $performedBy = null
    ): array {
        if (!$user->isActiveSystemUser()) {
            throw new \InvalidArgumentException('User must be an active system user');
        }

        // Create the token
        $token = $user->createToken($name, $abilities);
        $tokenModel = $token->accessToken;

        // Update with additional fields
        $updateData = [
            'token_type' => $tokenType,
            'description' => $description,
            'is_active' => true,
            'created_by_ip' => request()->ip(),
        ];

        if ($expiresAt) {
            $updateData['expires_at'] = $expiresAt;
        }

        if ($allowedIps) {
            $updateData['allowed_ips'] = $allowedIps;
        }

        $tokenModel->update($updateData);

        // Log the creation
        TokenAuditLog::logTokenOperation(
            $tokenModel->id,
            $name,
            $user->id,
            'created',
            $performedBy,
            'Token created via web interface',
            null,
            $updateData
        );

        return [
            'token' => $token->plainTextToken,
            'token_model' => $tokenModel,
        ];
    }

    /**
     * Revoke a token
     */
    public function revokeToken(
        PersonalAccessToken $token,
        ?string $reason = null,
        ?int $performedBy = null
    ): bool {
        // Log before deletion
        TokenAuditLog::logTokenOperation(
            $token->id,
            $token->name,
            $token->tokenable_id,
            'revoked',
            $performedBy,
            $reason ?? 'Token revoked',
            [
                'last_used_at' => $token->last_used_at,
                'usage_count' => $token->usage_count,
            ]
        );

        return $token->delete();
    }

    /**
     * Rotate a token (create new, revoke old)
     */
    public function rotateToken(
        PersonalAccessToken $oldToken,
        ?string $newName = null,
        ?string $reason = null,
        ?int $performedBy = null
    ): array {
        $user = $oldToken->tokenable;
        $newName = $newName ?? $oldToken->name . '_rotated_' . now()->format('YmdHis');

        // Store old token data
        $oldTokenData = [
            'id' => $oldToken->id,
            'name' => $oldToken->name,
            'abilities' => $oldToken->abilities,
            'last_used_at' => $oldToken->last_used_at,
            'expires_at' => $oldToken->expires_at,
            'token_type' => $oldToken->token_type,
            'description' => $oldToken->description,
            'allowed_ips' => $oldToken->allowed_ips,
        ];

        // Create new token
        $newToken = $user->createToken($newName, $oldToken->abilities ?? ['*']);
        $newTokenModel = $newToken->accessToken;

        // Copy settings
        $newTokenModel->update([
            'expires_at' => $oldToken->expires_at,
            'token_type' => $oldToken->token_type ?? 'api',
            'description' => $oldToken->description,
            'allowed_ips' => $oldToken->allowed_ips,
            'is_active' => true,
            'rotated_at' => now(),
            'rotation_reason' => $reason ?? 'Token rotation',
        ]);

        // Log rotation
        TokenAuditLog::logTokenOperation(
            $oldToken->id,
            $oldToken->name,
            $user->id,
            'rotated',
            $performedBy,
            $reason ?? 'Token rotation',
            $oldTokenData,
            [
                'new_token_id' => $newTokenModel->id,
                'new_token_name' => $newName,
            ]
        );

        // Log new token creation
        TokenAuditLog::logTokenOperation(
            $newTokenModel->id,
            $newName,
            $user->id,
            'created',
            $performedBy,
            'Created as rotation of token: ' . $oldToken->name,
            null,
            [
                'rotated_from' => $oldToken->id,
                'rotated_from_name' => $oldToken->name,
            ]
        );

        // Delete old token
        $oldToken->delete();

        return [
            'token' => $newToken->plainTextToken,
            'token_model' => $newTokenModel,
        ];
    }

    /**
     * Get tokens for a user with statistics
     */
    public function getUserTokens(User $user): Collection
    {
        return $user->tokens()
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($token) {
                $token->is_expired = $token->expires_at && $token->expires_at <= now();
                $token->status = $this->getTokenStatus($token);
                return $token;
            });
    }

    /**
     * Get token statistics
     */
    public function getTokenStatistics(?User $user = null): array
    {
        $query = PersonalAccessToken::query();
        
        if ($user) {
            $query->where('tokenable_id', $user->id);
        } else {
            $query->whereHasMorph('tokenable', [User::class], function ($q) {
                $q->where('is_system_user', true);
            });
        }

        $total = $query->count();
        $active = $query->where('is_active', true)
                       ->where(function ($q) {
                           $q->whereNull('expires_at')
                             ->orWhere('expires_at', '>', now());
                       })->count();
        
        $expired = $query->where(function ($q) {
            $q->where('is_active', false)
              ->orWhere('expires_at', '<=', now());
        })->count();

        $recentlyUsed = $query->where('last_used_at', '>=', now()->subDays(7))->count();

        return [
            'total' => $total,
            'active' => $active,
            'expired' => $expired,
            'recently_used' => $recentlyUsed,
            'inactive' => $total - $active - $expired,
        ];
    }

    /**
     * Get token status
     */
    private function getTokenStatus(PersonalAccessToken $token): string
    {
        if (!$token->is_active) {
            return 'inactive';
        }

        if ($token->expires_at && $token->expires_at <= now()) {
            return 'expired';
        }

        return 'active';
    }

    /**
     * Update token settings
     */
    public function updateToken(
        PersonalAccessToken $token,
        array $data,
        ?int $performedBy = null
    ): bool {
        $oldValues = $token->only(['description', 'expires_at', 'is_active', 'allowed_ips']);
        
        $updated = $token->update($data);

        if ($updated) {
            TokenAuditLog::logTokenOperation(
                $token->id,
                $token->name,
                $token->tokenable_id,
                'updated',
                $performedBy,
                'Token settings updated',
                $oldValues,
                $data
            );
        }

        return $updated;
    }
}
