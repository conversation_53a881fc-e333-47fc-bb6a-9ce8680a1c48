<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class DebugAdminAccessSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('=== DEBUG ADMIN ACCESS ===');
        
        $adminUser = User::where('email', '<EMAIL>')->first();
        
        if (!$adminUser) {
            $this->command->error('Admin user not found!');
            return;
        }
        
        $this->command->info("User: {$adminUser->name} ({$adminUser->email})");
        
        // Check roles
        $roles = $adminUser->roles;
        $this->command->info("User roles: " . $roles->pluck('name')->join(', '));
        
        if ($roles->isEmpty()) {
            $this->command->error('User has no roles!');
            
            // Assign Admin role
            $adminRole = Role::where('name', 'Admin')->first();
            if ($adminRole) {
                $adminUser->assignRole('Admin');
                $this->command->info('Assigned Admin role to user');
            } else {
                $this->command->error('Admin role not found!');
            }
        }
        
        // Check permissions
        $this->command->info('=== USER PERMISSIONS ===');
        $permissions = [
            'access-admin-dashboard',
            'view-any-posts',
            'manage-users',
            'manage-roles-permissions'
        ];
        
        foreach ($permissions as $permission) {
            $hasPermission = $adminUser->can($permission);
            $this->command->info("  {$permission}: " . ($hasPermission ? '✓' : '✗'));
        }
        
        // Check if permission exists
        $this->command->info('=== PERMISSION EXISTS ===');
        foreach ($permissions as $permission) {
            $exists = Permission::where('name', $permission)->exists();
            $this->command->info("  {$permission}: " . ($exists ? '✓' : '✗'));
        }
        
        // Check Admin role permissions
        $adminRole = Role::where('name', 'Admin')->first();
        if ($adminRole) {
            $this->command->info('=== ADMIN ROLE PERMISSIONS ===');
            $rolePermissions = $adminRole->permissions->pluck('name');
            foreach ($permissions as $permission) {
                $hasPermission = $rolePermissions->contains($permission);
                $this->command->info("  {$permission}: " . ($hasPermission ? '✓' : '✗'));
            }
        }
        
        $this->command->info('=== SOLUTION ===');
        $this->command->info('Try logging in with: <EMAIL> / password123');
        $this->command->info('If still blocked, check middleware: app/Http/Middleware/CheckAdminAccess.php');
    }
}
