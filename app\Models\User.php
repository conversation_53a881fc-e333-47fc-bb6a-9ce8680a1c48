<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use HasRoles;
    use Notifiable;
    use TwoFactorAuthenticatable;

    /**
     * The guard name for <PERSON><PERSON> Permission
     */
    protected $guard_name = 'web';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'provider',
        'provider_id',
        'google_id',
        'github_id',
        'is_system_user',
        'system_type',
        'system_description',
        'rate_limit_tier',
        'rate_limit_per_minute',
        'rate_limit_burst',
        'allowed_ips',
        'last_api_call_at',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array<int, string>
     */
    protected $appends = [
        'profile_photo_url',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_system_user' => 'boolean',
            'is_active' => 'boolean',
            'allowed_ips' => 'array',
            'last_api_call_at' => 'datetime',
        ];
    }

    public function departments(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Departments::class, 'department_user', 'user_id', 'department_id');
    }

    public function comments()
    {
        return $this->hasMany(Comments::class);
    }

    public function isAdmin()
    {
        return $this->hasRole('Admin');
    }

    public function assignedTickets()
    {
        return $this->hasMany(Ticket::class, 'assignee_id');
    }

    /**
     * Check if user is a system user
     */
    public function isSystemUser(): bool
    {
        return $this->is_system_user;
    }

    /**
     * Check if system user is active
     */
    public function isActiveSystemUser(): bool
    {
        return $this->is_system_user && $this->is_active;
    }

    /**
     * Get tickets created by this system user
     */
    public function createdTickets()
    {
        return $this->hasMany(Ticket::class, 'user_id');
    }

    /**
     * Update last API call timestamp
     */
    public function updateLastApiCall(): void
    {
        $this->update(['last_api_call_at' => now()]);
    }

    /**
     * Check if IP is allowed for this system user
     */
    public function isIpAllowed(string $ip): bool
    {
        if (!$this->is_system_user || empty($this->allowed_ips)) {
            return true; // No restrictions for regular users or if no IPs specified
        }

        return in_array($ip, $this->allowed_ips);
    }

    /**
     * Get rate limit configuration
     */
    public function getRateLimitConfig(): array
    {
        return [
            'tier' => $this->rate_limit_tier,
            'per_minute' => $this->rate_limit_per_minute,
            'burst' => $this->rate_limit_burst,
        ];
    }

    /**
     * Scope to get only system users
     */
    public function scopeSystemUsers($query)
    {
        return $query->where('is_system_user', true);
    }

    /**
     * Scope to get only active system users
     */
    public function scopeActiveSystemUsers($query)
    {
        return $query->where('is_system_user', true)->where('is_active', true);
    }
}
