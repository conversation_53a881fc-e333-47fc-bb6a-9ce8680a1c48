import { Ability, AbilityBuilder, AbilityClass, MongoAbility, createMongoAbility } from '@casl/ability';
import { Permission, Role, User } from '../types/Role';

// Định nghĩa các hành động và đối tượng mà CASL sẽ kiểm tra theo chiến lược phân quyền
export type Actions =
    | 'create' | 'read' | 'update' | 'delete' | 'manage'
    | 'view-public' | 'view-any' | 'view-own'
    | 'update-any' | 'update-own' | 'update-status'
    | 'delete-any-posts' | 'delete-own'
    | 'access-admin-dashboard' | 'assign-roles' | 'moderate' | 'assign'
    | 'assign-posts' | 'assign-users' | 'view-department' | 'manage-department' | 'manage-own'
    | 'manage-roles-permissions';

export type Subjects =
    | 'Post' | 'User' | 'Tag' | 'Category' | 'Department' | 'Comment'
    | 'AutomationRule' | 'RolePermission' | 'AdminDashboard' | 'all';

// Define the subject types with their fields
type PostSubject = {
  id: string;
  user: {
    id: number;
  };
};

type CommentSubject = {
  id: string;
  user: {
    id: number;
  };
};

export type AppAbility = MongoAbility<[Actions, Subjects | PostSubject | CommentSubject]>;
export const AppAbility = Ability as AbilityClass<AppAbility>;

// Hàm tạo ability dựa trên user hiện tại
export function defineRulesFor(user: User | null): AppAbility {
  const { can, cannot, build } = new AbilityBuilder<AppAbility>(createMongoAbility);

  if (!user) {
    // Người dùng chưa đăng nhập - có thể xem public posts và tạo tickets
    can('view-public', 'Post');
    can('create', 'Post'); // Ai cũng có thể tạo tickets
    can('read', 'Category');
    can('read', 'Tag');
    can('read', 'Department');
    return build();
  }

  // Mapping permissions từ backend sang CASL rules
  if (user.permissions) {
    user.permissions.forEach(permission => {
      mapPermissionToRule(permission.name, can);
    });
  }

  // Nếu user có roles, cũng map permissions từ roles
  if (user.roles) {
    user.roles.forEach(role => {
      if (role.permissions) {
        role.permissions.forEach(permission => {
          mapPermissionToRule(permission.name, can);
        });
      }
    });
  }

  // Thêm rules đặc biệt cho ownership
  can('update', 'Post', { user: { id: user.id } });
  can('delete', 'Post', { user: { id: user.id } });
  can('update', 'Comment', { user: { id: user.id } });
  can('delete', 'Comment', { user: { id: user.id } });

  // Ai cũng có thể tạo posts/tickets
  can('create', 'Post');

  return build();
}

// Hàm mapping permission từ backend sang CASL rules
function mapPermissionToRule(permissionName: string, can: any) {
  switch (permissionName) {
    // Posts permissions
    case 'view-public-posts':
      can('view-public', 'Post');
      break;
    case 'view-any-posts':
      can('view-any', 'Post');
      can('read', 'Post');
      break;
    case 'view-own-posts':
      can('view-own', 'Post');
      break;
    // case 'create-posts': // Bỏ vì ai cũng có thể tạo posts
    //   can('create', 'Post');
    //   break;
    case 'update-any-posts':
      can('update-any', 'Post');
      can('update', 'Post');
      break;
    case 'update-own-posts':
      can('update-own', 'Post');
      break;
    case 'delete-any-posts':
      can('delete-any-posts', 'Post');
      can('delete', 'Post');
      break;
    case 'delete-own-posts':
      can('delete-own', 'Post');
      break;
    case 'update-status-posts':
      can('update-status', 'Post');
      break;

    // Admin Dashboard
    case 'access-admin-dashboard':
      can('access-admin-dashboard', 'AdminDashboard');
      break;

    // Department Posts
    case 'view-department-posts':
      can('view-department', 'Post');
      break;

    // Assign Posts - Admin và Manager
    case 'assign-posts':
      can('assign', 'Post');
      can('assign-posts', 'Post');
      can('assign-users', 'Post');
      break;

    // Department Users
    case 'manage-department-users':
      can('manage-department', 'User');
      break;

    // Own Department
    case 'manage-own-department':
      can('manage-own', 'Department');
      break;

    // Users permissions
    case 'view-users':
      can('read', 'User');
      break;
    case 'update-users':
      can('update', 'User');
      break;
    case 'delete-users':
      can('delete', 'User');
      break;
    case 'assign-roles':
      can('assign-roles', 'User');
      break;

    // Roles & Permissions
    case 'manage-roles-permissions':
      can('manage', 'RolePermission');
      break;

    // Categories & Tags
    case 'manage-categories':
      can('manage', 'Category');
      can('create', 'Category');
      can('update', 'Category');
      can('delete', 'Category');
      break;
    case 'manage-tags':
      can('manage', 'Tag');
      can('create', 'Tag');
      can('update', 'Tag');
      can('delete', 'Tag');
      break;

    // Departments
    case 'manage-departments':
      can('manage', 'Department');
      can('create', 'Department');
      can('update', 'Department');
      can('delete', 'Department');
      break;
    case 'assign-users-to-departments':
      can('assign-users', 'Department');
      break;

    // Comments
    case 'create-comments':
      can('create', 'Comment');
      break;
    case 'moderate-comments':
      can('moderate', 'Comment');
      can('update', 'Comment');
      can('delete', 'Comment');
      break;

    default:
      // Log unknown permissions for debugging
      console.warn(`Unknown permission: ${permissionName}`);
      break;
  }
}

// Tạo ability mặc định (sẽ được cập nhật khi user đăng nhập)
const defaultAbility = defineRulesFor(null);
export default defaultAbility;