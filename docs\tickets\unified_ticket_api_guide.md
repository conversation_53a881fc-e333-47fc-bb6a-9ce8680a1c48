# Hướng dẫn Triển khai API Nhận Ticket (Phương pháp <PERSON> nhất)

> **M<PERSON><PERSON> tiêu**: <PERSON><PERSON> cấp một lộ trình triển khai A-Z, kết hợp tốc độ của Laravel Sanctum và sự rõ ràng của kiến trúc "System User" để tạo ra một API nhận ticket an toàn, dễ quản lý và dễ mở rộng.

---

## 0) Triết lý thiết kế

1.  **X<PERSON><PERSON> thực nhanh gọn**: Sử dụng **Bearer <PERSON>ken** c<PERSON><PERSON> Sanctum, tận dụng hệ sinh thái có sẵn của <PERSON>.
2.  **Định danh rõ ràng**: Áp dụng khái niệm **"System User"**. Mỗi hệ thống bên ngoài là một `User` thực sự trong CSDL, gi<PERSON><PERSON> quản lý và truy vết dễ dàng.
3.  **Tách biệt vai trò**: <PERSON><PERSON> biệt rõ ràng giữa **<PERSON><PERSON><PERSON><PERSON> t<PERSON> (Creator)** - là System User, và **Người yêu cầu (Requester)** - là người dùng cuối cùng cần hỗ trợ.

---

## 1) Bước 1: Chuẩn bị Cơ sở dữ liệu (Migrations)

Chúng ta cần một migration để tạo bảng `tickets` và một bảng để chống trùng lặp.

### 1.1) Bảng `tickets`

Chạy lệnh: `php artisan make:migration create_tickets_table`

```php
// database/migrations/YYYY_MM_DD_HHMMSS_create_tickets_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->longText('content');
            
            // --- Định danh ---
            // Người tạo (là System User)
            $table->foreignId('user_id')->constrained('users')->comment('ID of the System User who created the ticket');
            
            // Người yêu cầu (là người dùng cuối)
            $table->string('requester_name');
            $table->string('requester_email');

            // --- Phân loại & Truy vết ---
            $table->unsignedBigInteger('category_id')->nullable();
            $table->enum('priority', ['low','medium','high','urgent'])->default('medium');
            $table->string('source_system')->nullable()->comment('e.g., crm, payment, monitoring');
            $table->string('external_id')->nullable()->comment('The ticket ID from the source system');

            // --- Dữ liệu thô ---
            $table->json('payload')->nullable()->comment('Original JSON payload for debugging');
            
            $table->timestamps();

            // Index để tăng tốc độ truy vấn
            $table->index(['source_system', 'external_id']);
        });
    }

    public function down(): void {
        Schema::dropIfExists('tickets');
    }
};
```

### 1.2) Bảng chống trùng lặp (Tùy chọn nhưng khuyến khích)

Bảng này đảm bảo `external_id` từ một `source_system` là duy nhất, tránh tạo ticket hai lần cho cùng một sự kiện.

Chạy lệnh: `php artisan make:migration create_ticket_external_refs_table`

```php
// database/migrations/YYYY_MM_DD_HHMMSS_create_ticket_external_refs_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('ticket_external_refs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ticket_id')->constrained('tickets')->cascadeOnDelete();
            $table->string('source_system');
            $table->string('external_id');
            $table->timestamps();

            $table->unique(['source_system', 'external_id']);
        });
    }

    public function down(): void {
        Schema::dropIfExists('ticket_external_refs');
    }
};
```

Sau khi tạo xong, chạy lệnh `php artisan migrate`.

---

## 2) Bước 2: Tạo và Quản lý "System User"

Không cần tạo bảng mới. Chúng ta sẽ dùng chính bảng `users` của Laravel.

### 2.1) Tạo System User

Bạn có thể tạo bằng Tinker, Seeder, hoặc một giao diện quản trị.

Mở Tinker: `php artisan tinker`

```php
// Trong Tinker
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

User::create([
    'name' => 'Payment System', // Tên hệ thống
    'email' => '<EMAIL>', // Email định danh, không cần có thật
    'password' => Hash::make(Str::random(40)), // Mật khẩu ngẫu nhiên, không dùng để đăng nhập
]);

User::create([
    'name' => 'CRM System',
    'email' => '<EMAIL>',
    'password' => Hash::make(Str::random(40)),
]);
```

### 2.2) Cấp API Token cho System User

Tạo một Artisan command để việc này trở nên dễ dàng.

Chạy lệnh: `php artisan make:command IssueApiTokenForSystem`

```php
// app/Console/Commands/IssueApiTokenForSystem.php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class IssueApiTokenForSystem extends Command
{
    protected $signature = 'system:issue-token {email} {token_name}';
    protected $description = 'Issue a Sanctum API token for a system user by their email.';

    public function handle()
    {
        $email = $this->argument('email');
        $tokenName = $this->argument('token_name');

        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return self::FAILURE;
        }

        $token = $user->createToken($tokenName);

        $this->info("Token created for user: {$user->name}");
        $this->line("Email: {$user->email}");
        $this->line("Token Name: {$tokenName}");
        $this->warn("Token: {$token->plainTextToken}");
        $this->info("Store this token securely. It will not be shown again.");

        return self::SUCCESS;
    }
}
```

**Sử dụng:**

```bash
php artisan system:issue-token <EMAIL> payment-gateway-token
```

Lưu lại token được sinh ra. Đây chính là "mật khẩu" để hệ thống bên ngoài truy cập API của bạn.

---

## 3) Bước 3: Xây dựng API Endpoint

### 3.1) Route

Thêm vào file `routes/api.php`:

```php
// routes/api.php
use App\Http\Controllers\Api\V1\TicketController;
use Illuminate\Support\Facades\Route;

Route::middleware(['auth:sanctum', 'throttle:60,1'])
    ->prefix('v1')
    ->group(function () {
        Route::post('/tickets', [TicketController::class, 'store']);
    });
```

### 3.2) Form Request (Để validate dữ liệu)

Chạy lệnh: `php artisan make:request Api/V1/StoreTicketRequest`

```php
// app/Http/Requests/Api/V1/StoreTicketRequest.php
namespace App\Http\Requests\Api\V1\;

use Illuminate\Foundation\Http\FormRequest;

class StoreTicketRequest extends FormRequest
{
    public function authorize(): bool
    {
        // Middleware 'auth:sanctum' đã xử lý việc xác thực
        return true;
    }

    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'content' => ['required', 'string'],
            'requester_name' => ['required', 'string', 'max:255'],
            'requester_email' => ['required', 'email', 'max:255'],
            'priority' => ['nullable', 'in:low,medium,high,urgent'],
            'category_key' => ['nullable', 'string', 'max:255'], // Dùng để map sang category_id
            'tags' => ['nullable', 'array'],
            'tags.*' => ['string', 'max:50'],
            'external_id' => ['nullable', 'string', 'max:255'],
        ];
    }
}
```

### 3.3) Service Class (Để xử lý logic)

Tạo file `app/Services/TicketIntakeService.php`:

```php
// app/Services/TicketIntakeService.php
namespace App\Services;

use App\Models\Ticket;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class TicketIntakeService
{
    public function createFromExternal(User $systemUser, array $data): array
    {
        $sourceSystem = $data['source_system'] ?? $systemUser->name;

        // --- Chống trùng lặp ---
        if (!empty($data['external_id'])) {
            $ref = DB::table('ticket_external_refs')
                ->where('source_system', $sourceSystem)
                ->where('external_id', $data['external_id'])
                ->first();

            if ($ref) {
                return [
                    'status' => 'duplicate',
                    'ticket_id' => (int) $ref->ticket_id,
                ];
            }
        }

        $ticket = null;
        DB::transaction(function () use ($systemUser, $data, $sourceSystem, &$ticket) {
            // --- Tạo Ticket ---
            $ticket = Ticket::create([
                'title' => $data['title'],
                'content' => $data['content'],
                'user_id' => $systemUser->id, // Gán System User là người tạo
                'requester_name' => $data['requester_name'],
                'requester_email' => $data['requester_email'],
                'priority' => $data['priority'] ?? 'medium',
                'source_system' => $sourceSystem,
                'external_id' => $data['external_id'] ?? null,
                'payload' => $data,
                // 'category_id' => $this->mapCategory($data['category_key']), // Logic map category (tùy chọn)
            ]);

            // --- Gắn Tags (tùy chọn) ---
            // if (!empty($data['tags'])) { ... }

            // --- Lưu ref chống trùng ---
            if (!empty($data['external_id'])) {
                DB::table('ticket_external_refs')->insert([
                    'ticket_id' => $ticket->id,
                    'source_system' => $sourceSystem,
                    'external_id' => $data['external_id'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        });

        return [
            'status' => 'created',
            'ticket_id' => $ticket->id,
        ];
    }
}
```

### 3.4) Controller

Chạy lệnh: `php artisan make:controller Api/V1/TicketController`

```php
// app/Http/Controllers/Api/V1/TicketController.php
namespace App\Http\Controllers\Api\V1\;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\StoreTicketRequest;
use App\Services\TicketIntakeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TicketController extends Controller
{
    public function __construct(private TicketIntakeService $service) {}

    public function store(StoreTicketRequest $request): JsonResponse
    {
        // $request->user() chính là System User đã được xác thực bởi Sanctum
        $result = $this->service->createFromExternal($request->user(), $request->validated());

        if ($result['status'] === 'duplicate') {
            return response()->json([
                'message' => 'Duplicate ticket detected.',
                'ticket_id' => $result['ticket_id'],
                'ticket_url' => url('/tickets/'.$result['ticket_id']) // URL ví dụ
            ], 409); // 409 Conflict
        }

        return response()->json([
            'message' => 'Ticket created successfully.',
            'ticket_id' => $result['ticket_id'],
            'ticket_url' => url('/tickets/'.$result['ticket_id'])
        ], 201); // 201 Created
    }
}
```

### 3.5) Model

Cập nhật model `app/Models/Ticket.php`:

```php
// app/Models/Ticket.php
namespace App\Models\;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Ticket extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'user_id',
        'requester_name',
        'requester_email',
        'category_id',
        'priority',
        'source_system',
        'external_id',
        'payload',
    ];

    protected $casts = [
        'payload' => 'array',
    ];

    /**
     * Lấy System User đã tạo ticket này.
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
```

---

## 4) Bước 4: Sử dụng API

Hệ thống bên ngoài giờ có thể gọi API của bạn như sau:

```bash
curl -X POST https://your-domain.com/api/v1/tickets \
 -H "Authorization: Bearer <PASTE_TOKEN_HERE>" \
 -H "Content-Type: application/json" \
 -H "Accept: application/json" \
 -d '{
  "title": "Lỗi thanh toán cho đơn hàng #556677",
  "content": "Khách hàng báo không thể hoàn thành thanh toán bằng thẻ Visa.",
  "requester_name": "Trần Thị B",
  "requester_email": "<EMAIL>",
  "priority": "high",
  "source_system": "payment-gateway",
  "external_id": "payment-error-987654",
  "tags": ["payment", "visa-error"]
 }'
```

---

## 5) Kết luận

Bằng cách làm theo hướng dẫn này, bạn đã:
1.  Xây dựng một API an toàn bằng Laravel Sanctum.
2.  Áp dụng thành công kiến trúc "System User" để quản lý định danh hệ thống một cách rõ ràng.
3.  Phân biệt được ai là người tạo ticket (máy) và ai là người cần hỗ trợ (người thật).
4.  Có sẵn cơ chế chống trùng lặp ticket mạnh mẽ.

Đây là một nền tảng vững chắc để bạn mở rộng các tính năng khác như gửi thông báo, mapping category, tự động gán ticket...
