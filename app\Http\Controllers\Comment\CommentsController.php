<?php

namespace App\Http\Controllers\Comment;

use App\Events\CommentPosted;
use App\Events\NewCommentCreated;
use App\Http\Controllers\Controller;
use App\Http\Requests\StoreCommentRequest;
use App\Models\Comments;
use App\Models\Ticket;
use App\Notifications\NewCommentNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Inertia\Inertia;

class CommentsController extends Controller
{
    public function store(StoreCommentRequest $request)
    {
        // \Log::info('Comment store request received', [
        //     'user_id' => auth()->id(),
        //     'request_data' => $request->all()
        // ]);

        $validated = $request->validated();

        // Check if user is HR staff and set is_hr_response flag
        $isHrResponse = auth()->user()->departments->contains(function ($department) {
            return $department->name === 'HR' || $department->name === 'Human Resources';
        });

        $comment = Comments::create([
            'comment' => $validated['comment'],
            'ticket_id' => $validated['ticket_id'],
            'user_id' => auth()->id(),
            'parent_id' => $validated['parent_id'] ?? null,
            'is_hr_response' => $isHrResponse,
        ]);

        $comment->load(['user.roles', 'user.departments']);

        // \Log::info('Comment created successfully', [
        //     'comment_id' => $comment->id,
        //     'user_id' => $comment->user_id,
        //     'post_id' => $comment->post_id
        // ]);

        // Broadcast to other users only (not the author)
        broadcast(new CommentPosted($comment))->toOthers();
        broadcast(new NewCommentCreated($comment))->toOthers();

        $postOwner = $comment->ticket->user;
        if ($postOwner->id !== auth()->id()) {
            $postOwner->notify(new NewCommentNotification($comment));
        }

        return back()->with('success', 'Comment added successfully!');
    }

    public function show(Ticket $ticket)
    {
        $comments = $ticket->comments()
            ->whereNull('parent_id')
            ->with([
                'user.roles',
                'user.departments',
                'replies.user.roles',
                'replies.user.departments',
            ])
            ->latest()
            ->paginate(5);

        return Inertia::render('Tickets/TicketDetail', [
            'ticket' => $ticket,
            'comments' => [
                'data' => $comments->items(),
                'next_page_url' => $comments->nextPageUrl(),
                'prev_page_url' => $comments->previousPageUrl(),
                'current_page' => $comments->currentPage(),
                'last_page' => $comments->lastPage(),
                'per_page' => $comments->perPage(),
                'total' => $comments->total(),
                'from' => $comments->firstItem(),
                'to' => $comments->lastItem(),
            ],
        ]);
    }

    public function destroy(Comments $comment)
    {
        if (Gate::denies('delete-comment', $comment)) {
            abort(403, 'Unauthorized action.');
        }

        $comment->delete();

        return redirect()->back()->with('success', 'Comment deleted successfully!');
    }
}
