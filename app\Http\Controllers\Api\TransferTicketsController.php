<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Ticket;
use App\Models\Category;
use App\Models\Tag;
use App\Models\Departments;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Notifications\NewPostNotification;
class TransferTicketsController extends Controller
{
    /**
     * Transfer ticket from external product
     */
    public function transferTicket(Request $request)
    {
        // Validate API key
        if (!$this->validateApiKey($request)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid API key'
            ], 401);
        }

        // Validate input
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'category' => 'required|string|max:100',
            'email' => 'required|email|max:255',
            'username' => 'required|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Check if title already exists (prevent duplicates)
            $existingTicket = Ticket::where('title', $request->title)->first();
            if ($existingTicket) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ticket with this title already exists',
                    'existing_ticket' => [
                        'id' => $existingTicket->id,
                        'slug' => $existingTicket->slug,
                        'url' => route('tickets.show', $existingTicket->slug)
                    ]
                ], 409);
            }

            // Find or create user
            $user = User::where('email', $request->email)->first();
            $isNewUser = false;
            $password = null;

            if (!$user) {
                // Create new user
                $password = Str::random(12);
                $user = User::create([
                    'name' => $request->username,
                    'email' => $request->email,
                    'password' => Hash::make($password),
                    'email_verified_at' => now(),
                    'is_system_user' => false,
                ]);
                $isNewUser = true;
            }

            // Find or create category
            $category = Category::firstOrCreate(
                ['title' => $request->category],
                ['description' => 'Category for ' . $request->category . ' product']
            );

            // Find or create support tag
            $supportTag = Tag::firstOrCreate(
                ['name' => 'support'],
            );

            // Get default department (first one)
            $department = Departments::first();
            if (!$department) {
                // Create default department if none exists
                $department = Departments::create([
                    'name' => 'General Support',
                    'description' => 'General support department'
                ]);
            }

            // Create ticket
            $ticket = Ticket::create([
                'title' => $request->title,
                'content' => $request->content,
                'slug' =>  Str::slug($request->title),
                'user_id' => $user->id,
                'is_published' => false,
            ]);

            // Attach category and tag
            $ticket->categories()->attach($category->id);
            $ticket->tags()->attach($supportTag->id);

            // Send email notification
            $this->sendNotificationEmail($user, $ticket, $isNewUser, $password);
              $users = User::whereHas('roles', function ($query) {
                    $query->whereIn('name', ['Admin', 'Employee']);
                })->where('id', '!=', $ticket->user_id)->get();

                foreach ($users as $user) {
                    $user->notify(new NewPostNotification($ticket));
                }

            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => $isNewUser ? 'Ticket created and user account created' : 'Ticket created successfully',
                'data' => [
                    'ticket' => [
                        'id' => $ticket->id,
                        'title' => $ticket->title,
                        'slug' => $ticket->slug,
                        'status' => $ticket->status,
                        'priority' => $ticket->priority,
                        'url' => route('tickets.show', $ticket->slug),
                        'created_at' => $ticket->created_at
                    ],
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'is_new_user' => $isNewUser
                    ],
                    'category' => $category->name,
                    'tags' => ['support']
                ]
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            \Log::error('Transfer ticket failed', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create ticket: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate API key
     */
    private function validateApiKey(Request $request): bool
    {
        $apiKey = $request->header('X-API-Key') ?? $request->input('api_key');
        $validApiKey = config('app.transfer_api_key');

        if (!$validApiKey) {
            \Log::warning('Transfer API key not configured in environment');
            return false;
        }

        return hash_equals($validApiKey, $apiKey);
    }

    /**
     * Generate unique slug for ticket
     */
    private function generateUniqueSlug(string $title): string
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        while (Ticket::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Send notification email to user
     */
    private function sendNotificationEmail(User $user, Ticket $ticket, bool $isNewUser, ?string $password)
    {
        try {
            $ticketUrl = route('tickets.show', $ticket->slug);
            
            if ($isNewUser) {
                // Send welcome email with login credentials
                Mail::send('emails.new-user-ticket', [
                    'user' => $user,
                    'ticket' => $ticket,
                    'password' => $password,
                    'ticketUrl' => $ticketUrl,
                    'loginUrl' => route('login')
                ], function ($message) use ($user, $ticket) {
                    $message->to($user->email, $user->name)
                           ->subject('Welcome! Your support ticket has been created - ' . $ticket->title);
                });
            } else {
                // Send ticket notification to existing user
                Mail::send('emails.ticket-created', [
                    'user' => $user,
                    'ticket' => $ticket,
                    'ticketUrl' => $ticketUrl
                ], function ($message) use ($user, $ticket) {
                    $message->to($user->email, $user->name)
                           ->subject('Your support ticket has been created - ' . $ticket->title);
                });
            }
        } catch (\Exception $e) {
            \Log::error('Failed to send notification email', [
                'user_id' => $user->id,
                'ticket_id' => $ticket->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get API status and information
     */
    public function status(Request $request)
    {
        if (!$this->validateApiKey($request)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid API key'
            ], 401);
        }

        return response()->json([
            'success' => true,
            'message' => 'Transfer API is working',
            'version' => '1.0',
            'endpoints' => [
                'transfer' => [
                    'method' => 'POST',
                    'url' => route('api.transfer.ticket'),
                    'description' => 'Transfer ticket from external product'
                ],
                'status' => [
                    'method' => 'GET',
                    'url' => route('api.transfer.status'),
                    'description' => 'Check API status'
                ]
            ],
            'required_fields' => [
                'title' => 'string (max 255 chars)',
                'content' => 'string',
                'category' => 'string (max 100 chars)',
                'email' => 'valid email',
                'username' => 'string (max 100 chars)'
            ],
            'authentication' => 'X-API-Key header or api_key parameter'
        ]);
    }
}
