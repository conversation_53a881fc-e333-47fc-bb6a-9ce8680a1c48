<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;

class FixRoleNamesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('=== FIXING ROLE NAMES ===');
        
        // Mapping old role names to new role names
        $roleMapping = [
            'admin' => 'Admin',
            'employee' => 'Employee', 
            'customer' => 'Customer',
            'department_manager' => 'Department Manager'
        ];
        
        foreach ($roleMapping as $oldName => $newName) {
            $oldRole = Role::where('name', $oldName)->first();
            $newRole = Role::where('name', $newName)->first();
            
            if ($oldRole && $newRole) {
                $this->command->info("Moving users from '{$oldName}' to '{$newName}'");
                
                // Get all users with old role
                $users = User::role($oldName)->get();
                
                foreach ($users as $user) {
                    // Remove old role and assign new role
                    $user->removeRole($oldName);
                    $user->assignRole($newName);
                    $this->command->info("  - Updated user: {$user->name} ({$user->email})");
                }
                
                // Delete old role if it has no users
                if ($oldRole->users()->count() === 0) {
                    $oldRole->delete();
                    $this->command->info("  - Deleted old role: {$oldName}");
                }
            } elseif ($oldRole && !$newRole) {
                $this->command->warn("Old role '{$oldName}' exists but new role '{$newName}' doesn't exist");
            }
        }
        
        $this->command->info('=== CURRENT ROLES AND USERS ===');
        
        $roles = Role::with('users')->get();
        foreach ($roles as $role) {
            $this->command->info("Role: {$role->name} ({$role->users->count()} users)");
            foreach ($role->users as $user) {
                $this->command->info("  - {$user->name} ({$user->email})");
            }
        }
        
        $this->command->info('=== COMPLETED ===');
    }
}
