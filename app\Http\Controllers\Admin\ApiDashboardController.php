<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ApiAuditLog;
use App\Models\TokenAuditLog;
use App\Models\User;
use App\Services\AuditLogService;
use App\Services\TokenManagementService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Laravel\Sanctum\PersonalAccessToken;

class ApiDashboardController extends Controller
{
    use AuthorizesRequests;

    public function __construct(
        private AuditLogService $auditService,
        private TokenManagementService $tokenService
    ) {}

    /**
     * Display the API management dashboard
     */
    public function index(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $today = now()->startOfDay();
        $yesterday = now()->subDay()->startOfDay();

        // System Users Statistics
        $systemUsersStats = [
            'total' => User::systemUsers()->count(),
            'active' => User::activeSystemUsers()->count(),
            'inactive' => User::systemUsers()->where('is_active', false)->count(),
        ];

        // Token Statistics
        $tokenStats = $this->tokenService->getTokenStatistics();

        // API Requests Statistics (Today)
        $apiRequestsStats = [
            'total_today' => ApiAuditLog::where('requested_at', '>=', $today)->count(),
            'successful_today' => ApiAuditLog::where('requested_at', '>=', $today)
                ->whereBetween('response_status', [200, 299])
                ->count(),
            'failed_today' => ApiAuditLog::where('requested_at', '>=', $today)
                ->where('response_status', '>=', 400)
                ->count(),
            'success_rate' => 0,
            'avg_response_time' => ApiAuditLog::where('requested_at', '>=', $today)
                ->avg('response_time_ms') ?? 0,
        ];

        // Calculate success rate
        if ($apiRequestsStats['total_today'] > 0) {
            $apiRequestsStats['success_rate'] = round(
                ($apiRequestsStats['successful_today'] / $apiRequestsStats['total_today']) * 100,
                1
            );
        }

        // Security Statistics (Last 24 hours)
        $securityStats = [
            'alerts_count' => $this->auditService->getSecurityAlerts(24)->count(),
            'rate_limit_violations' => ApiAuditLog::where('requested_at', '>=', $yesterday)
                ->where('response_status', 429)
                ->count(),
            'auth_failures' => ApiAuditLog::where('requested_at', '>=', $yesterday)
                ->where('response_status', 401)
                ->count(),
            'suspicious_ips' => $this->getSuspiciousIpsCount($yesterday),
        ];

        // Top System Users (by requests today)
        $topUsers = User::systemUsers()
            ->select(['users.id', 'users.name', 'users.system_type'])
            ->leftJoin('api_audit_logs', 'users.id', '=', 'api_audit_logs.user_id')
            ->where('api_audit_logs.requested_at', '>=', $today)
            ->groupBy(['users.id', 'users.name', 'users.system_type'])
            ->selectRaw('COUNT(api_audit_logs.id) as requests_today')
            ->selectRaw('ROUND(AVG(CASE WHEN api_audit_logs.response_status BETWEEN 200 AND 299 THEN 100 ELSE 0 END), 1) as success_rate')
            ->orderBy('requests_today', 'desc')
            ->limit(5)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'system_type' => $user->system_type,
                    'requests_today' => $user->requests_today ?? 0,
                    'success_rate' => $user->success_rate ?? 0,
                ];
            });

        // Recent Activities (Last 50 activities)
        $recentActivities = collect();

        // Token activities
        $tokenActivities = TokenAuditLog::with(['tokenable', 'performedBy'])
            ->orderBy('created_at', 'desc')
            ->limit(25)
            ->get()
            ->map(function ($log) {
                return [
                    'id' => 'token_' . $log->id,
                    'type' => 'token_' . $log->action,
                    'message' => $this->formatTokenActivity($log),
                    'timestamp' => $log->created_at->toISOString(),
                    'severity' => $this->getTokenActivitySeverity($log->action),
                ];
            });

        // API activities (errors and important events)
        $apiActivities = ApiAuditLog::with('user')
            ->where('requested_at', '>=', $yesterday)
            ->where(function ($query) {
                $query->where('response_status', '>=', 400)
                      ->orWhere('response_status', 429);
            })
            ->orderBy('requested_at', 'desc')
            ->limit(25)
            ->get()
            ->map(function ($log) {
                return [
                    'id' => 'api_' . $log->id,
                    'type' => $this->getApiActivityType($log->response_status),
                    'message' => $this->formatApiActivity($log),
                    'timestamp' => $log->requested_at->toISOString(),
                    'severity' => $this->getApiActivitySeverity($log->response_status),
                ];
            });

        $recentActivities = $tokenActivities->concat($apiActivities)
            ->sortByDesc('timestamp')
            ->take(20)
            ->values();

        $stats = [
            'system_users' => $systemUsersStats,
            'tokens' => $tokenStats,
            'api_requests' => $apiRequestsStats,
            'security' => $securityStats,
            'top_users' => $topUsers,
            'recent_activities' => $recentActivities,
        ];

        return Inertia::render('Admin/ApiDashboard', [
            'stats' => $stats,
        ]);
    }

    /**
     * Get count of suspicious IPs
     */
    private function getSuspiciousIpsCount(\DateTime $since): int
    {
        return ApiAuditLog::where('requested_at', '>=', $since)
            ->selectRaw('ip_address, COUNT(*) as request_count, COUNT(CASE WHEN response_status >= 400 THEN 1 END) as error_count')
            ->groupBy('ip_address')
            ->havingRaw('request_count > 1000 OR error_count > 100')
            ->count();
    }

    /**
     * Format token activity message
     */
    private function formatTokenActivity(TokenAuditLog $log): string
    {
        $user = $log->tokenable->name ?? 'Unknown User';
        $performer = $log->performedBy->name ?? 'System';
        
        switch ($log->action) {
            case 'created':
                return "Token '{$log->token_name}' created for {$user} by {$performer}";
            case 'revoked':
                return "Token '{$log->token_name}' revoked for {$user} by {$performer}";
            case 'rotated':
                return "Token '{$log->token_name}' rotated for {$user} by {$performer}";
            case 'updated':
                return "Token '{$log->token_name}' updated for {$user} by {$performer}";
            default:
                return "Token '{$log->token_name}' {$log->action} for {$user}";
        }
    }

    /**
     * Format API activity message
     */
    private function formatApiActivity(ApiAuditLog $log): string
    {
        $user = $log->user->name ?? 'Anonymous';
        $endpoint = $log->endpoint;
        
        if ($log->response_status === 429) {
            return "Rate limit exceeded for {$user} on {$endpoint}";
        } elseif ($log->response_status === 401) {
            return "Authentication failed for {$endpoint}";
        } elseif ($log->response_status >= 500) {
            return "Server error ({$log->response_status}) for {$user} on {$endpoint}";
        } elseif ($log->response_status >= 400) {
            return "Client error ({$log->response_status}) for {$user} on {$endpoint}";
        }
        
        return "Request to {$endpoint} returned {$log->response_status}";
    }

    /**
     * Get token activity severity
     */
    private function getTokenActivitySeverity(string $action): string
    {
        switch ($action) {
            case 'revoked':
                return 'warning';
            case 'created':
                return 'info';
            case 'rotated':
                return 'info';
            default:
                return 'info';
        }
    }

    /**
     * Get API activity type
     */
    private function getApiActivityType(int $status): string
    {
        if ($status === 429) {
            return 'rate_limit';
        } elseif ($status === 401) {
            return 'auth_failure';
        } elseif ($status >= 500) {
            return 'server_error';
        } elseif ($status >= 400) {
            return 'client_error';
        }
        
        return 'api_request';
    }

    /**
     * Get API activity severity
     */
    private function getApiActivitySeverity(int $status): string
    {
        if ($status >= 500) {
            return 'error';
        } elseif ($status === 429 || $status === 401) {
            return 'warning';
        } elseif ($status >= 400) {
            return 'warning';
        }
        
        return 'info';
    }
}
