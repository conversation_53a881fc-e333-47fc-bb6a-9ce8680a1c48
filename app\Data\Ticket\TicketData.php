<?php

namespace App\Data\Ticket;

use Illuminate\Support\Carbon;
use <PERSON>tie\LaravelData\Data;

class TicketData extends Data
{
    public function __construct(
        public int $id,
        public string $title,
        public string $content,
        public int $upvotes_count,
        public Carbon $created_at,
        public ?string $author_name,
    ) {}

    public static function fromModel(\App\Models\Ticket $ticket): self
    {
        return new self(
            id: $ticket->id,
            title: $ticket->title,
            content: $ticket->content,
            upvotes_count: $ticket->upvotes_count,
            created_at: $ticket->created_at,
            author_name: $ticket->user?->name
        );
    }
}
