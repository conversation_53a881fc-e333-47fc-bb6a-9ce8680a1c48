<?php

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

class StoreTicketRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        // Middleware 'auth:sanctum' already handles authentication
        // Additional check: ensure user is an active system user
        return $this->user() && $this->user()->isActiveSystemUser();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['required', 'string', 'max:255'],
            'content' => ['required', 'string', 'max:65535'],
            'requester_name' => ['required', 'string', 'max:255'],
            'requester_email' => ['required', 'email', 'max:255'],
            'priority' => ['nullable', 'in:low,medium,high,urgent'],
            'category_key' => ['nullable', 'string', 'max:255'],
            'category_id' => ['nullable', 'integer', 'exists:categories,id'],
            'department_id' => ['nullable', 'integer', 'exists:departments,id'],
            'tags' => ['nullable', 'array', 'max:10'],
            'tags.*' => ['string', 'max:50'],
            'external_id' => ['nullable', 'string', 'max:255'],
            'source_system' => ['nullable', 'string', 'max:100'],
            'product_id' => ['nullable', 'string', 'max:255'],
            'product_name' => ['nullable', 'string', 'max:255'],
            'metadata' => ['nullable', 'array'],
            'attachments' => ['nullable', 'array', 'max:5'],
            'attachments.*.name' => ['required_with:attachments', 'string', 'max:255'],
            'attachments.*.url' => ['required_with:attachments', 'url', 'max:500'],
            'attachments.*.type' => ['nullable', 'string', 'max:100'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Ticket title is required.',
            'title.max' => 'Ticket title cannot exceed 255 characters.',
            'content.required' => 'Ticket content is required.',
            'content.max' => 'Ticket content cannot exceed 65535 characters.',
            'requester_name.required' => 'Requester name is required.',
            'requester_email.required' => 'Requester email is required.',
            'requester_email.email' => 'Requester email must be a valid email address.',
            'priority.in' => 'Priority must be one of: low, medium, high, urgent.',
            'tags.max' => 'Maximum 10 tags are allowed.',
            'tags.*.max' => 'Each tag cannot exceed 50 characters.',
            'external_id.max' => 'External ID cannot exceed 255 characters.',
            'source_system.max' => 'Source system cannot exceed 100 characters.',
            'attachments.max' => 'Maximum 5 attachments are allowed.',
            'attachments.*.name.required_with' => 'Attachment name is required.',
            'attachments.*.url.required_with' => 'Attachment URL is required.',
            'attachments.*.url.url' => 'Attachment URL must be a valid URL.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'requester_name' => 'requester name',
            'requester_email' => 'requester email',
            'external_id' => 'external ID',
            'source_system' => 'source system',
            'category_key' => 'category key',
            'category_id' => 'category ID',
            'department_id' => 'department ID',
            'product_id' => 'product ID',
            'product_name' => 'product name',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default source_system if not provided
        if (!$this->has('source_system')) {
            $this->merge([
                'source_system' => $this->user()->system_type ?? 'unknown'
            ]);
        }

        // Set default priority if not provided
        if (!$this->has('priority')) {
            $this->merge(['priority' => 'medium']);
        }

        // Trim whitespace from string fields
        $stringFields = ['title', 'content', 'requester_name', 'requester_email', 'external_id', 'source_system'];
        foreach ($stringFields as $field) {
            if ($this->has($field)) {
                $this->merge([$field => trim($this->input($field))]);
            }
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Custom validation: check for duplicate external_id if provided
            if ($this->filled('external_id') && $this->filled('source_system')) {
                $exists = \DB::table('ticket_external_refs')
                    ->where('source_system', $this->input('source_system'))
                    ->where('external_id', $this->input('external_id'))
                    ->exists();

                if ($exists) {
                    $validator->errors()->add('external_id', 'A ticket with this external ID already exists for this source system.');
                }
            }

            // Custom validation: ensure either category_key or category_id is provided if categorization is required
            if (!$this->filled('category_key') && !$this->filled('category_id')) {
                // This is optional - remove if categorization is not mandatory
                // $validator->errors()->add('category', 'Either category_key or category_id must be provided.');
            }
        });
    }
}
