<?php

namespace App\Http\Middleware;

use App\Models\ApiAuditLog;
use App\Services\RateLimitService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;

class ApiAuditLogger
{
    public function __construct(
        private RateLimitService $rateLimitService
    ) {}

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $startTime = microtime(true);
        $requestId = Str::uuid()->toString();
        
        // Add request ID to request for tracking
        $request->attributes->set('request_id', $requestId);

        // Process the request
        $response = $next($request);

        // Calculate response time
        $responseTime = (microtime(true) - $startTime) * 1000; // Convert to milliseconds

        // Log the API call
        $this->logApiCall($request, $response, $requestId, $responseTime);

        return $response;
    }

    /**
     * Log the API call to audit log
     */
    private function logApiCall(Request $request, Response $response, string $requestId, float $responseTime): void
    {
        try {
            $user = $request->user();
            $token = $request->bearerToken() ? $user?->currentAccessToken() : null;

            // Get rate limit info if user is a system user
            $rateLimitRemaining = null;
            $rateLimitReset = null;
            
            if ($user && $user->isSystemUser()) {
                $rateLimitStatus = $this->rateLimitService->getRateLimitStatus($user);
                $rateLimitRemaining = $rateLimitStatus['usage']['minute_remaining'] ?? null;
                $rateLimitReset = $rateLimitStatus['reset_times']['minute_reset'] ?? null;
            }

            // Prepare request headers (filter sensitive data)
            $requestHeaders = $this->filterSensitiveHeaders($request->headers->all());

            // Prepare request payload (limit size and filter sensitive data)
            $requestPayload = $this->prepareRequestPayload($request);

            // Prepare response body (limit size for large responses)
            $responseBody = $this->prepareResponseBody($response);

            // Determine error type and message for failed requests
            $errorType = null;
            $errorMessage = null;
            
            if ($response->getStatusCode() >= 400) {
                $errorType = $this->getErrorType($response->getStatusCode());
                $errorMessage = $this->extractErrorMessage($response);
            }

            ApiAuditLog::create([
                'request_id' => $requestId,
                'user_id' => $user?->id,
                'token_name' => $token?->name,
                'token_id' => $token?->id,
                'method' => $request->method(),
                'endpoint' => $request->getPathInfo(),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'request_headers' => $requestHeaders,
                'request_payload' => $requestPayload,
                'response_status' => $response->getStatusCode(),
                'response_body' => $responseBody,
                'response_time_ms' => round($responseTime, 2),
                'requested_at' => now(),
                'rate_limit_remaining' => $rateLimitRemaining,
                'rate_limit_reset_at' => $rateLimitReset ? now()->timestamp($rateLimitReset) : null,
                'error_type' => $errorType,
                'error_message' => $errorMessage,
            ]);

        } catch (\Exception $e) {
            // Log the error but don't fail the request
            \Log::error('Failed to log API audit trail', [
                'request_id' => $requestId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Filter sensitive headers
     */
    private function filterSensitiveHeaders(array $headers): array
    {
        $sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
        
        $filtered = [];
        foreach ($headers as $key => $value) {
            if (in_array(strtolower($key), $sensitiveHeaders)) {
                $filtered[$key] = ['[FILTERED]'];
            } else {
                $filtered[$key] = $value;
            }
        }
        
        return $filtered;
    }

    /**
     * Prepare request payload for logging
     */
    private function prepareRequestPayload(Request $request): ?string
    {
        $content = $request->getContent();
        
        if (empty($content)) {
            return null;
        }

        // Limit payload size (max 64KB)
        if (strlen($content) > 65536) {
            $content = substr($content, 0, 65536) . '... [TRUNCATED]';
        }

        // Try to parse as JSON and filter sensitive fields
        try {
            $data = json_decode($content, true);
            if (is_array($data)) {
                $data = $this->filterSensitiveData($data);
                return json_encode($data);
            }
        } catch (\Exception $e) {
            // Not JSON, return as is
        }

        return $content;
    }

    /**
     * Prepare response body for logging
     */
    private function prepareResponseBody(Response $response): ?string
    {
        $content = $response->getContent();
        
        if (empty($content)) {
            return null;
        }

        // Limit response size (max 32KB for responses)
        if (strlen($content) > 32768) {
            $content = substr($content, 0, 32768) . '... [TRUNCATED]';
        }

        return $content;
    }

    /**
     * Filter sensitive data from arrays
     */
    private function filterSensitiveData(array $data): array
    {
        $sensitiveFields = ['password', 'token', 'secret', 'key', 'authorization'];
        
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->filterSensitiveData($value);
            } elseif (in_array(strtolower($key), $sensitiveFields)) {
                $data[$key] = '[FILTERED]';
            }
        }
        
        return $data;
    }

    /**
     * Get error type based on status code
     */
    private function getErrorType(int $statusCode): string
    {
        return match (true) {
            $statusCode >= 500 => 'server_error',
            $statusCode === 429 => 'rate_limit_exceeded',
            $statusCode === 401 => 'unauthorized',
            $statusCode === 403 => 'forbidden',
            $statusCode === 404 => 'not_found',
            $statusCode === 422 => 'validation_error',
            $statusCode >= 400 => 'client_error',
            default => 'unknown',
        };
    }

    /**
     * Extract error message from response
     */
    private function extractErrorMessage(Response $response): ?string
    {
        try {
            $content = $response->getContent();
            $data = json_decode($content, true);
            
            if (is_array($data)) {
                return $data['message'] ?? $data['error'] ?? null;
            }
        } catch (\Exception $e) {
            // Ignore JSON parsing errors
        }
        
        return null;
    }
}
