<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            // Check if columns already exist before adding them
            if (!Schema::hasColumn('personal_access_tokens', 'expires_at')) {
                $table->timestamp('expires_at')->nullable()->after('last_used_at');
            }
            if (!Schema::hasColumn('personal_access_tokens', 'is_active')) {
                $table->boolean('is_active')->default(true)->after('expires_at');
            }
            if (!Schema::hasColumn('personal_access_tokens', 'token_type')) {
                $table->string('token_type')->default('api')->after('is_active')->comment('api, webhook, etc.');
            }
            if (!Schema::hasColumn('personal_access_tokens', 'description')) {
                $table->text('description')->nullable()->after('token_type');
            }

            // Usage tracking
            if (!Schema::hasColumn('personal_access_tokens', 'usage_count')) {
                $table->integer('usage_count')->default(0)->after('description');
            }
            if (!Schema::hasColumn('personal_access_tokens', 'last_activity_at')) {
                $table->timestamp('last_activity_at')->nullable()->after('usage_count');
            }
            if (!Schema::hasColumn('personal_access_tokens', 'usage_stats')) {
                $table->json('usage_stats')->nullable()->after('last_activity_at')->comment('Daily/hourly usage statistics');
            }

            // Security fields
            if (!Schema::hasColumn('personal_access_tokens', 'created_by_ip')) {
                $table->string('created_by_ip')->nullable()->after('usage_stats');
            }
            if (!Schema::hasColumn('personal_access_tokens', 'allowed_ips')) {
                $table->json('allowed_ips')->nullable()->after('created_by_ip');
            }
            if (!Schema::hasColumn('personal_access_tokens', 'rotated_at')) {
                $table->timestamp('rotated_at')->nullable()->after('allowed_ips');
            }
            if (!Schema::hasColumn('personal_access_tokens', 'rotation_reason')) {
                $table->string('rotation_reason')->nullable()->after('rotated_at');
            }
        });

        // Add indexes separately to avoid conflicts
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            // Check if indexes don't already exist
            $indexes = collect(DB::select("SHOW INDEX FROM personal_access_tokens"))->pluck('Key_name');

            if (!$indexes->contains('personal_access_tokens_tokenable_id_is_active_index')) {
                $table->index(['tokenable_id', 'is_active']);
            }
            if (!$indexes->contains('personal_access_tokens_expires_at_is_active_index')) {
                $table->index(['expires_at', 'is_active']);
            }
            if (!$indexes->contains('personal_access_tokens_token_type_index')) {
                $table->index('token_type');
            }
            if (!$indexes->contains('personal_access_tokens_last_activity_at_index')) {
                $table->index('last_activity_at');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->dropIndex(['tokenable_id', 'is_active']);
            $table->dropIndex(['expires_at', 'is_active']);
            $table->dropIndex('token_type');
            $table->dropIndex('last_activity_at');
            
            $table->dropColumn([
                'expires_at',
                'is_active',
                'token_type',
                'description',
                'usage_count',
                'last_activity_at',
                'usage_stats',
                'created_by_ip',
                'allowed_ips',
                'rotated_at',
                'rotation_reason'
            ]);
        });
    }
};
