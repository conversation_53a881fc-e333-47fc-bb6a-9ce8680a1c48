<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class FixAdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('=== FIXING ADMIN USER ===');
        
        // Tạo hoặc cập nhật admin user
        $adminUser = User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );

        // Đảm bảo có role Admin
        $adminRole = Role::where('name', 'Admin')->first();
        if (!$adminRole) {
            $this->command->error('Admin role not found! Run RolesAndPermissionsSeeder first.');
            return;
        }

        // <PERSON><PERSON>a t<PERSON>t cả roles cũ và gán role Admin
        $adminUser->syncRoles(['Admin']);

        $this->command->info('✓ Admin user fixed successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: password123');
        $this->command->info('Role: ' . $adminUser->roles->pluck('name')->join(', '));
        
        // Test permissions
        $this->command->info('=== TESTING PERMISSIONS ===');
        $testPermissions = [
            'access-admin-dashboard',
            'view-any-posts',
            'manage-users'
        ];
        
        foreach ($testPermissions as $permission) {
            $hasPermission = $adminUser->can($permission);
            $this->command->info($permission . ': ' . ($hasPermission ? '✓' : '✗'));
        }
        
        $this->command->info('=== READY TO TEST ===');
        $this->command->info('1. <NAME_EMAIL> / password123');
        $this->command->info('2. Go to /admin');
        $this->command->info('3. Should work now!');
    }
}
