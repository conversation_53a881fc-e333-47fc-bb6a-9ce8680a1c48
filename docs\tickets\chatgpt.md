# Tài liệu triển khai: API tiếp nhận Ticket từ hệ thống ngoài (Laravel)

> <PERSON><PERSON><PERSON> tiêu: cung cấp một hướng dẫn **đơn giản – rõ ràng – có thể triển khai ngay** để nhận Ticket từ hệ thống bên ngoài qua API, kèm các lựa chọn cấu hình tối thiểu để mở rộng khi cần.

---

## 0) Triết lý thiết kế

* **Đơn giản mặc định (Simple by default)**: 1 endpoint POST, Bearer <PERSON>, payload JSON tối giản.
* **An toàn hợp lý**: Sanctum token, rate limit, logging, optional IP whitelist.
* **Có lộ trình mở rộng**: mapping category/tag theo `category_key`, `tags`, tránh duplicate bằng `external_id` + `source_system`.

---

## 1) Phạm vi

* Backend: Lara<PERSON> 10/11.
* Chức năng: Nhận ticket từ hệ thống ngoài; auto-provision user (tạo tài khoản & gửi email mời) **hoặc** gắn về service account; mapping danh mục/tag tùy chọn; tránh trùng; gửi thông báo nội bộ.

---

## 2) Kiến trúc tổng quát (logic)

1. Client ngoài gọi `POST /api/v1/tickets` kèm Bearer Token.
2. API xác thực token → áp dụng rate limit, (tùy chọn) IP whitelist.
3. Validate payload → chuẩn hóa dữ liệu.
4. Tìm user theo `requester_email`.

   * Tồn tại → gán ticket cho user này.
   * Không tồn tại → (a) nếu bật **auto-provision** → tạo user + gửi email mời (reset password), hoặc (b) gán về **service account** và lưu requester vào metadata.
5. Mapping `category_key` → `category_id` (nếu cung cấp).
6. Dedupe theo `external_id` + `source_system` (nếu cung cấp).
7. Tạo ticket + gán tags.
8. Lưu `payload` gốc để debug.
9. Gửi thông báo cho admin/nhóm CS.
10. Trả về `201 Created` với `ticket_id`, `ticket_url`.

---

## 3) Chuẩn bị môi trường

* PHP 8.2+; MySQL 8+ hoặc PostgreSQL 13+.
* Cấu hình mail (SMTP) để gửi thư mời/notification.
* Cấu hình queue (khuyến khích): Redis + Horizon hoặc database queue.

### Cài Sanctum

```bash
composer require laravel/sanctum
php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
php artisan migrate
```

> Sanctum tạo sẵn bảng `personal_access_tokens`. Ta sẽ dùng bảng này để lưu API token.

---

## 4) Thiết kế dữ liệu (migrations)

> **Ghi chú**: Tùy hệ thống hiện có, bạn có thể map sang bảng `posts` sẵn có. Tài liệu này dùng bảng `tickets` cho rõ ràng. Nếu đang dùng `posts`, đổi tên model/column tương ứng.

### 4.1) Bảng `tickets`

```php
// database/migrations/2025_08_15_000001_create_tickets_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->longText('content');
            $table->enum('priority', ['low','medium','high','urgent'])->default('medium');
            $table->unsignedBigInteger('category_id')->nullable();
            $table->foreignId('user_id')->nullable()->constrained('users')->nullOnDelete();
            // Lưu thông tin requester để tra cứu nhanh
            $table->string('requester_name');
            $table->string('requester_email');
            // Lưu payload gốc để debug
            $table->json('payload')->nullable();
            $table->timestamps();
        });
    }
    public function down(): void {
        Schema::dropIfExists('tickets');
    }
};
```

### 4.2) Bảng chống trùng `ticket_external_refs`

```php
// database/migrations/2025_08_15_000002_create_ticket_external_refs_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('ticket_external_refs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ticket_id')->constrained('tickets')->cascadeOnDelete();
            $table->string('source_system');
            $table->string('external_id');
            $table->timestamps();
            $table->unique(['source_system','external_id']);
        });
    }
    public function down(): void {
        Schema::dropIfExists('ticket_external_refs');
    }
};
```

### 4.3) Mapping danh mục & tag (tùy chọn)

```php
// database/migrations/2025_08_15_000003_create_integration_mappings_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::create('integration_mappings', function (Blueprint $table) {
            $table->id();
            $table->string('source_system');
            $table->string('external_key'); // category_key hoặc tag text
            $table->unsignedBigInteger('internal_id')->nullable(); // id của category hoặc tag
            $table->enum('type', ['category','tag']);
            $table->timestamps();
            $table->unique(['source_system','external_key','type']);
        });
    }
    public function down(): void {
        Schema::dropIfExists('integration_mappings');
    }
};
```

### 4.4) Bảng `categories`, `tags`, pivot (nếu chưa có)

```php
// categories
Schema::create('categories', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('slug')->unique();
    $table->timestamps();
});

// tags
Schema::create('tags', function (Blueprint $table) {
    $table->id();
    $table->string('name')->unique();
    $table->timestamps();
});

// ticket_tag pivot
Schema::create('tag_ticket', function (Blueprint $table) {
    $table->foreignId('tag_id')->constrained('tags')->cascadeOnDelete();
    $table->foreignId('ticket_id')->constrained('tickets')->cascadeOnDelete();
    $table->primary(['tag_id','ticket_id']);
});
```

### 4.5) Bổ sung trường `status` cho users (tùy chọn)

```php
// database/migrations/2025_08_15_000004_add_status_to_users_table.php
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void {
        Schema::table('users', function (Blueprint $table) {
            $table->enum('status', ['active','invited','provisioned'])
                  ->default('provisioned')->after('password');
        });
    }
    public function down(): void {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
};
```

---

## 5) Cấu hình (config/tickets.php)

```php
// config/tickets.php
return [
    // Tự tạo user mới nếu requester_email chưa tồn tại
    'auto_provision_users' => true,

    // Gửi email mời (reset password) khi tạo user mới
    'invite_new_user' => true,

    // Nếu không auto_provision_users: gán ticket về user này
    'service_account_user_id' => env('TICKETS_SERVICE_ACCOUNT_ID'),

    // Cho phép tạo tag mới nếu tag chưa tồn tại
    'create_unknown_tags' => true,

    // Mapping category theo integration_mappings
    'enable_category_mapping' => true,

    // Nguồn mặc định nếu client không gửi
    'default_source_system' => 'external',

    // IP whitelist theo token (tùy chọn) – lưu ở DB nếu muốn tinh vi hơn
    'ip_whitelist' => env('TICKETS_IP_WHITELIST', ''), // "*******,*******"
];
```

> Đăng ký vào `config/app.php` phần `providers` nếu cần, và nhớ `php artisan config:cache`.

---

## 6) Authentication & Rate Limiting

* Sử dụng Sanctum Bearer Token: tạo token qua giao diện admin hoặc artisan.
* Middleware: `auth:sanctum` và `throttle:60,1` (mặc định 60 req/phút).
* (Tùy chọn) IP whitelist middleware.

### Tạo token mẫu bằng Artisan (ví dụ)

```php
// app/Console/Commands/IssueApiToken.php
namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;

class IssueApiToken extends Command {
    protected $signature = 'api:issue-token {user_id} {name}';
    protected $description = 'Issue Sanctum token for external system';

    public function handle() {
        $user = User::findOrFail((int)$this->argument('user_id'));
        $token = $user->createToken($this->argument('name'));
        $this->info('Token: '.$token->plainTextToken);
        return self::SUCCESS;
    }
}
```

---

## 7) API Specification

### Endpoint

```
POST /api/v1/tickets
Authorization: Bearer <API_TOKEN>
Content-Type: application/json
X-Source-System: <optional string, ví dụ: "crm", "monitoring">
```

### Request Body

```json
{
  "title": "string (required)",
  "content": "string (required)",
  "requester_name": "string (required)",
  "requester_email": "string (required, email)",
  "priority": "low | medium | high | urgent (optional, default medium)",
  "category_key": "string (optional)",
  "tags": ["string", "string"],
  "external_id": "string (optional)"
}
```

### Responses

**201 Created**

```json
{
  "message": "Ticket created successfully.",
  "ticket_id": 123,
  "ticket_url": "/tickets/123"
}
```

**400 Bad Request**

```json
{
  "message": "Invalid data.",
  "errors": {"title": ["The title field is required."]}
}
```

**401 Unauthorized**

```json
{"message": "Unauthenticated."}
```

**409 Conflict** (duplicate external\_id)

```json
{
  "message": "Duplicate ticket.",
  "ticket_id": 123,
  "ticket_url": "/tickets/123"
}
```

**429 Too Many Requests** (rate limit)

---

## 8) Validation (Form Request)

```php
// app/Http/Requests/Api/V1/StoreTicketRequest.php
namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

class StoreTicketRequest extends FormRequest
{
    public function authorize(): bool { return true; }

    public function rules(): array {
        return [
            'title' => ['required','string','max:255'],
            'content' => ['required','string'],
            'requester_name' => ['required','string','max:255'],
            'requester_email' => ['required','email','max:255'],
            'priority' => ['nullable','in:low,medium,high,urgent'],
            'category_key' => ['nullable','string','max:255'],
            'tags' => ['nullable','array'],
            'tags.*' => ['string','max:50'],
            'external_id' => ['nullable','string','max:255'],
        ];
    }
}
```

---

## 9) Route & Middleware

```php
// routes/api.php
use App\Http\Controllers\Api\V1\TicketController;

Route::middleware(['auth:sanctum','throttle:60,1'])
    ->prefix('v1')
    ->group(function () {
        Route::post('/tickets', [TicketController::class, 'store']);
    });
```

(Optional) IP whitelist middleware

```php
// app/Http/Middleware/IpWhitelist.php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class IpWhitelist
{
    public function handle(Request $request, Closure $next)
    {
        $list = config('tickets.ip_whitelist');
        if (!$list) return $next($request);
        $allowed = collect(explode(',', $list))->map(fn($i)=>trim($i))->filter();
        if ($allowed->isEmpty() || $allowed->contains($request->ip())) {
            return $next($request);
        }
        return response()->json(['message' => 'Forbidden'], 403);
    }
}
```

Đăng ký middleware vào `app/Http/Kernel.php` nếu dùng.

---

## 10) Service xử lý (khuyến khích)

```php
// app/Services/TicketIntakeService.php
namespace App\Services;

use App\Models\{Ticket, User, Category, Tag};
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;

class TicketIntakeService
{
    public function createFromExternal(array $data, string $sourceSystem): array
    {
        return DB::transaction(function () use ($data, $sourceSystem) {
            // 1) User
            $user = User::where('email', $data['requester_email'])->first();
            if (!$user) {
                if (config('tickets.auto_provision_users')) {
                    $user = User::create([
                        'name' => $data['requester_name'],
                        'email' => $data['requester_email'],
                        'password' => Hash::make(Str::random(32)),
                        'status' => 'invited',
                    ]);
                    if (config('tickets.invite_new_user')) {
                        // Gửi email đặt lại mật khẩu chuẩn của Laravel
                        Password::sendResetLink(['email' => $user->email]);
                    }
                } else {
                    $user = User::find(config('tickets.service_account_user_id'));
                }
            }

            // 2) Category mapping
            $categoryId = null;
            if (!empty($data['category_key']) && config('tickets.enable_category_mapping')) {
                $map = DB::table('integration_mappings')
                    ->where(['source_system' => $sourceSystem, 'external_key' => $data['category_key'], 'type' => 'category'])
                    ->first();
                if ($map) $categoryId = (int) $map->internal_id;
            }

            // 3) Dedupe theo external_id
            if (!empty($data['external_id'])) {
                $ref = DB::table('ticket_external_refs')
                    ->where('source_system', $sourceSystem)
                    ->where('external_id', $data['external_id'])
                    ->first();
                if ($ref) {
                    return [
                        'duplicate' => true,
                        'ticket_id' => (int) $ref->ticket_id,
                    ];
                }
            }

            // 4) Tạo ticket
            $ticket = Ticket::create([
                'title' => $data['title'],
                'content' => $data['content'],
                'priority' => $data['priority'] ?? 'medium',
                'category_id' => $categoryId,
                'user_id' => $user?->id,
                'requester_name' => $data['requester_name'],
                'requester_email' => $data['requester_email'],
                'payload' => $data,
            ]);

            // 5) Gắn tags
            if (!empty($data['tags']) && is_array($data['tags'])) {
                $tagIds = [];
                foreach ($data['tags'] as $name) {
                    $name = trim($name);
                    if ($name === '') continue;
                    $tag = Tag::where('name', $name)->first();
                    if (!$tag && config('tickets.create_unknown_tags')) {
                        $tag = Tag::create(['name' => $name]);
                    }
                    if ($tag) $tagIds[] = $tag->id;
                }
                if ($tagIds) $ticket->tags()->sync($tagIds, false);
            }

            // 6) Lưu external ref
            if (!empty($data['external_id'])) {
                DB::table('ticket_external_refs')->insert([
                    'ticket_id' => $ticket->id,
                    'source_system' => $sourceSystem,
                    'external_id' => $data['external_id'],
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }

            return [
                'duplicate' => false,
                'ticket_id' => $ticket->id,
            ];
        });
    }
}
```

---

## 11) Controller

```php
// app/Http/Controllers/Api/V1/TicketController.php
namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\StoreTicketRequest;
use App\Services\TicketIntakeService;
use Illuminate\Http\JsonResponse;

class TicketController extends Controller
{
    public function __construct(private TicketIntakeService $service) {}

    public function store(StoreTicketRequest $request): JsonResponse
    {
        $source = $request->header('X-Source-System', config('tickets.default_source_system'));
        $result = $this->service->createFromExternal($request->validated(), $source);

        if ($result['duplicate'] ?? false) {
            return response()->json([
                'message' => 'Duplicate ticket.',
                'ticket_id' => $result['ticket_id'],
                'ticket_url' => url('/tickets/'.$result['ticket_id'])
            ], 409);
        }

        return response()->json([
            'message' => 'Ticket created successfully.',
            'ticket_id' => $result['ticket_id'],
            'ticket_url' => url('/tickets/'.$result['ticket_id'])
        ], 201);
    }
}
```

---

## 12) Model quan hệ tối thiểu

```php
// app/Models/Ticket.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Ticket extends Model
{
    protected $fillable = [
        'title','content','priority','category_id','user_id','requester_name','requester_email','payload'
    ];

    protected $casts = [
        'payload' => 'array',
    ];

    public function user(){ return $this->belongsTo(User::class); }
    public function category(){ return $this->belongsTo(Category::class); }
    public function tags(){ return $this->belongsToMany(Tag::class); }
}
```

---

## 13) Thông báo (Notifications)

Ví dụ gửi thông báo tới admin khi có ticket mới:

```php
// app/Notifications/NewTicketNotification.php
namespace App\Notifications;

use App\Models\Ticket;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Messages\MailMessage;

class NewTicketNotification extends Notification
{
    use Queueable;

    public function __construct(public Ticket $ticket) {}

    public function via($notifiable) { return ['mail']; }

    public function toMail($notifiable) {
        return (new MailMessage)
            ->subject('New Ticket: '.$this->ticket->title)
            ->line('Priority: '.$this->ticket->priority)
            ->line('From: '.$this->ticket->requester_name.' <'.$this->ticket->requester_email.'>')
            ->action('View Ticket', url('/tickets/'.$this->ticket->id));
    }
}
```

Gọi notification sau khi tạo ticket (trong Service hoặc Listener) tới role admin hoặc một email group.

---

## 14) Tài liệu & Hỗ trợ tích hợp

* **Swagger/OpenAPI** (khuyến khích): dùng `darkaonline/l5-swagger` để sinh docs.
* **Endpoints hỗ trợ tra cứu** (tùy chọn):

  * `GET /api/v1/categories` – trả về danh sách categories nội bộ
  * `GET /api/v1/tags` – trả về danh sách tags nội bộ
* **Postman collection** (ví dụ tối thiểu ở cuối).

---

## 15) Kiểm thử (Feature Tests)

```php
// tests/Feature/Api/TicketIntakeTest.php
use Tests\TestCase;
use App\Models\User;
use Laravel\Sanctum\Sanctum;

it('creates ticket successfully', function(){
    $client = User::factory()->create();
    $token = $client->createToken('crm')->plainTextToken;

    $res = $this->withHeader('Authorization', 'Bearer '.$token)
        ->withHeader('X-Source-System','crm')
        ->postJson('/api/v1/tickets', [
            'title' => 'A',
            'content' => 'B',
            'requester_name' => 'John',
            'requester_email' => '<EMAIL>'
        ]);

    $res->assertCreated()->assertJsonStructure(['ticket_id','ticket_url']);
});
```

---

## 16) Triển khai (Deployment checklist)

* [ ] `.env` cấu hình MAIL\_\*, QUEUE\_CONNECTION, SANCTUM\_STATEFUL\_DOMAINS (nếu cần), `TICKETS_SERVICE_ACCOUNT_ID`, `TICKETS_IP_WHITELIST`.
* [ ] Migrate DB.
* [ ] Cấp token cho hệ thống ngoài (qua giao diện hoặc artisan).
* [ ] Bật queue & Horizon (khuyến khích) để gửi mail/notification.
* [ ] Thiết lập log & giám sát (Sentry, Bugsnag…).
* [ ] Viết tài liệu ngắn cho đối tác tích hợp (endpoint, ví dụ cURL, mã lỗi).

---

## 17) Ví dụ cURL

```bash
curl -X POST https://your-domain.com/api/v1/tickets \
 -H "Authorization: Bearer <API_TOKEN>" \
 -H "Content-Type: application/json" \
 -H "X-Source-System: crm" \
 -d '{
  "title": "Website form: cannot login",
  "content": "User cannot login after password reset.",
  "requester_name": "Alice",
  "requester_email": "<EMAIL>",
  "priority": "high",
  "category_key": "account",
  "tags": ["login","web"],
  "external_id": "crm-12345"
 }'
```

---

## 18) Postman Collection (ví dụ tối thiểu)

```json
{
  "info": {"name": "Ticket Intake API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"},
  "item": [
    {
      "name": "Create Ticket",
      "request": {
        "method": "POST",
        "header": [
          {"key": "Authorization", "value": "Bearer {{API_TOKEN}}"},
          {"key": "Content-Type", "value": "application/json"},
          {"key": "X-Source-System", "value": "crm"}
        ],
        "url": {"raw": "{{BASE_URL}}/api/v1/tickets", "host": ["{{BASE_URL}}"], "path": ["api","v1","tickets"]},
        "body": {
          "mode": "raw",
          "raw": "{\n  \"title\": \"Website form: cannot login\",\n  \"content\": \"User cannot login after password reset.\",\n  \"requester_name\": \"Alice\",\n  \"requester_email\": \"<EMAIL>\",\n  \"priority\": \"high\",\n  \"category_key\": \"account\",\n  \"tags\": [\"login\",\"web\"],\n  \"external_id\": \"crm-12345\"\n}"
        }
      }
    }
  ]
}
```

---

## 19) Ghi chú vận hành & bảo mật

* **Luân phiên token**: thu hồi và cấp mới định kỳ; log `last_used_at` từ Sanctum.
* **Rate limit theo token** (nếu cần tinh chỉnh): viết RateLimiter dựa vào `$request->user()->id`.
* **IP whitelist** theo token: có thể lưu IPs trong bảng riêng liên kết `personal_access_tokens`.
* **Quan sát & cảnh báo**: cảnh báo khi tỉ lệ lỗi 4xx/5xx tăng, spike request, hoặc số ticket/ngày bất thường.

---

## 20) Lộ trình mở rộng (tùy chọn)

* **Webhook** về hệ thống ngoài khi ticket thay đổi trạng thái.
* **Attachment upload**: endpoint tải file đính kèm (kèm virus scan).
* **SLA & phân luồng**: tự động gán nhóm/agent theo category/priority.
* **Multi-tenant**: phân lập token theo tenant.

---

### Kết thúc

Bộ tài liệu trên đủ để dev triển khai từ A→Z theo nguyên tắc **đơn giản mặc định** và có sẵn các hook để mở rộng khi cần. Hãy tùy biến tên bảng/model để khớp với hệ thống hiện có (ví dụ đang dùng `posts` thay `tickets`).
