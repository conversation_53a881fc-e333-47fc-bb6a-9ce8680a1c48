<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class UserPolicyTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Tạo permissions
        Permission::create(['name' => 'view-users']);
        Permission::create(['name' => 'update-users']);
        Permission::create(['name' => 'delete-users']);
        Permission::create(['name' => 'assign-roles']);

        // Tạo roles
        $superAdmin = Role::create(['name' => 'Super Admin']);
        $admin = Role::create(['name' => 'Admin']);
        $customer = Role::create(['name' => 'Customer']);

        // <PERSON><PERSON> permissions cho roles
        $superAdmin->givePermissionTo(Permission::all());
        $admin->givePermissionTo(['view-users', 'update-users', 'delete-users', 'assign-roles']);
        $customer->givePermissionTo([]);
    }

    public function test_admin_can_view_any_users()
    {
        $admin = User::factory()->create();
        $admin->assignRole('Admin');
        
        $this->assertTrue($this->app['gate']->forUser($admin)->allows('viewAny', User::class));
    }

    public function test_customer_cannot_view_any_users()
    {
        $customer = User::factory()->create();
        $customer->assignRole('Customer');
        
        $this->assertFalse($this->app['gate']->forUser($customer)->allows('viewAny', User::class));
    }

    public function test_user_can_view_own_profile()
    {
        $user = User::factory()->create();
        $user->assignRole('Customer');
        
        $this->assertTrue($this->app['gate']->forUser($user)->allows('view', $user));
    }

    public function test_admin_can_view_other_users()
    {
        $admin = User::factory()->create();
        $admin->assignRole('Admin');
        
        $otherUser = User::factory()->create();
        
        $this->assertTrue($this->app['gate']->forUser($admin)->allows('view', $otherUser));
    }

    public function test_customer_cannot_view_other_users()
    {
        $customer = User::factory()->create();
        $customer->assignRole('Customer');
        
        $otherUser = User::factory()->create();
        
        $this->assertFalse($this->app['gate']->forUser($customer)->allows('view', $otherUser));
    }

    public function test_user_can_update_own_profile()
    {
        $user = User::factory()->create();
        $user->assignRole('Customer');
        
        $this->assertTrue($this->app['gate']->forUser($user)->allows('update', $user));
    }

    public function test_admin_can_update_other_users()
    {
        $admin = User::factory()->create();
        $admin->assignRole('Admin');
        
        $otherUser = User::factory()->create();
        
        $this->assertTrue($this->app['gate']->forUser($admin)->allows('update', $otherUser));
    }

    public function test_customer_cannot_update_other_users()
    {
        $customer = User::factory()->create();
        $customer->assignRole('Customer');
        
        $otherUser = User::factory()->create();
        
        $this->assertFalse($this->app['gate']->forUser($customer)->allows('update', $otherUser));
    }

    public function test_user_cannot_delete_themselves()
    {
        $user = User::factory()->create();
        $user->assignRole('Admin');
        
        $this->assertFalse($this->app['gate']->forUser($user)->allows('delete', $user));
    }

    public function test_admin_can_delete_other_users()
    {
        $admin = User::factory()->create();
        $admin->assignRole('Admin');
        
        $otherUser = User::factory()->create();
        $otherUser->assignRole('Customer');
        
        $this->assertTrue($this->app['gate']->forUser($admin)->allows('delete', $otherUser));
    }

    public function test_super_admin_cannot_be_deleted()
    {
        $admin = User::factory()->create();
        $admin->assignRole('Admin');
        
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('Super Admin');
        
        $this->assertFalse($this->app['gate']->forUser($admin)->allows('delete', $superAdmin));
    }

    public function test_user_cannot_assign_roles_to_themselves()
    {
        $admin = User::factory()->create();
        $admin->assignRole('Admin');
        
        $this->assertFalse($this->app['gate']->forUser($admin)->allows('assignRoles', $admin));
    }

    public function test_admin_can_assign_roles_to_other_users()
    {
        $admin = User::factory()->create();
        $admin->assignRole('Admin');
        
        $otherUser = User::factory()->create();
        $otherUser->assignRole('Customer');
        
        $this->assertTrue($this->app['gate']->forUser($admin)->allows('assignRoles', $otherUser));
    }

    public function test_super_admin_roles_can_only_be_managed_by_super_admin()
    {
        $admin = User::factory()->create();
        $admin->assignRole('Admin');
        
        $superAdmin = User::factory()->create();
        $superAdmin->assignRole('Super Admin');
        
        $this->assertFalse($this->app['gate']->forUser($admin)->allows('assignRoles', $superAdmin));
        
        $anotherSuperAdmin = User::factory()->create();
        $anotherSuperAdmin->assignRole('Super Admin');
        
        $this->assertTrue($this->app['gate']->forUser($anotherSuperAdmin)->allows('assignRoles', $superAdmin));
    }
}
