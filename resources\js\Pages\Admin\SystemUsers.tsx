import React, { useState } from 'react';
import { Head, Link, router, useForm } from '@inertiajs/react';

import { route } from 'ziggy-js';

import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Badge } from '@/Components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/Components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Textarea } from '@/Components/ui/textarea';
import { Switch } from '@/Components/ui/switch';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/Components/ui/table';
import { Trash2, Edit, Plus, Eye, Power, Shield, Activity } from 'lucide-react';
import { toast } from 'sonner';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';

interface SystemUser {
    id: number;
    name: string;
    email: string;
    system_type: string;
    system_description?: string;
    rate_limit_tier: string;
    rate_limit_per_minute?: number;
    rate_limit_burst?: number;
    allowed_ips?: string[];
    is_active: boolean;
    last_api_call_at?: string;
    tokens_count: number;
    created_tickets_count: number;
    created_at: string;
}

interface Props {
    systemUsers: {
        data: SystemUser[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    filters: {
        search?: string;
        per_page: number;
    };
}

export default function SystemUsers({ systemUsers, filters }: Props) {
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [editingUser, setEditingUser] = useState<SystemUser | null>(null);
    const [viewingUser, setViewingUser] = useState<SystemUser | null>(null);

    const { data, setData, post, put, delete: destroy, processing, errors, reset } = useForm({
        name: '',
        email: '',
        system_type: '',
        system_description: '',
        rate_limit_tier: 'standard',
        rate_limit_per_minute: '',
        rate_limit_burst: '',
        allowed_ips: [] as string[],
        is_active: true,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        if (editingUser) {
            put(route('admin.system-users.update', editingUser.id), {
                onSuccess: () => {
                    setEditingUser(null);
                    reset();
                    toast.success('System user updated successfully');
                },
                onError: () => toast.error('Failed to update system user'),
            });
        } else {
            post(route('admin.system-users.store'), {
                onSuccess: () => {
                    setShowCreateModal(false);
                    reset();
                    toast.success('System user created successfully');
                },
                onError: () => toast.error('Failed to create system user'),
            });
        }
    };

    const handleDelete = (user: SystemUser) => {
        if (confirm(`Are you sure you want to delete ${user.name}? This will also revoke all their tokens.`)) {
            destroy(route('admin.system-users.destroy', user.id), {
                onSuccess: () => toast.success('System user deleted successfully'),
                onError: () => toast.error('Failed to delete system user'),
            });
        }
    };

    const handleToggleStatus = (user: SystemUser) => {
        post(route('admin.system-users.toggle-status', user.id), {
            onSuccess: () => {
                const status = user.is_active ? 'deactivated' : 'activated';
                toast.success(`System user ${status} successfully`);
            },
            onError: () => toast.error('Failed to toggle user status'),
        });
    };

    const openEditModal = (user: SystemUser) => {
        setData({
            name: user.name,
            email: user.email,
            system_type: user.system_type,
            system_description: user.system_description || '',
            rate_limit_tier: user.rate_limit_tier,
            rate_limit_per_minute: user.rate_limit_per_minute?.toString() || '',
            rate_limit_burst: user.rate_limit_burst?.toString() || '',
            allowed_ips: user.allowed_ips || [],
            is_active: user.is_active,
        });
        setEditingUser(user);
    };

    const getRateLimitInfo = (user: SystemUser) => {
        const tierColors = {
            standard: 'bg-blue-100 text-blue-800',
            premium: 'bg-purple-100 text-purple-800',
            enterprise: 'bg-gold-100 text-gold-800',
        };
        
        return (
            <div className="space-y-1">
                <Badge className={tierColors[user.rate_limit_tier as keyof typeof tierColors]}>
                    {user.rate_limit_tier}
                </Badge>
                <div className="text-xs text-gray-500">
                    {user.rate_limit_per_minute || 60}/min, burst: {user.rate_limit_burst || 10}
                </div>
            </div>
        );
    };

    return (
        <AuthenticatedLayout
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl  leading-tight">
                        System Users Management
                    </h2>
                    <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
                        <DialogTrigger asChild>
                            <Button>
                                <Plus className="w-4 h-4 mr-2" />
                                Create System User
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                            <DialogHeader>
                                <DialogTitle>Create New System User</DialogTitle>
                                <DialogDescription>
                                    Create a new system user for external API access.
                                </DialogDescription>
                            </DialogHeader>
                            <form onSubmit={handleSubmit} className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="name">Name</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="Payment System"
                                        />
                                        {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                                    </div>
                                    <div>
                                        <Label htmlFor="email">Email</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            placeholder="<EMAIL>"
                                        />
                                        {errors.email && <p className="text-sm text-red-600">{errors.email}</p>}
                                    </div>
                                </div>
                                
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="system_type">System Type</Label>
                                        <Input
                                            id="system_type"
                                            value={data.system_type}
                                            onChange={(e) => setData('system_type', e.target.value)}
                                            placeholder="payment, crm, monitoring"
                                        />
                                        {errors.system_type && <p className="text-sm text-red-600">{errors.system_type}</p>}
                                    </div>
                                    <div>
                                        <Label htmlFor="rate_limit_tier">Rate Limit Tier</Label>
                                        <Select value={data.rate_limit_tier} onValueChange={(value) => setData('rate_limit_tier', value)}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="standard">Standard (60/min)</SelectItem>
                                                <SelectItem value="premium">Premium (120/min)</SelectItem>
                                                <SelectItem value="enterprise">Enterprise (300/min)</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="system_description">Description</Label>
                                    <Textarea
                                        id="system_description"
                                        value={data.system_description}
                                        onChange={(e) => setData('system_description', e.target.value)}
                                        placeholder="Description of the system and its purpose"
                                    />
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="rate_limit_per_minute">Custom Rate Limit (per minute)</Label>
                                        <Input
                                            id="rate_limit_per_minute"
                                            type="number"
                                            value={data.rate_limit_per_minute}
                                            onChange={(e) => setData('rate_limit_per_minute', e.target.value)}
                                            placeholder="Leave empty for tier default"
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="rate_limit_burst">Custom Burst Limit</Label>
                                        <Input
                                            id="rate_limit_burst"
                                            type="number"
                                            value={data.rate_limit_burst}
                                            onChange={(e) => setData('rate_limit_burst', e.target.value)}
                                            placeholder="Leave empty for tier default"
                                        />
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="allowed_ips">Allowed IP Addresses (optional)</Label>
                                    <Textarea
                                        id="allowed_ips"
                                        value={data.allowed_ips.join('\n')}
                                        onChange={(e) => {
                                            const ips = e.target.value.split('\n').filter(ip => ip.trim() !== '');
                                            setData('allowed_ips', ips);
                                        }}
                                        placeholder="Enter IP addresses, one per line&#10;*************&#10;*********"
                                        rows={3}
                                    />
                                    <p className="text-xs text-gray-500 mt-1">
                                        Leave empty to allow access from any IP address
                                    </p>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="is_active"
                                        checked={data.is_active}
                                        onCheckedChange={(checked) => setData('is_active', checked)}
                                    />
                                    <Label htmlFor="is_active">Active</Label>
                                </div>

                                <DialogFooter>
                                    <Button type="button" variant="outline" onClick={() => setShowCreateModal(false)}>
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        Create System User
                                    </Button>
                                </DialogFooter>
                            </form>
                        </DialogContent>
                    </Dialog>
                </div>
            }
        >
            <Head title="System Users Management" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Search and Filters */}
                    <Card className="mb-6">
                        <CardContent className="pt-6">
                            <div className="flex gap-4">
                                <Input
                                    placeholder="Search system users..."
                                    defaultValue={filters.search}
                                    onChange={(e) => {
                                        router.get(route('admin.system-users.index'), 
                                            { ...filters, search: e.target.value },
                                            { preserveState: true, replace: true }
                                        );
                                    }}
                                    className="max-w-sm"
                                />
                            </div>
                        </CardContent>
                    </Card>

                    {/* System Users Table */}
                    <Card>
                        <CardHeader>
                            <CardTitle>System Users ({systemUsers.total})</CardTitle>
                            <CardDescription>
                                Manage external system users and their API access permissions.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>User</TableHead>
                                        <TableHead>System Type</TableHead>
                                        <TableHead>Rate Limits</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Tokens</TableHead>
                                        <TableHead>Last Activity</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {systemUsers.data.map((user) => (
                                        <TableRow key={user.id}>
                                            <TableCell>
                                                <div>
                                                    <div className="font-medium">{user.name}</div>
                                                    <div className="text-sm text-gray-500">{user.email}</div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant="outline">{user.system_type}</Badge>
                                            </TableCell>
                                            <TableCell>{getRateLimitInfo(user)}</TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <Badge variant={user.is_active ? "default" : "secondary"}>
                                                        {user.is_active ? "Active" : "Inactive"}
                                                    </Badge>
                                                    {user.is_active && (
                                                        <Activity className="w-4 h-4 text-green-500" />
                                                    )}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-center">
                                                    <div className="font-medium">{user.tokens_count}</div>
                                                    <div className="text-xs text-gray-500">tokens</div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                {user.last_api_call_at ? (
                                                    <div className="text-sm">
                                                        {new Date(user.last_api_call_at).toLocaleDateString()}
                                                    </div>
                                                ) : (
                                                    <span className="text-gray-400">Never</span>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center gap-2">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => setViewingUser(user)}
                                                    >
                                                        <Eye className="w-4 h-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => openEditModal(user)}
                                                    >
                                                        <Edit className="w-4 h-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleToggleStatus(user)}
                                                    >
                                                        <Power className="w-4 h-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleDelete(user)}
                                                        className="text-red-600 hover:text-red-700"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
