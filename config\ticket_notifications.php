<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Ticket Notification Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the ticket notification
    | system, including automation rules, notification channels, and
    | delivery preferences.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Notification Channels
    |--------------------------------------------------------------------------
    |
    | Define which notification channels should be used for different
    | types of ticket notifications.
    |
    */
    'channels' => [
        'api_tickets' => ['mail', 'database'],
        'web_tickets' => ['database'],
        'urgent_tickets' => ['mail', 'database', 'slack'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the notification queue system.
    |
    */
    'queue' => [
        'connection' => env('TICKET_NOTIFICATION_QUEUE_CONNECTION', 'redis'),
        'queue_name' => env('TICKET_NOTIFICATION_QUEUE_NAME', 'notifications'),
        'delay_seconds' => env('TICKET_NOTIFICATION_DELAY', 5),
        'max_attempts' => env('TICKET_NOTIFICATION_MAX_ATTEMPTS', 3),
        'timeout_seconds' => env('TICKET_NOTIFICATION_TIMEOUT', 120),
    ],

    /*
    |--------------------------------------------------------------------------
    | User Notification Rules
    |--------------------------------------------------------------------------
    |
    | Define which users should receive notifications based on different
    | criteria.
    |
    */
    'notification_rules' => [
        'admin_users' => [
            'enabled' => true,
            'roles' => ['Admin'],
            'for_sources' => ['api', 'web'],
        ],
        'department_users' => [
            'enabled' => true,
            'for_sources' => ['api', 'web'],
            'exclude_creator' => true,
        ],
        'assigned_users' => [
            'enabled' => true,
            'for_sources' => ['api', 'web'],
        ],
        'category_handlers' => [
            'enabled' => true,
            'for_sources' => ['api'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Automation Rules
    |--------------------------------------------------------------------------
    |
    | Configuration for automatic rule application during ticket creation.
    |
    */
    'automation' => [
        'enabled' => env('TICKET_AUTOMATION_ENABLED', true),
        'apply_to_api_tickets' => env('TICKET_AUTOMATION_API', true),
        'apply_to_web_tickets' => env('TICKET_AUTOMATION_WEB', true),
        'max_rules_per_ticket' => env('TICKET_AUTOMATION_MAX_RULES', 10),
        'execution_timeout' => env('TICKET_AUTOMATION_TIMEOUT', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for ticket notification and automation logging.
    |
    */
    'logging' => [
        'enabled' => env('TICKET_LOGGING_ENABLED', true),
        'level' => env('TICKET_LOGGING_LEVEL', 'info'),
        'log_successful_notifications' => env('TICKET_LOG_SUCCESS', false),
        'log_failed_notifications' => env('TICKET_LOG_FAILURES', true),
        'log_automation_rules' => env('TICKET_LOG_AUTOMATION', true),
        'log_user_eligibility' => env('TICKET_LOG_ELIGIBILITY', false),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configuration for rate limiting notification sending to prevent
    | overwhelming users or external services.
    |
    */
    'rate_limiting' => [
        'enabled' => env('TICKET_RATE_LIMITING_ENABLED', true),
        'max_notifications_per_user_per_hour' => env('TICKET_MAX_NOTIFICATIONS_PER_HOUR', 10),
        'max_notifications_per_minute' => env('TICKET_MAX_NOTIFICATIONS_PER_MINUTE', 2),
        'cooldown_period_minutes' => env('TICKET_NOTIFICATION_COOLDOWN', 5),
    ],

    /*
    |--------------------------------------------------------------------------
    | Email Configuration
    |--------------------------------------------------------------------------
    |
    | Specific configuration for email notifications.
    |
    */
    'email' => [
        'from_address' => env('TICKET_EMAIL_FROM', env('MAIL_FROM_ADDRESS')),
        'from_name' => env('TICKET_EMAIL_FROM_NAME', env('MAIL_FROM_NAME')),
        'reply_to' => env('TICKET_EMAIL_REPLY_TO'),
        'template_path' => 'emails.tickets',
        'include_ticket_content' => env('TICKET_EMAIL_INCLUDE_CONTENT', true),
        'max_content_length' => env('TICKET_EMAIL_MAX_CONTENT_LENGTH', 500),
    ],

    /*
    |--------------------------------------------------------------------------
    | Priority-based Notification Rules
    |--------------------------------------------------------------------------
    |
    | Different notification behavior based on ticket priority.
    |
    */
    'priority_rules' => [
        'urgent' => [
            'immediate_notification' => true,
            'channels' => ['mail', 'database', 'slack'],
            'notify_all_admins' => true,
            'escalation_delay_minutes' => 15,
        ],
        'high' => [
            'immediate_notification' => true,
            'channels' => ['mail', 'database'],
            'notify_all_admins' => false,
            'escalation_delay_minutes' => 60,
        ],
        'medium' => [
            'immediate_notification' => false,
            'channels' => ['database'],
            'notify_all_admins' => false,
            'escalation_delay_minutes' => 240,
        ],
        'low' => [
            'immediate_notification' => false,
            'channels' => ['database'],
            'notify_all_admins' => false,
            'escalation_delay_minutes' => 1440, // 24 hours
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Templates
    |--------------------------------------------------------------------------
    |
    | Template configuration for different notification types.
    |
    */
    'templates' => [
        'api_ticket_created' => [
            'mail' => 'emails.tickets.api-created',
            'database' => 'notifications.tickets.api-created',
        ],
        'web_ticket_created' => [
            'mail' => 'emails.tickets.web-created',
            'database' => 'notifications.tickets.web-created',
        ],
        'ticket_assigned' => [
            'mail' => 'emails.tickets.assigned',
            'database' => 'notifications.tickets.assigned',
        ],
    ],
];
