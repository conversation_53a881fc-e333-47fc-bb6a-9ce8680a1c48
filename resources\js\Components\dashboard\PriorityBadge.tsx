import React from 'react';
import { Badge } from '@/Components/ui/badge';
import { cn } from '@/lib/utils';
import { AlertTriangle, ArrowUp, ArrowDown, Minus } from 'lucide-react';

interface PriorityBadgeProps {
  priority: string;
  className?: string;
  showIcon?: boolean;
}

export default function PriorityBadge({ priority, className, showIcon = true }: PriorityBadgeProps) {
  const getPriorityConfig = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return {
          label: 'Khẩn cấp',
          variant: 'destructive' as const,
          className: 'bg-red-100 text-red-800 border-red-200 hover:bg-red-200',
          icon: AlertTriangle
        };
      case 'high':
        return {
          label: 'Cao',
          variant: 'secondary' as const,
          className: 'bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200',
          icon: ArrowUp
        };
      case 'medium':
        return {
          label: 'Trung bình',
          variant: 'secondary' as const,
          className: 'bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200',
          icon: Minus
        };
      case 'low':
        return {
          label: 'Thấp',
          variant: 'secondary' as const,
          className: 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200',
          icon: ArrowDown
        };
      default:
        return {
          label: priority,
          variant: 'outline' as const,
          className: '',
          icon: Minus
        };
    }
  };

  const config = getPriorityConfig(priority);
  const Icon = config.icon;

  return (
    <Badge 
      variant={config.variant}
      className={cn('flex items-center gap-1', config.className, className)}
    >
      {showIcon && <Icon className="h-3 w-3" />}
      {config.label}
    </Badge>
  );
}
