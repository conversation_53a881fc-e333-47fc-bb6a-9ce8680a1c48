<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            // External system integration fields
            $table->string('requester_name')->nullable()->after('user_id')->comment('Name of the person requesting support');
            $table->string('requester_email')->nullable()->after('requester_name')->comment('Email of the person requesting support');
            $table->string('source_system')->nullable()->after('requester_email')->comment('e.g., crm, payment, monitoring');
            $table->string('external_id')->nullable()->after('source_system')->comment('The ticket ID from the source system');
            
            // Payload storage for debugging and audit
            $table->json('payload')->nullable()->after('external_id')->comment('Original JSON payload for debugging');
            
            // Indexes for performance
            $table->index(['source_system', 'external_id']);
            $table->index('requester_email');
            $table->index('source_system');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tickets', function (Blueprint $table) {
            $table->dropIndex(['source_system', 'external_id']);
            $table->dropIndex('requester_email');
            $table->dropIndex('source_system');
            
            $table->dropColumn([
                'requester_name',
                'requester_email',
                'source_system',
                'external_id',
                'payload'
            ]);
        });
    }
};
