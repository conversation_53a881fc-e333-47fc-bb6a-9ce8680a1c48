# Ticket Automation and Notification System

## Overview

This document describes the enhanced ticket creation system that includes automatic rule application and comprehensive notification functionality for both web interface and API-created tickets.

## Features

### 1. Automatic Rule Application
- **Consistent Application**: Rules are applied automatically for tickets created from ANY source (web interface or API)
- **Pre-save Processing**: Rules are applied before saving the ticket to the database
- **Comprehensive Logging**: All rule applications are logged for debugging and auditing

### 2. Enhanced API Notification System
- **Comprehensive Coverage**: Notifications sent to admin users, department users, and assigned users
- **Asynchronous Processing**: Notifications are dispatched via queue jobs to avoid API response delays
- **Rich Content**: Notifications include ticket details, priority, department info, and automation history

### 3. Robust Error Handling and Logging
- **Detailed Logging**: Comprehensive logging for all operations using `TicketLoggingService`
- **Error Recovery**: Proper error handling with retry mechanisms for failed notifications
- **Performance Monitoring**: Tracking of notification success rates and automation statistics

## Architecture

### Core Components

1. **AdminService** (`app/Services/AdminService.php`)
   - Central service for ticket creation with automation
   - Handles rule application and notification dispatch
   - Provides consistent interface for both web and API ticket creation

2. **TicketLoggingService** (`app/Services/TicketLoggingService.php`)
   - Centralized logging for all ticket-related operations
   - Tracks automation rule applications, notification attempts, and failures
   - Provides detailed audit trail for debugging

3. **SendTicketNotificationsJob** (`app/Jobs/SendTicketNotificationsJob.php`)
   - Asynchronous job for sending notifications
   - Handles both standard and API-specific notifications
   - Implements retry logic and failure handling

4. **ApiTicketCreatedNotification** (`app/Notifications/ApiTicketCreatedNotification.php`)
   - Enhanced notification class for API-created tickets
   - Includes additional context like source system and automation history
   - Supports both email and database notifications

## Usage

### Creating Tickets with Automation

#### From Web Interface
```php
// In TicketController
$adminService = app(\App\Services\AdminService::class);
$result = $adminService->createTicketWithAutomation($ticketData, false);
```

#### From API
```php
// In TicketIntakeService
$result = $this->adminService->createTicketWithAutomation($ticketData, true);
```

### Configuration

The system uses the configuration file `config/ticket_notifications.php` for customization:

```php
// Enable/disable automation
'automation' => [
    'enabled' => true,
    'apply_to_api_tickets' => true,
    'apply_to_web_tickets' => true,
],

// Notification channels
'channels' => [
    'api_tickets' => ['mail', 'database'],
    'web_tickets' => ['database'],
],
```

## Notification Recipients

### For API-Created Tickets
1. **Admin Users**: All users with 'Admin' role
2. **Department Users**: All users belonging to the ticket's assigned department
3. **Assigned Users**: The specific user assigned to handle the ticket
4. **Category Handlers**: Users who handle the specific ticket type/category

### For Web Interface Tickets
1. **Department Users**: Users in the same department as the ticket
2. **Assigned Users**: The specific user assigned to handle the ticket

## Automation Rules

### Rule Application Process
1. Retrieve all active automation rules ordered by execution priority
2. For each rule, check if it matches the ticket based on:
   - Title keywords
   - Content keywords
   - Category conditions
   - Custom conditions
3. Apply matching rules to set:
   - Priority level
   - Department assignment
   - User assignment
   - Category type
4. Log all rule applications for audit trail

### Rule Configuration
Rules are configured through the admin interface and stored in the `automation_rules` table with the following structure:
- **Conditions**: JSON field containing matching criteria
- **Actions**: JSON field containing actions to take
- **Execution Order**: Priority order for rule application
- **Category Type**: Target category for the rule
- **Assigned Priority**: Priority level to set
- **Department/User Assignment**: Target department or user

## Logging and Monitoring

### Log Categories
1. **Ticket Creation**: Success/failure of ticket creation attempts
2. **Rule Application**: Which rules matched and were applied
3. **Notification Dispatch**: Success/failure of notification sending
4. **Job Processing**: Queue job execution status

### Log Levels
- **INFO**: Successful operations and general flow
- **DEBUG**: Detailed operation information (disabled in production)
- **WARNING**: Non-critical issues (e.g., no users to notify)
- **ERROR**: Failed operations requiring attention

### Monitoring Queries
```php
// Check notification success rate
$stats = DB::table('failed_jobs')
    ->where('queue', 'notifications')
    ->where('created_at', '>=', now()->subDay())
    ->count();

// Check automation rule effectiveness
$ruleStats = AutomationRule::orderBy('matched_count', 'desc')
    ->limit(10)
    ->get(['name', 'matched_count', 'last_matched_at']);
```

## Error Handling

### Notification Failures
- **Retry Logic**: Failed notifications are retried up to 3 times
- **Graceful Degradation**: Individual notification failures don't affect others
- **Detailed Logging**: All failures are logged with context for debugging

### Rule Application Failures
- **Isolation**: Failed rules don't prevent other rules from executing
- **Logging**: All failures are logged with rule and ticket context
- **Continuation**: Ticket creation continues even if some rules fail

## Performance Considerations

### Asynchronous Processing
- Notifications are dispatched via queue jobs to avoid blocking API responses
- Queue jobs are processed in the background with configurable delays

### Caching
- User role and department information is cached where appropriate
- Automation rules are retrieved once per ticket creation

### Database Optimization
- Proper indexing on frequently queried fields
- Efficient queries for user notification eligibility

## Security Considerations

### Access Control
- Only authorized users can create tickets via API
- Notification recipients are filtered based on permissions
- Sensitive information is excluded from logs

### Data Protection
- Personal information is handled according to privacy policies
- Notification content is sanitized before sending
- Audit logs are retained according to compliance requirements

## Troubleshooting

### Common Issues

1. **No Notifications Sent**
   - Check if users exist in the target department
   - Verify user email addresses are valid
   - Check queue job processing status

2. **Rules Not Applied**
   - Verify rules are active (`is_active = true`)
   - Check rule conditions match ticket content
   - Review execution order for rule conflicts

3. **Performance Issues**
   - Monitor queue job processing times
   - Check database query performance
   - Review log file sizes and rotation

### Debug Commands
```bash
# Check queue job status
php artisan queue:work --queue=notifications --verbose

# View recent logs
tail -f storage/logs/laravel.log | grep "ticket"

# Test automation rules
php artisan tinker
>>> $ticket = Ticket::find(1);
>>> $service = app(\App\Services\AdminService::class);
>>> $service->applyAutomationRules($ticket);
```

## Future Enhancements

1. **Real-time Notifications**: WebSocket-based real-time notifications
2. **Advanced Rules**: More complex rule conditions and actions
3. **Notification Preferences**: User-configurable notification preferences
4. **Analytics Dashboard**: Visual dashboard for automation and notification metrics
5. **Integration APIs**: Webhooks for external system integration
