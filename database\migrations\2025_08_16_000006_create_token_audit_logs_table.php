<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('token_audit_logs', function (Blueprint $table) {
            $table->id();
            
            // Token identification
            $table->string('token_id');
            $table->string('token_name');
            $table->foreignId('tokenable_id')->constrained('users')->cascadeOnDelete();
            $table->string('tokenable_type')->default('App\\Models\\User');
            
            // Operation details
            $table->enum('action', ['created', 'updated', 'revoked', 'rotated', 'expired', 'activated', 'deactivated']);
            $table->foreignId('performed_by')->nullable()->constrained('users')->nullOnDelete();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            
            // Context and metadata
            $table->text('reason')->nullable();
            $table->json('old_values')->nullable();
            $table->json('new_values')->nullable();
            $table->json('metadata')->nullable();
            
            $table->timestamps();
            
            // Indexes
            $table->index(['token_id', 'created_at']);
            $table->index(['tokenable_id', 'action', 'created_at']);
            $table->index(['performed_by', 'created_at']);
            $table->index(['action', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('token_audit_logs');
    }
};
