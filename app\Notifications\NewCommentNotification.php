<?php

namespace App\Notifications;

use App\Models\Comments;
use Illuminate\Broadcasting\Channel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewCommentNotification extends Notification implements ShouldBroadcast
{
    use Queueable;

    public Comments $comment;

    public function __construct(Comments $comment)
    {
        $this->comment = $comment;
    }

    public function via($notifiable): array
    {
        return ['database', 'broadcast'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('<PERSON><PERSON>nh luận mới trên ticket của bạn')
            ->greeting("Xin chào {$notifiable->name}!")
            ->line("{$this->comment->user->name} đã bình luận trên ticket của bạn: **{$this->comment->ticket->title}**")
            ->line("Nội dung: {$this->comment->comment}")
            ->action('Xem bình luận', url('/tickets/'.$this->comment->ticket->slug.'#comment-'.$this->comment->id))
            ->line('Cảm ơn bạn đã sử dụng dịch vụ!');
    }

    public function toArray($notifiable)
    {
        return [
            'ticket_id' => $this->comment->ticket_id,
            'title' => $this->comment->ticket->title,
            'message' => "You have new comment in {$this->comment->ticket->title}",
            'slug' => $this->comment->ticket->slug,
            'name' => $this->comment->user->name,
            'profile_photo_url' => $this->comment->user->profile_photo_path
                ? asset('storage/'.$this->comment->user->profile_photo_path)
                : null,
            'comment_id' => $this->comment->id,
            'type_notification' => 'comment',
        ];
    }

    public function toBroadcast($notifiable)
    {
        return new BroadcastMessage([
            'id' => $this->id, // Notification ID generated by Laravel
            'data' => $this->toArray($notifiable),
            'type' => 'comment',
            'read_at' => null,
            'created_at' => $this->comment->created_at->diffForHumans(),
        ]);
    }

    public function broadcastOn()
    {
        return new Channel('notifications-comment.'.$this->comment->ticket->user_id);
    }
}
