<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckAdminAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Kiểm tra user đã đăng nhập
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // <PERSON><PERSON><PERSON> tra quyền truy cập admin dashboard
        if (!$user->can('access-admin-dashboard')) {
            abort(403, 'Bạn không có quyền truy cập vào khu vực quản trị.');
        }

        return $next($request);
    }
}
