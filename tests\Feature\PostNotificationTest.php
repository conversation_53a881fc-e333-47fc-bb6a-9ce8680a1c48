<?php

namespace Tests\Feature;

use App\Events\NewPostCreated;
use App\Listeners\SendPostNotification;
use App\Models\Ticket;
use App\Models\User;
use App\Models\Category;
use App\Models\Tag;
use App\Notifications\NewPostNotification;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Notification;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class PostNotificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'admin']);
        Role::create(['name' => 'moderator']);
        Role::create(['name' => 'user']);
    }

    /** @test */
    public function it_sends_notification_when_post_is_created()
    {
        // Arrange
        Notification::fake();
        Event::fake();

        // Create users
        $author = User::factory()->create();
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        // Create a ticket/post
        $ticket = Ticket::factory()->create([
            'user_id' => $author->id,
            'title' => 'Test Post',
            'content' => 'This is a test post content',
            'is_published' => true,
        ]);

        // Act - Dispatch the event
        event(new NewPostCreated($ticket));

        // Assert
        Event::assertDispatched(NewPostCreated::class, function ($event) use ($ticket) {
            return $event->post->id === $ticket->id;
        });
    }

    /** @test */
    public function listener_sends_notification_to_admin_users()
    {
        // Arrange
        Notification::fake();

        // Create users
        $author = User::factory()->create();
        $admin1 = User::factory()->create();
        $admin2 = User::factory()->create();
        $regularUser = User::factory()->create();

        $admin1->assignRole('admin');
        $admin2->assignRole('admin');
        $regularUser->assignRole('user');

        // Create a ticket/post with relationships
        $category = Category::factory()->create();
        $tag = Tag::factory()->create();
        
        $ticket = Ticket::factory()->create([
            'user_id' => $author->id,
            'title' => 'Test Post',
            'content' => 'This is a test post content',
            'is_published' => true,
        ]);

        $ticket->categories()->attach($category);
        $ticket->tags()->attach($tag);
        $ticket->load(['user', 'categories', 'tags']);

        // Act - Handle the event through listener
        $listener = new SendPostNotification();
        $listener->handle(new NewPostCreated($ticket));

        // Assert - Check notifications were sent to admin users only
        Notification::assertSentTo(
            [$admin1, $admin2],
            NewPostNotification::class,
            function ($notification) use ($ticket) {
                return $notification->post->id === $ticket->id;
            }
        );

        // Assert - Regular user should not receive notification
        Notification::assertNotSentTo($regularUser, NewPostNotification::class);
        
        // Assert - Author should not receive notification
        Notification::assertNotSentTo($author, NewPostNotification::class);
    }

    /** @test */
    public function notification_contains_correct_data()
    {
        // Arrange
        $author = User::factory()->create(['name' => 'John Doe']);
        $admin = User::factory()->create();
        $admin->assignRole('admin');

        $category = Category::factory()->create(['title' => 'Test Category']);
        $tag = Tag::factory()->create(['name' => 'test-tag']);

        $ticket = Ticket::factory()->create([
            'user_id' => $author->id,
            'title' => 'Test Post Title',
            'content' => 'This is a test post content that should be truncated in excerpt',
            'slug' => 'test-post-title',
            'is_published' => true,
        ]);

        $ticket->categories()->attach($category);
        $ticket->tags()->attach($tag);
        $ticket->load(['user', 'categories', 'tags']);

        // Act
        $notification = new NewPostNotification($ticket);
        $notificationData = $notification->toArray($admin);

        // Assert
        $this->assertEquals($ticket->id, $notificationData['post_id']);
        $this->assertEquals('Test Post Title', $notificationData['title']);
        $this->assertEquals('test-post-title', $notificationData['slug']);
        $this->assertEquals('New post created: Test Post Title', $notificationData['message']);
        $this->assertEquals('John Doe', $notificationData['name']);
        $this->assertEquals(['Test Category'], $notificationData['categories']);
        $this->assertEquals(['test-tag'], $notificationData['tags']);
        $this->assertEquals('post', $notificationData['type_notification']);
        $this->assertNotEmpty($notificationData['content']); // Should contain excerpt
    }

    /** @test */
    public function notification_broadcast_data_is_correct()
    {
        // Arrange
        $author = User::factory()->create(['name' => 'Jane Doe']);
        $admin = User::factory()->create();

        $ticket = Ticket::factory()->create([
            'user_id' => $author->id,
            'title' => 'Broadcast Test Post',
            'content' => 'Content for broadcast test',
            'is_published' => true,
        ]);

        $ticket->load(['user', 'categories', 'tags']);

        // Act
        $notification = new NewPostNotification($ticket);
        $broadcastData = $notification->toBroadcast($admin);

        // Assert
        $this->assertEquals($ticket->id, $broadcastData->data['id']);
        $this->assertEquals('post', $broadcastData->data['type']);
        $this->assertArrayHasKey('data', $broadcastData->data);
        $this->assertArrayHasKey('created_at', $broadcastData->data);
        $this->assertNull($broadcastData->data['read_at']);
    }
}
