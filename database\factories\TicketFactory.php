<?php

namespace Database\Factories;

use App\Models\Ticket;
use App\Models\User;
use App\Models\Departments;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Ticket>
 */
class TicketFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    protected $model = Ticket::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(),
            'content' => $this->faker->paragraphs(3, true),
            'slug' => $this->faker->slug(),
            'user_id' => User::factory(),
            'department_id' => Departments::factory(),
            'assignee_id' => User::factory(),
            'status' => $this->faker->randomElement(['open', 'in_progress', 'resolved', 'closed']),
            'priority' => $this->faker->randomElement(['low', 'medium', 'high', 'urgent']),
            'category_type' => $this->faker->randomElement(['technical', 'payment', 'consultation', 'general']),
            'is_published' => $this->faker->boolean(80), // 80% chance of being published
            'product_id' => $this->faker->optional()->randomNumber(5),
            'product_name' => $this->faker->optional()->words(2, true),
            'priority_score' => $this->faker->numberBetween(0, 100),
            'automation_applied' => $this->faker->optional()->randomElements(['auto_assign', 'priority_boost'], 2),
            'auto_assigned_at' => $this->faker->optional()->dateTimeBetween('-1 month', 'now'),
            'auto_assigned_by_rule_id' => $this->faker->optional()->randomNumber(3),
            'created_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the ticket is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => true,
        ]);
    }

    /**
     * Indicate that the ticket is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_published' => false,
        ]);
    }

    /**
     * Indicate that the ticket has high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'high',
            'priority_score' => $this->faker->numberBetween(75, 100),
        ]);
    }

    /**
     * Indicate that the ticket is urgent.
     */
    public function urgent(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => 'urgent',
            'priority_score' => $this->faker->numberBetween(90, 100),
        ]);
    }

    /**
     * Indicate that the ticket is resolved.
     */
    public function resolved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'resolved',
        ]);
    }

    /**
     * Indicate that the ticket is closed.
     */
    public function closed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'closed',
        ]);
    }
}
