import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import { Badge } from '@/Components/ui/badge';
import { Card, CardContent } from '@/Components/ui/card';
import { 
  Mail, 
  Building, 
  Activity, 
  CheckCircle, 
  Clock,
  User
} from 'lucide-react';

interface UserCardProps {
  user: {
    id: number;
    name: string;
    email: string;
    profile_photo_url?: string;
    department?: {
      id: number;
      name: string;
    };
    role?: string;
    workload?: number;
    performance_score?: number;
    availability?: 'available' | 'busy' | 'offline';
  };
  isSelected?: boolean;
  onClick?: () => void;
  showStats?: boolean;
}

export default function UserCard({ 
  user, 
  isSelected = false, 
  onClick, 
  showStats = true 
}: UserCardProps) {
  const getAvailabilityColor = (availability?: string) => {
    switch (availability) {
      case 'available':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'busy':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'offline':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getAvailabilityLabel = (availability?: string) => {
    switch (availability) {
      case 'available':
        return 'Sẵn sàng';
      case 'busy':
        return 'Bận';
      case 'offline':
        return 'Offline';
      default:
        return 'Online';
    }
  };

  return (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-md ${
        isSelected 
          ? 'ring-2 ring-blue-500 bg-blue-50 border-blue-200' 
          : 'hover:bg-gray-50'
      }`}
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          <Avatar className="h-12 w-12">
            <AvatarImage src={user.profile_photo_url || ''} alt={user.name} />
            <AvatarFallback className="bg-blue-100 text-blue-600">
              {user.name.charAt(0)}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-gray-900 truncate">{user.name}</h4>
              <Badge 
                variant="outline" 
                className={getAvailabilityColor(user.availability)}
              >
                {getAvailabilityLabel(user.availability)}
              </Badge>
            </div>
            
            <div className="space-y-1">
              <div className="flex items-center gap-1 text-sm text-gray-600">
                <Mail className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">{user.email}</span>
              </div>
              
              {user.department && (
                <div className="flex items-center gap-1 text-sm text-gray-600">
                  <Building className="h-3 w-3 flex-shrink-0" />
                  <span className="truncate">{user.department.name}</span>
                </div>
              )}
            </div>

            {showStats && (
              <div className="flex items-center gap-3 mt-2">
                {user.workload !== undefined && (
                  <div className="flex items-center gap-1 text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded">
                    <Activity className="h-3 w-3" />
                    <span>{user.workload} tickets</span>
                  </div>
                )}
                {user.performance_score !== undefined && (
                  <div className="flex items-center gap-1 text-xs bg-green-50 text-green-700 px-2 py-1 rounded">
                    <CheckCircle className="h-3 w-3" />
                    <span>{user.performance_score}%</span>
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="flex-shrink-0">
            {isSelected && (
              <CheckCircle className="h-5 w-5 text-blue-500" />
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
