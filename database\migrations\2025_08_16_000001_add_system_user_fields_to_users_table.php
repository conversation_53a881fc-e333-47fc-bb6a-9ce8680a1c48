<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // System user identification
            $table->boolean('is_system_user')->default(false)->after('email');
            $table->string('system_type')->nullable()->after('is_system_user')->comment('e.g., payment, crm, monitoring');
            $table->text('system_description')->nullable()->after('system_type');
            
            // Rate limiting configuration
            $table->string('rate_limit_tier')->default('standard')->after('system_description')->comment('standard, premium, enterprise');
            $table->integer('rate_limit_per_minute')->default(60)->after('rate_limit_tier');
            $table->integer('rate_limit_burst')->default(10)->after('rate_limit_per_minute');
            
            // System user metadata
            $table->json('allowed_ips')->nullable()->after('rate_limit_burst')->comment('IP whitelist for system users');
            $table->timestamp('last_api_call_at')->nullable()->after('allowed_ips');
            $table->boolean('is_active')->default(true)->after('last_api_call_at');
            
            // Indexes for performance
            $table->index(['is_system_user', 'is_active']);
            $table->index(['system_type', 'is_active']);
            $table->index('last_api_call_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['is_system_user', 'is_active']);
            $table->dropIndex(['system_type', 'is_active']);
            $table->dropIndex('last_api_call_at');
            
            $table->dropColumn([
                'is_system_user',
                'system_type',
                'system_description',
                'rate_limit_tier',
                'rate_limit_per_minute',
                'rate_limit_burst',
                'allowed_ips',
                'last_api_call_at',
                'is_active'
            ]);
        });
    }
};
