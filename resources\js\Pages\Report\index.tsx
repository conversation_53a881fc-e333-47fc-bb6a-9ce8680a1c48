import { AppSidebar } from '@/Components/dashboard/app-sidebar';
import { SidebarInset, SidebarProvider } from '@/Components/ui/sidebar';
import { PageProps } from '@inertiajs/core';
import React, { useState, useEffect } from 'react';
import { SiteHeader } from '@/Components/dashboard/site-header';
import { NavigationProgress } from '@/Components/ui/navigation-progress';
import { Head, router } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import { Progress } from '@/Components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/Components/ui/tabs';
import { AdvancedTicketTable } from '@/Components/dashboard/ticket-table';
import {
  AlertTriangle,
  Clock,
  CheckCircle,
  XCircle,
  Users,
  TrendingUp,
  TrendingDown,
  Activity,
  BarChart3,
  Calendar,
  RefreshCw,
  Filter,
  Download,
  Settings,
  Bell,
  Zap,
  Target,
  Award,
  Timer
} from 'lucide-react';
import PostsPage from '../Admin/Tickets';
interface User {
  name: string;
  email: string;
  profile_photo_url: string | null;
}

interface Post {
  id: number;
  title: string;
  status: string;
  priority: string;
  created_at: string;
  user: {
    name: string;
    email: string;
    profile_photo_url: string | null;
  };
  assignee?: {
    id: number;
    name: string;
    email: string;
    profile_photo_url: string | null;
  };
  department?: {
    id: number;
    name: string;
  };
  comment?: number;
  priority_score?: number;
}

interface AutomationStats {
  total_rules: number;
  active_rules: number;
  total_matches: number;
  recent_matches: number;
  top_rules: Array<{
    id: number;
    name: string;
    matched_count: number;
  }>;
}

interface DashboardData {
  ticketStats: {
    urgentTickets: number;
    openTickets: number;
    inProgressTickets: number;
    resolvedTickets: number;
    closedTickets: number;
  };
  posts: Array<{
    id: number;
    status: string;
    priority: string;
    title: string;
    created_at: string;
    user: {
      id: number;
      name: string;
      email: string;
      profile_photo_url: string | null;
    };
    assignee?: {
      id: number;
      name: string;
      email: string;
      profile_photo_url: string | null;
    };
    department?: {
      id: number;
      name: string;
    };
  }>;
  totalPosts: number;
  totalUsers: number;
  automation_stats: AutomationStats;
}

interface DashboardProps extends PageProps {
  data: DashboardData;
  assignableUsers: Array<{
    id: number;
    name: string;
    email: string;
    profile_photo_url: string | null;
  }>;
}

export default function Page({ data, assignableUsers = [] }: DashboardProps) {
  // Initialize default values
  const defaultTicketStats = {
    urgentTickets: 0,
    openTickets: 0,
    inProgressTickets: 0,
    resolvedTickets: 0,
    closedTickets: 0
  };

  // Destructure data with defaults
  const {
    ticketStats = defaultTicketStats,
    totalPosts = 0,
    totalUsers = 0,
    automation_stats = {},
    posts = []
  } = data || {};

  const [activeTab, setActiveTab] = useState('overview');
  const [refreshKey, setRefreshKey] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Calculate ticket statistics
  const calculatedTicketStats = React.useMemo(() => {
    const openTickets = posts.filter((post: { status: string }) => post.status === 'open').length;
    const inProgressTickets = posts.filter((post: { status: string }) => post.status === 'in_progress').length;
    const urgentTickets = posts.filter((post: { priority: string, status: string }) => 
      post.priority === 'urgent' && post.status !== 'resolved'
    ).length;
    const resolvedTickets = posts.filter((post: { status: string }) => post.status === 'resolved').length;
    const closedTickets = posts.filter((post: { status: string }) => post.status === 'closed').length;
    const unassignedTickets = posts.filter((post: { assignee?: { id: number; name: string; email: string; profile_photo_url: string | null } }) => !post.assignee).length;

    return {
      openTickets,
      inProgressTickets,
      urgentTickets,
      resolvedTickets,
      closedTickets,
      unassignedTickets,
      totalTickets: posts.length,
    };
  }, [posts]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setRefreshKey(prev => prev + 1);
    }, 30000);
    return () => clearInterval(interval);
  }, []);

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      router.reload();
    } catch (error) {
      console.error('Error refreshing dashboard:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Calculate performance metrics
  const performanceMetrics = React.useMemo(() => {
    const totalTickets = posts.length;
    const resolvedTickets = calculatedTicketStats.resolvedTickets;
    const resolutionRate = totalTickets > 0 ? (resolvedTickets / totalTickets) * 100 : 0;

    const urgentTickets = calculatedTicketStats.urgentTickets;
    const urgentRate = totalTickets > 0 ? (urgentTickets / totalTickets) * 100 : 0;

    return {
      resolutionRate: Math.round(resolutionRate),
      urgentRate: Math.round(urgentRate),
      averageResponseTime: '2.5h', // Mock data
      customerSatisfaction: 94 // Mock data
    };
  }, [posts, calculatedTicketStats]);

  return (
    <SidebarProvider>
      <Head title={'Admin Dashboard - Hệ thống hỗ trợ'} />
      <NavigationProgress />
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader title={'Admin Dashboard'} />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-6 p-4 md:p-6">

            {/* Header với Quick Actions */}
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div>
                <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
                <p className="text-muted-foreground">
                  Tổng quan hệ thống hỗ trợ khách hàng
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                  {isRefreshing ? 'Đang tải...' : 'Refresh'}
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Cài đặt
                </Button>
              </div>
            </div>

            {/* Alert cho tickets khẩn cấp */}
            {calculatedTicketStats.urgentTickets > 0 && (
              <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950">
                <CardContent className="flex items-center gap-3 p-4">
                  <AlertTriangle className="h-5 w-5 text-red-600" />
                  <div className="flex-1">
                    <p className="text-sm font-medium text-red-800 dark:text-red-200">
                      Có {calculatedTicketStats.urgentTickets} ticket khẩn cấp cần xử lý ngay!
                    </p>
                    <p className="text-xs text-red-600 dark:text-red-300">
                      Hãy ưu tiên xử lý các ticket này để đảm bảo chất lượng dịch vụ
                    </p>
                  </div>
                  <Badge variant="destructive" className="font-semibold">
                    {calculatedTicketStats.urgentTickets}
                  </Badge>
                </CardContent>
              </Card>
            )}

            {/* Stats Cards Grid */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* Total Tickets */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Tổng Tickets</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{calculatedTicketStats.totalTickets}</div>
                  <p className="text-xs text-muted-foreground">
                    +{Math.floor(Math.random() * 10) + 1} từ hôm qua
                  </p>
                </CardContent>
              </Card>

              {/* Open Tickets */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Tickets Mở</CardTitle>
                  <Clock className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">
                    {calculatedTicketStats.openTickets}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {calculatedTicketStats.unassignedTickets} chưa được giao
                  </p>
                </CardContent>
              </Card>

              {/* In Progress */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Đang Xử Lý</CardTitle>
                  <Timer className="h-4 w-4 text-yellow-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-yellow-600">
                    {calculatedTicketStats.inProgressTickets}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Đang được xử lý bởi team
                  </p>
                </CardContent>
              </Card>

              {/* Resolution Rate */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Tỷ Lệ Giải Quyết</CardTitle>
                  <Target className="h-4 w-4 text-green-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {performanceMetrics.resolutionRate}%
                  </div>
                  <Progress value={performanceMetrics.resolutionRate} className="mt-2" />
                </CardContent>
              </Card>
            </div>

            {/* Performance Metrics */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Thời Gian Phản Hồi TB</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{performanceMetrics.averageResponseTime}</div>
                  <p className="text-xs text-green-600">
                    <TrendingDown className="inline h-3 w-3 mr-1" />
                    -12% so với tuần trước
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Độ Hài Lòng KH</CardTitle>
                  <Award className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{performanceMetrics.customerSatisfaction}%</div>
                  <p className="text-xs text-green-600">
                    <TrendingUp className="inline h-3 w-3 mr-1" />
                    +2% so với tuần trước
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Tickets Khẩn Cấp</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">
                    {calculatedTicketStats.urgentTickets}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {performanceMetrics.urgentRate}% tổng tickets
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Tổng Người Dùng</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{totalUsers}</div>
                  <p className="text-xs text-muted-foreground">
                    Người dùng đã đăng ký
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Main Content Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
              <TabsList>
                <TabsTrigger value="overview">Tổng Quan</TabsTrigger>
                <TabsTrigger value="tickets">Quản Lý Tickets</TabsTrigger>
                <TabsTrigger value="analytics">Phân Tích</TabsTrigger>
                <TabsTrigger value="automation">Tự Động Hóa</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  {/* Recent Activity */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Activity className="h-5 w-5" />
                        Hoạt Động Gần Đây
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                        <div className="flex-1">
                          <p className="text-sm">Ticket #123 đã được giải quyết</p>
                          <p className="text-xs text-muted-foreground">2 phút trước</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                        <div className="flex-1">
                          <p className="text-sm">Ticket mới #124 được tạo</p>
                          <p className="text-xs text-muted-foreground">5 phút trước</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
                        <div className="flex-1">
                          <p className="text-sm">Ticket #122 được giao cho John</p>
                          <p className="text-xs text-muted-foreground">10 phút trước</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Quick Stats */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-5 w-5" />
                        Thống Kê Nhanh
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Tickets hôm nay</span>
                        <Badge variant="secondary">{Math.floor(Math.random() * 20) + 5}</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Đã giải quyết hôm nay</span>
                        <Badge variant="secondary">{Math.floor(Math.random() * 15) + 3}</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Thời gian phản hồi TB</span>
                        <Badge variant="secondary">2.5h</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Nhân viên online</span>
                        <Badge variant="secondary">{assignableUsers.length}</Badge>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* <TabsContent value="tickets" className="space-y-4">
                <AdvancedTicketTable
                    posts={undefined}
                />
              </TabsContent> */}

              <TabsContent value="analytics" className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle>Biểu Đồ Trạng Thái Tickets</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Mở</span>
                          <div className="flex items-center gap-2">
                            <Progress value={(calculatedTicketStats.openTickets / calculatedTicketStats.totalTickets) * 100} className="w-20" />
                            <span className="text-sm font-medium">{calculatedTicketStats.openTickets}</span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Đang xử lý</span>
                          <div className="flex items-center gap-2">
                            <Progress value={(calculatedTicketStats.inProgressTickets / calculatedTicketStats.totalTickets) * 100} className="w-20" />
                            <span className="text-sm font-medium">{calculatedTicketStats.inProgressTickets}</span>
                          </div>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Đã giải quyết</span>
                          <div className="flex items-center gap-2">
                            <Progress value={(calculatedTicketStats.resolvedTickets / calculatedTicketStats.totalTickets) * 100} className="w-20" />
                            <span className="text-sm font-medium">{calculatedTicketStats.resolvedTickets}</span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Hiệu Suất Team</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Tỷ lệ giải quyết</span>
                          <span className="text-sm font-medium">{performanceMetrics.resolutionRate}%</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Độ hài lòng KH</span>
                          <span className="text-sm font-medium">{performanceMetrics.customerSatisfaction}%</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Thời gian phản hồi TB</span>
                          <span className="text-sm font-medium">{performanceMetrics.averageResponseTime}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              <TabsContent value="automation" className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Zap className="h-5 w-5" />
                        Quy Tắc Tự Động
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Tổng quy tắc</span>
                          <Badge variant="secondary">{(automation_stats as any)?.total_rules || 0}</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Đang hoạt động</span>
                          <Badge variant="secondary">{(automation_stats as any)?.active_rules || 0}</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Lượt khớp hôm nay</span>
                          <Badge variant="secondary">{(automation_stats as any)?.recent_matches || 0}</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Quy Tắc Hiệu Quả Nhất</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {(automation_stats as any)?.top_rules?.slice(0, 3).map((rule: any, index: number) => (
                          <div key={rule.id} className="flex justify-between items-center">
                            <span className="text-sm">{rule.name}</span>
                            <Badge variant="outline">{rule.matched_count}</Badge>
                          </div>
                        )) || (
                          <p className="text-sm text-muted-foreground">Chưa có dữ liệu</p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}