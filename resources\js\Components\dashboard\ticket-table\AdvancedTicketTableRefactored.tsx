import React, { useState, useMemo, useEffect } from 'react';
import { Card, CardContent } from '@/Components/ui/card';
import { Table, TableBody, TableHead, TableHeader, TableRow } from '@/Components/ui/table';
import { router } from '@inertiajs/react';
import { useAbility } from '@/Context/AbilityContext';
import { toast } from 'sonner';

// Import sub-components
import TicketTableHeader from './TicketTableHeader';
import TicketTableFilters from './TicketTableFilters';
import TicketTableBulkActions from './TicketTableBulkActions';
import TicketTableRow from './TicketTableRow';
import TicketTablePagination from './TicketTablePagination';

interface Ticket {
  id: number;
  title: string;
  status: string;
  priority: string;
  created_at: string;
  assignee?: {
    id: number;
    name: string;
    profile_photo_url?: string;
  };
  department?: {
    id: number;
    name: string;
  };
  comments_count: number;
}

interface AssignableUser {
  id: number;
  name: string;
  email: string;
  profile_photo_url?: string;
}

interface PaginationData {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
  prev_page_url: string | null;
  next_page_url: string | null;
  data: Ticket[];
}

interface AdvancedTicketTableProps {
  posts: PaginationData;
  refreshKey?: number;
  onRefresh?: () => void;
  assignableUsers?: AssignableUser[];
}

export default function AdvancedTicketTableRefactored({
  posts,
  refreshKey = 0,
  onRefresh,
  assignableUsers = []
}: AdvancedTicketTableProps) {
  const ability = useAbility();

  // States
  const [selectedTickets, setSelectedTickets] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [assigneeFilter, setAssigneeFilter] = useState('all');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);

  // Permissions
  const canAssignPosts = ability.can('assign', 'Post');
  const canUpdateAnyPosts = ability.can('update-any', 'Post');
  const canDeleteAnyPosts = ability.can('delete-any', 'Post');
  const canUpdateStatus = ability.can('update-status', 'Post');

  // Filtered tickets
  const filteredTickets = useMemo(() => {
    return posts.data.filter(ticket => {
      const matchesSearch = ticket.title.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || ticket.status === statusFilter;
      const matchesPriority = priorityFilter === 'all' || ticket.priority === priorityFilter;
      const matchesAssignee = assigneeFilter === 'all' || 
        (assigneeFilter === 'assigned' && ticket.assignee) ||
        (assigneeFilter === 'unassigned' && !ticket.assignee);
      const matchesDepartment = departmentFilter === 'all' || 
        ticket.department?.name.toLowerCase() === departmentFilter.toLowerCase();

      return matchesSearch && matchesStatus && matchesPriority && matchesAssignee && matchesDepartment;
    });
  }, [posts.data, searchTerm, statusFilter, priorityFilter, assigneeFilter, departmentFilter]);

  // Keyboard shortcut for refresh
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey && event.key === 'r') || event.key === 'F5') {
        event.preventDefault();
        handleRefresh();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Handlers
  const handleRefresh = async () => {
    setIsRefreshing(true);
    const loadingToast = toast.loading('Đang tải lại danh sách tickets...');
    
    try {
      if (onRefresh) {
        await onRefresh();
      } else {
        router.reload();
      }
      
      toast.success('✅ Đã tải lại danh sách tickets thành công!', {
        id: loadingToast,
        duration: 2000
      });
      
      setLastRefresh(new Date());
    } catch (error) {
      console.error('❌ Error refreshing tickets:', error);
      toast.error('❌ Có lỗi xảy ra khi tải lại danh sách tickets', {
        id: loadingToast,
        duration: 3000
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleQuickAssign = (assigneeId: number) => {
    const ticketsToAssign = selectedTickets.length > 0 
      ? selectedTickets 
      : filteredTickets.map(t => t.id);
    setSelectedTickets(ticketsToAssign);
    handleBulkAssign(assigneeId);
  };

  const handleBulkAssign = (assigneeId: number) => {
    if (selectedTickets.length === 0) return;

    router.post('/admin/tickets/bulk-assign', {
      ticket_ids: selectedTickets,
      assignee_id: assigneeId,
    }, {
      onSuccess: () => {
        setSelectedTickets([]);
        toast.success('Đã giao việc thành công!');
      },
      onError: () => {
        toast.error('Có lỗi xảy ra khi giao việc');
      }
    });
  };

  const handleBulkStatusUpdate = (status: string) => {
    if (selectedTickets.length === 0) return;

    router.post('/admin/tickets/bulk-status-update', {
      ticket_ids: selectedTickets,
      status: status,
    }, {
      onSuccess: () => {
        setSelectedTickets([]);
        toast.success('Đã cập nhật trạng thái thành công!');
      },
      onError: () => {
        toast.error('Có lỗi xảy ra khi cập nhật trạng thái');
      }
    });
  };

  const handleBulkPriorityUpdate = (priority: string) => {
    if (selectedTickets.length === 0) return;

    router.post('/admin/tickets/bulk-priority-update', {
      ticket_ids: selectedTickets,
      priority: priority,
    }, {
      onSuccess: () => {
        setSelectedTickets([]);
        toast.success('Đã cập nhật độ ưu tiên thành công!');
      },
      onError: () => {
        toast.error('Có lỗi xảy ra khi cập nhật độ ưu tiên');
      }
    });
  };

  const handleSelectTicket = (ticketId: number, checked: boolean) => {
    if (checked) {
      setSelectedTickets(prev => [...prev, ticketId]);
    } else {
      setSelectedTickets(prev => prev.filter(id => id !== ticketId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedTickets(filteredTickets.map(ticket => ticket.id));
    } else {
      setSelectedTickets([]);
    }
  };

  const handleAssignSingle = (ticketId: number, assigneeId: number) => {
    setSelectedTickets([ticketId]);
    handleBulkAssign(assigneeId);
  };

  const handlePageChange = (url: string | null) => {
    if (url) {
      setIsLoading(true);
      router.visit(url, {
        preserveState: true,
        onSuccess: () => setIsLoading(false),
        onError: () => setIsLoading(false),
      });
    }
  };

  const handlePerPageChange = (perPage: string) => {
    setIsLoading(true);
    router.get(window.location.pathname, {
      per_page: perPage,
      page: 1,
    }, {
      preserveState: true,
      onSuccess: () => setIsLoading(false),
      onError: () => setIsLoading(false),
    });
  };

  return (
    <Card>
      <TicketTableHeader
        filteredTicketsCount={filteredTickets.length}
        totalTicketsCount={posts.total}
        selectedTicketsCount={selectedTickets.length}
        lastRefresh={lastRefresh}
        isRefreshing={isRefreshing}
        isLoading={isLoading}
        canAssignPosts={canAssignPosts}
        assignableUsers={assignableUsers}
        onRefresh={handleRefresh}
        onQuickAssign={handleQuickAssign}
      />

      <CardContent className="p-0">
        <div className="p-4">
          <TicketTableFilters
            searchTerm={searchTerm}
            statusFilter={statusFilter}
            priorityFilter={priorityFilter}
            assigneeFilter={assigneeFilter}
            departmentFilter={departmentFilter}
            onSearchChange={setSearchTerm}
            onStatusFilterChange={setStatusFilter}
            onPriorityFilterChange={setPriorityFilter}
            onAssigneeFilterChange={setAssigneeFilter}
            onDepartmentFilterChange={setDepartmentFilter}
          />
        </div>

        <TicketTableBulkActions
          selectedCount={selectedTickets.length}
          canAssignPosts={canAssignPosts}
          canUpdateStatus={canUpdateStatus}
          canUpdateAnyPosts={canUpdateAnyPosts}
          assignableUsers={assignableUsers}
          onBulkAssign={handleBulkAssign}
          onBulkStatusUpdate={handleBulkStatusUpdate}
          onBulkPriorityUpdate={handleBulkPriorityUpdate}
        />

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <input
                  type="checkbox"
                  checked={selectedTickets.length === filteredTickets.length && filteredTickets.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                />
              </TableHead>
              <TableHead>Tiêu đề</TableHead>
              <TableHead>Trạng thái</TableHead>
              <TableHead>Độ ưu tiên</TableHead>
              <TableHead>Người được giao</TableHead>
              <TableHead>Phòng ban</TableHead>
              <TableHead>Comments</TableHead>
              <TableHead>Ngày tạo</TableHead>
              <TableHead className="w-12">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTickets.map((ticket) => (
              <TicketTableRow
                key={ticket.id}
                ticket={ticket}
                isSelected={selectedTickets.includes(ticket.id)}
                canAssignPosts={canAssignPosts}
                canDeleteAnyPosts={canDeleteAnyPosts}
                assignableUsers={assignableUsers}
                onSelect={handleSelectTicket}
                onAssign={handleAssignSingle}
              />
            ))}
          </TableBody>
        </Table>

        <TicketTablePagination
          pagination={posts}
          isLoading={isLoading}
          onPageChange={handlePageChange}
          onPerPageChange={handlePerPageChange}
        />
      </CardContent>
    </Card>
  );
}
