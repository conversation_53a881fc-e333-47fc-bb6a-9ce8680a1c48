export const CONSTANTS = {
    MAX_NOTIFICATIONS: 10,
    ERROR_TIMEOUT: 3000,
    MAX_RETRY_ATTEMPTS: 3,
  } as const;
  
 export interface NotificationData {
      post_id?: string;
      ticket_id?: string;
      title?: string;
      message: string;
      slug?: string;
      name?: string;
      user_name?: string;
      user_email?: string;
      profile_photo_url?: string;
      categories?: any[];
      tags?: any[];
      priority?: string;
      status?: string;
      department_name?: string;
      type_notification?: string;
      comment_id?: string;
      time?: string;
    }
    
 export interface Notification {
      id: string;
      data: NotificationData;
      read_at: string | null;
      created_at: string;
      type: string;
    }
    
 export interface ErrorState {
    type: 'fetch' | 'mark-read' | 'mark-all-read' | 'connection' | 'hide';
    message: string;
    retryCount?: number;
  }