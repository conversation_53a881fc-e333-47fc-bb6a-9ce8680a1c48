import React from 'react';
import NavLink from '@/Components/NavLink';
import useRoute from '@/Hooks/useRoute';
import { router } from '@inertiajs/react';
import { useAbility } from '@/Context/AbilityContext';
import { ro } from 'date-fns/locale';
import useTypedPage from '@/Hooks/useTypedPage';

interface DesktopNavigationProps {
  roles: string;
  department: any;
}

export default function DesktopNavigation({ roles, department }: DesktopNavigationProps) {

  const permission = [
    "Admin",
    "Manager",
    "Employee",
    "Customer",
  ]

  const route = useRoute();
  const ability = useAbility();
  const page = useTypedPage();
  const userRoles = page.props.auth.user?.roles.map(r => r.name) || [];
  // console.log('DesktopNavigation - User roles:', userRoles);
  return (
    <div className="hidden md:flex flex-1 items-center justify-center max-w-2xl mx-auto">
      <div className="flex space-x-1 lg:space-x-4">
        <NavLink 
          href={route('home')}
          active={route().current('home')}
        >
          Trang chủ
        </NavLink>
        {/* <NavLink 
          href={route('tickets.index')}
          active={route().current('tickets')}
        >
          Tickets
        </NavLink> */}
        {/* {ability.can('access-admin-dashboard', 'AdminDashboard') && (
          <NavLink
            href={route('admin.dashboard')}
            active={route().current('admin.dashboard')}
          >
            Admin
          </NavLink>
        )} */}

        
        {userRoles.some(r => permission.includes(r)) && (
            <NavLink
              href={route('admin.dashboard')}
              active={route().current('admin.dashboard')}
            >
              Dashboard
            </NavLink>
          )}
        
        {/* {department && (
          <NavLink
            href={route('departments.show', {
              department: department.slug,
            })}
            active={route().current('departments.show', {
              department: department.slug,
            })}
          >
            <span className="truncate max-w-[120px] block" title={department.name}>
              {department.name}
            </span>
          </NavLink>
        )} */}
      </div>
    </div>
  );
}
