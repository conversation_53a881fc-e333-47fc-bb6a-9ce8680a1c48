<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\StoreTicketRequest;
use App\Services\TicketIntakeService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TicketController extends Controller
{
    public function __construct(
        private TicketIntakeService $ticketIntakeService
    ) {}

    /**
     * Store a new ticket from external system
     */
    public function store(StoreTicketRequest $request): JsonResponse
    {
        try {
            // Get the authenticated system user
            $systemUser = $request->user();
            
            // Create the ticket
            $result = $this->ticketIntakeService->createFromExternal(
                $systemUser,
                $request->validated()
            );

            // Handle duplicate ticket
            if ($result['status'] === 'duplicate') {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                    'data' => [
                        'ticket_id' => $result['ticket_id'],
                        'ticket_url' => $this->getTicketUrl($result['ticket_id']),
                        'status' => 'duplicate',
                    ]
                ], 409); // 409 Conflict
            }

            // Success response
            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => [
                    'ticket_id' => $result['ticket_id'],
                    'ticket_url' => $this->getTicketUrl($result['ticket_id']),
                    'status' => 'created',
                    'ticket' => [
                        'id' => $result['ticket']->id,
                        'title' => $result['ticket']->title,
                        'slug' => $result['ticket']->slug,
                        'priority' => $result['ticket']->priority,
                        'status' => $result['ticket']->status,
                        'created_at' => $result['ticket']->created_at->toISOString(),
                        'creator' => [
                            'id' => $result['ticket']->creator->id,
                            'name' => $result['ticket']->creator->name,
                            'system_type' => $result['ticket']->creator->system_type,
                        ],
                        'requester' => [
                            'name' => $result['ticket']->requester_name,
                            'email' => $result['ticket']->requester_email,
                        ],
                        'category' => $result['ticket']->category ? [
                            'id' => $result['ticket']->category->id,
                            'name' => $result['ticket']->category->name,
                        ] : null,
                        'department' => $result['ticket']->department ? [
                            'id' => $result['ticket']->department->id,
                            'name' => $result['ticket']->department->name,
                        ] : null,
                        'tags' => $result['ticket']->tags->map(function ($tag) {
                            return [
                                'id' => $tag->id,
                                'name' => $tag->name,
                            ];
                        }),
                    ]
                ]
            ], 201); // 201 Created

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (\Exception $e) {
            \Log::error('Ticket creation failed', [
                'user_id' => $request->user()?->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->validated(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error occurred while creating ticket',
                'error_code' => 'TICKET_CREATION_FAILED',
            ], 500);
        }
    }

    /**
     * Get ticket by ID (for external systems to check status)
     */
    public function show(Request $request, string $id): JsonResponse
    {
        try {
            $systemUser = $request->user();
            
            // Find ticket - only allow system users to see tickets they created
            $ticket = \App\Models\Ticket::with(['creator', 'category', 'department', 'tags', 'assignee'])
                ->where('id', $id)
                ->where('user_id', $systemUser->id) // Only tickets created by this system user
                ->first();

            if (!$ticket) {
                return response()->json([
                    'success' => false,
                    'message' => 'Ticket not found or access denied',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'ticket' => [
                        'id' => $ticket->id,
                        'title' => $ticket->title,
                        'content' => $ticket->content,
                        'slug' => $ticket->slug,
                        'priority' => $ticket->priority,
                        'status' => $ticket->status,
                        'external_id' => $ticket->external_id,
                        'source_system' => $ticket->source_system,
                        'created_at' => $ticket->created_at->toISOString(),
                        'updated_at' => $ticket->updated_at->toISOString(),
                        'creator' => [
                            'id' => $ticket->creator->id,
                            'name' => $ticket->creator->name,
                            'system_type' => $ticket->creator->system_type,
                        ],
                        'requester' => [
                            'name' => $ticket->requester_name,
                            'email' => $ticket->requester_email,
                        ],
                        'assignee' => $ticket->assignee ? [
                            'id' => $ticket->assignee->id,
                            'name' => $ticket->assignee->name,
                            'email' => $ticket->assignee->email,
                        ] : null,
                        'category' => $ticket->category ? [
                            'id' => $ticket->category->id,
                            'name' => $ticket->category->name,
                        ] : null,
                        'department' => $ticket->department ? [
                            'id' => $ticket->department->id,
                            'name' => $ticket->department->name,
                        ] : null,
                        'tags' => $ticket->tags->map(function ($tag) {
                            return [
                                'id' => $tag->id,
                                'name' => $tag->name,
                            ];
                        }),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Ticket retrieval failed', [
                'user_id' => $request->user()?->id,
                'ticket_id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error occurred while retrieving ticket',
            ], 500);
        }
    }

    /**
     * List tickets created by the authenticated system user
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $systemUser = $request->user();
            $perPage = min($request->input('per_page', 20), 100); // Max 100 per page
            
            $tickets = \App\Models\Ticket::with(['category', 'department', 'assignee'])
                ->where('user_id', $systemUser->id)
                ->when($request->input('status'), function ($query, $status) {
                    return $query->where('status', $status);
                })
                ->when($request->input('priority'), function ($query, $priority) {
                    return $query->where('priority', $priority);
                })
                ->when($request->input('external_id'), function ($query, $externalId) {
                    return $query->where('external_id', $externalId);
                })
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => [
                    'tickets' => $tickets->items(),
                    'pagination' => [
                        'current_page' => $tickets->currentPage(),
                        'last_page' => $tickets->lastPage(),
                        'per_page' => $tickets->perPage(),
                        'total' => $tickets->total(),
                        'from' => $tickets->firstItem(),
                        'to' => $tickets->lastItem(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Ticket listing failed', [
                'user_id' => $request->user()?->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error occurred while listing tickets',
            ], 500);
        }
    }

    /**
     * Get intake statistics for the authenticated system user
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $systemUser = $request->user();
            
            $filters = [];
            if ($request->input('date_from')) {
                $filters['date_from'] = $request->input('date_from');
            }
            if ($request->input('date_to')) {
                $filters['date_to'] = $request->input('date_to');
            }

            // Add filter for current system user
            $stats = $this->ticketIntakeService->getIntakeStatistics($filters);
            
            // Filter stats to only include this system user's tickets
            $userTickets = \App\Models\Ticket::where('user_id', $systemUser->id);
            
            if (!empty($filters['date_from'])) {
                $userTickets->where('created_at', '>=', $filters['date_from']);
            }
            if (!empty($filters['date_to'])) {
                $userTickets->where('created_at', '<=', $filters['date_to']);
            }

            $userStats = [
                'total_tickets' => $userTickets->count(),
                'by_priority' => $userTickets->selectRaw('priority, COUNT(*) as count')
                    ->groupBy('priority')
                    ->get()
                    ->pluck('count', 'priority')
                    ->toArray(),
                'by_status' => $userTickets->selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->get()
                    ->pluck('count', 'status')
                    ->toArray(),
            ];

            return response()->json([
                'success' => true,
                'data' => $userStats
            ]);

        } catch (\Exception $e) {
            \Log::error('Statistics retrieval failed', [
                'user_id' => $request->user()?->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error occurred while retrieving statistics',
            ], 500);
        }
    }

    /**
     * Generate ticket URL
     */
    private function getTicketUrl(int $ticketId): string
    {
        return url("/tickets/{$ticketId}");
    }
}
