@import 'tailwindcss';

@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';
@plugin 'tailwindcss-animate';
@plugin '@tailwindcss/typography';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../vendor/laravel/jetstream/**/*.blade.php';
@source '../../storage/framework/views/*.php';

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans:
    Figtree, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --color-background: hsl(var(--background));
  --color-foreground: hsl(var(--foreground));
  --color-custom-blue: #071437;
  --color-muted-text: #99a1b7;
  --color-custom-blue1: #4b5675;
  --color-content-color: #252f4a;
  --color-custom-bg: #99a1b7;

  --color-card: hsl(var(--card));
  --color-card-foreground: hsl(var(--card-foreground));

  --color-popover: hsl(var(--popover));
  --color-popover-foreground: hsl(var(--popover-foreground));

  --color-primary: hsl(var(--primary));
  --color-primary-foreground: hsl(var(--primary-foreground));

  --color-secondary: hsl(var(--secondary));
  --color-secondary-foreground: hsl(var(--secondary-foreground));

  --color-muted: hsl(var(--muted));
  --color-muted-foreground: hsl(var(--muted-foreground));

  --color-accent: hsl(var(--accent));
  --color-accent-foreground: hsl(var(--accent-foreground));

  --color-destructive: hsl(var(--destructive));
  --color-destructive-foreground: hsl(var(--destructive-foreground));

  --color-border: hsl(var(--border));
  --color-input: hsl(var(--input));
  --color-ring: hsl(var(--ring));

  --color-chart-1: hsl(var(--chart-1));
  --color-chart-2: hsl(var(--chart-2));
  --color-chart-3: hsl(var(--chart-3));
  --color-chart-4: hsl(var(--chart-4));
  --color-chart-5: hsl(var(--chart-5));

  --color-sidebar: hsl(var(--sidebar-background));
  --color-sidebar-foreground: hsl(var(--sidebar-foreground));
  --color-sidebar-primary: hsl(var(--sidebar-primary));
  --color-sidebar-primary-foreground: hsl(var(--sidebar-primary-foreground));
  --color-sidebar-accent: hsl(var(--sidebar-accent));
  --color-sidebar-accent-foreground: hsl(var(--sidebar-accent-foreground));
  --color-sidebar-border: hsl(var(--sidebar-border));
  --color-sidebar-ring: hsl(var(--sidebar-ring));
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer utilities {
  [x-cloak] {
    display: none;
  }
}

@layer base {
  :root {
        --background: 0 0% 100%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 0 0% 9%;
        --primary-foreground: 0 0% 98%;
        --secondary: 0 0% 96.1%;
        --secondary-foreground: 0 0% 9%;
        --muted: 0 0% 96.1%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 89.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
        --sidebar-background: 0 0% 98%;
        --sidebar-foreground: 240 5.3% 26.1%;
        --sidebar-primary: 240 5.9% 10%;
        --sidebar-primary-foreground: 0 0% 98%;
        --sidebar-accent: 240 4.8% 95.9%;
        --sidebar-accent-foreground: 240 5.9% 10%;
        --sidebar-border: 220 13% 91%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
  .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 14.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

@layer base {
  * {
    @apply border-border;
    }
  body {
    @apply bg-background text-foreground;
    }
}
@layer utilities {
  @keyframes upvote {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.35);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes downvote {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(0.8);
    }
    100% {
      transform: scale(1);
    }
  }

  .animate-upvote {
    animation: upvote 300ms ease-in-out;
  }

  .animate-downvote {
    animation: downvote 300ms ease-in-out;
  }
}



@layer base {
  * {
    @apply border-border outline-ring/50;
    }
  body {
    @apply bg-background text-foreground;
    }
}

/* Add smooth transition for theme changes */
* {
    transition: background-color 0.5s cubic-bezier(0.4, 0, 0.2, 1), color 0.5s cubic-bezier(0.4, 0, 0.2, 1), border-color
    0.5s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
.post-container {
    @apply flex-1 w-full max-w-5xl mx-auto mt-4 sm:mt-5 md:mt-7 px-4 sm:px-6 md:px-4 dark:bg-[#0F1014] bg-white  transition-all lg:border-l lg:pl-8 xl:pl-12;
}




.mdx-editor-container {
    min-height: 400px;
  }
  
  .mdx-editor {
    border: none !important;
    font-family: inherit;
  }
  
  .mdx-editor.view-mode {
    --mdxeditor-font-family: inherit;
  }
  
  .mdx-editor.view-mode .mdxeditor-root-contenteditable {
    padding: 2rem;
    background: transparent;
  }
  
  .mdx-editor.edit-mode .mdxeditor-root-contenteditable {
    padding: 1rem 2rem;
    min-height: 400px;
  }
  
  .mdx-editor .mdxeditor-toolbar {
    border-bottom: 1px solid #e5e7eb;
    padding: 0.75rem 1rem;
    background: #f9fafb;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-top: 2rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor p {
    margin-bottom: 1rem;
    line-height: 1.7;
    color: rgb(55 65 81);
  }
  
  .mdx-editor .mdxeditor-rich-text-editor ul,
  .mdx-editor .mdxeditor-rich-text-editor ol {
    margin: 1rem 0;
    padding-left: 1.5rem;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor li {
    margin-bottom: 0.5rem;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor blockquote {
    border-left: 4px solid #3b82f6;
    padding-left: 1rem;
    margin: 1.5rem 0;
    font-style: italic;
    background: rgba(59, 130, 246, 0.05);
    padding: 1rem;
    border-radius: 0 0.5rem 0.5rem 0;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor code {
    background: #f3f4f6;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor pre {
    background: #1f2937;
    color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1.5rem 0;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor pre code {
    background: transparent;
    padding: 0;
    color: inherit;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    border: 1px solid #e5e7eb;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor th,
  .mdx-editor .mdxeditor-rich-text-editor td {
    border: 1px solid #e5e7eb;
    padding: 0.75rem;
    text-align: left;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor th {
    background: #f9fafb;
    font-weight: 600;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor a {
    color: #3b82f6;
    text-decoration: underline;
    text-decoration-color: rgba(59, 130, 246, 0.3);
  }
  
  .mdx-editor .mdxeditor-rich-text-editor a:hover {
    color: #1d4ed8;
    text-decoration-color: #1d4ed8;
  }
  
  .mdx-editor .mdxeditor-rich-text-editor img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .mdx-editor .mdxeditor-rich-text-editor hr {
    border: none;
    border-top: 1px solid #e5e7eb;
    margin: 2rem 0;
  }
  
  @media (max-width: 640px) {
    .mdx-editor.view-mode .mdxeditor-root-contenteditable,
    .mdx-editor.edit-mode .mdxeditor-root-contenteditable {
      padding: 1rem;
    }
  }

/* Responsive improvements for ticket system */
@layer utilities {
  /* Mobile-first responsive utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  /* Fixed sidebar positioning */
  .sidebar-fixed {
    position: fixed;
    left: 0;
    width: 320px;
    z-index: 10;
  }
  
  /* Responsive sidebar heights */
  .sidebar-height {
    height: calc(100vh - 3.5rem);
  }
  
  @media (min-width: 640px) {
    .sidebar-height {
      height: calc(100vh - 4rem);
    }
  }
  
  @media (min-width: 768px) {
    .sidebar-height {
      height: calc(100vh - 4.5rem);
    }
  }
  
  @media (min-width: 1024px) {
    .sidebar-height {
      height: calc(100vh - 5rem);
    }
  }
  
  /* Content area heights */
  .content-height {
    height: calc(100vh - 7rem);
  }
  
  @media (min-width: 640px) {
    .content-height {
      height: calc(100vh - 8rem);
    }
  }
  
  @media (min-width: 768px) {
    .content-height {
      height: calc(100vh - 9rem);
    }
  }
  
  @media (min-width: 1024px) {
    .content-height {
      height: calc(100vh - 10rem);
    }
  }
  
  /* Responsive text truncation */
  .truncate-mobile {
    @apply truncate;
  }
  
  @media (min-width: 640px) {
    .truncate-mobile {
      overflow: visible;
      text-overflow: unset;
      white-space: normal;
    }
  }
  
  /* Responsive spacing */
  .responsive-padding {
    @apply p-3;
  }
  
  @media (min-width: 640px) {
    .responsive-padding {
      @apply p-6;
    }
  }
  
  /* Responsive grid for ticket cards */
  .ticket-grid {
    @apply grid gap-3;
  }
  
  @media (min-width: 640px) {
    .ticket-grid {
      @apply gap-4;
    }
  }
  
  /* Mobile sidebar overlay */
  .mobile-sidebar-overlay {
    @apply fixed inset-0 bg-black/50 z-40;
  }
  
  /* Responsive container */
  .responsive-container {
    @apply container mx-auto px-4;
  }
  
  @media (min-width: 640px) {
    .responsive-container {
      @apply px-6;
    }
  }
  
  /* Responsive flex layouts */
  .responsive-flex {
    @apply flex flex-col gap-2;
  }
  
  @media (min-width: 640px) {
    .responsive-flex {
      @apply flex-row gap-4;
    }
  }
  
  /* Responsive text sizes */
  .responsive-text-base {
    @apply text-sm;
  }
  
  @media (min-width: 640px) {
    .responsive-text-base {
      @apply text-base;
    }
  }
  
  .responsive-text-lg {
    @apply text-base;
  }
  
  @media (min-width: 640px) {
    .responsive-text-lg {
      @apply text-lg;
    }
  }
  
  /* Responsive button sizes */
  .responsive-button {
    @apply px-3 py-2 text-sm;
  }
  
  @media (min-width: 640px) {
    .responsive-button {
      @apply px-4 py-2 text-base;
    }
  }
}
