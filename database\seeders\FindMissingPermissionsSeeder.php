<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Spatie\Permission\Models\Permission;

class FindMissingPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('=== SCANNING FOR MISSING PERMISSIONS ===');
        
        // Permissions hiện có trong database
        $existingPermissions = Permission::pluck('name')->toArray();
        $this->command->info('Existing permissions: ' . count($existingPermissions));
        
        // Permissions được sử dụng trong code
        $usedPermissions = $this->scanForPermissions();
        
        // Tìm permissions thiếu
        $missingPermissions = array_diff($usedPermissions, $existingPermissions);
        
        if (empty($missingPermissions)) {
            $this->command->info('✅ No missing permissions found!');
        } else {
            $this->command->error('❌ Missing permissions found:');
            foreach ($missingPermissions as $permission) {
                $this->command->error("  - {$permission}");
            }
            
            $this->command->info('Creating missing permissions...');
            foreach ($missingPermissions as $permission) {
                Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
                $this->command->info("  ✓ Created: {$permission}");
            }
        }
        
        // Hiển thị tất cả permissions được sử dụng
        $this->command->info('=== ALL USED PERMISSIONS ===');
        sort($usedPermissions);
        foreach ($usedPermissions as $permission) {
            $exists = in_array($permission, $existingPermissions);
            $this->command->info(($exists ? '✓' : '✗') . " {$permission}");
        }
    }
    
    private function scanForPermissions(): array
    {
        $permissions = [];
        
        // Scan controllers
        $controllerPath = app_path('Http/Controllers');
        $this->scanDirectory($controllerPath, $permissions);
        
        // Scan middleware
        $middlewarePath = app_path('Http/Middleware');
        $this->scanDirectory($middlewarePath, $permissions);
        
        // Scan policies
        $policiesPath = app_path('Policies');
        if (File::exists($policiesPath)) {
            $this->scanDirectory($policiesPath, $permissions);
        }
        
        return array_unique($permissions);
    }
    
    private function scanDirectory(string $path, array &$permissions): void
    {
        if (!File::exists($path)) {
            return;
        }
        
        $files = File::allFiles($path);
        
        foreach ($files as $file) {
            if ($file->getExtension() === 'php') {
                $content = File::get($file->getPathname());
                $this->extractPermissions($content, $permissions);
            }
        }
    }
    
    private function extractPermissions(string $content, array &$permissions): void
    {
        // Patterns để tìm permissions
        $patterns = [
            // can('permission-name')
            "/->can\(['\"]([^'\"]+)['\"]\)/",
            // cannot('permission-name')
            "/->cannot\(['\"]([^'\"]+)['\"]\)/",
            // Gate::authorize('permission-name')
            "/Gate::authorize\(['\"]([^'\"]+)['\"]\)/",
            // $this->authorize('permission-name')
            "/\$this->authorize\(['\"]([^'\"]+)['\"]\)/",
            // middleware('can:permission-name')
            "/middleware\(['\"]can:([^'\"]+)['\"]\)/",
            // 'can:permission-name'
            "/'can:([^'\"]+)'/",
            // hasPermissionTo('permission-name')
            "/->hasPermissionTo\(['\"]([^'\"]+)['\"]\)/",
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches)) {
                foreach ($matches[1] as $permission) {
                    // Skip policy methods (không phải permissions)
                    if (!in_array($permission, ['create', 'view', 'update', 'delete', 'viewAny', 'restore', 'forceDelete'])) {
                        $permissions[] = $permission;
                    }
                }
            }
        }
    }
}
