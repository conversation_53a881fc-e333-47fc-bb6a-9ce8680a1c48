<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Post;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class NewSystemCheckSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('=== NEW PERMISSION SYSTEM CHECK ===');
        
        // Check roles
        $this->command->info('=== ROLES ===');
        $roles = Role::with('permissions')->get();
        foreach ($roles as $role) {
            $this->command->info("✓ {$role->name} ({$role->permissions->count()} permissions)");
        }
        
        // Check permissions
        $this->command->info('=== PERMISSIONS ===');
        $permissions = Permission::all();
        foreach ($permissions as $permission) {
            $this->command->info("✓ {$permission->name}");
        }
        
        // Check users and their permissions
        $this->command->info('=== USERS AND ROLES ===');
        $testUsers = [
            '<EMAIL>' => 'Admin',
            '<EMAIL>' => 'Manager', 
            '<EMAIL>' => 'Employee',
            '<EMAIL>' => 'Customer'
        ];
        
        foreach ($testUsers as $email => $expectedRole) {
            $user = User::where('email', $email)->first();
            if ($user) {
                $actualRoles = $user->roles->pluck('name')->join(', ');
                $canAccessAdmin = $user->can('access-admin-dashboard');
                $canViewAnyPosts = $user->can('view-any-posts');
                $canViewDeptPosts = $user->can('view-department-posts');
                $canManageUsers = $user->can('manage-users');
                
                $this->command->info("✓ {$user->name} ({$email})");
                $this->command->info("  Roles: {$actualRoles}");
                $this->command->info("  Admin Dashboard: " . ($canAccessAdmin ? '✓' : '✗'));
                $this->command->info("  View Any Posts: " . ($canViewAnyPosts ? '✓' : '✗'));
                $this->command->info("  View Dept Posts: " . ($canViewDeptPosts ? '✓' : '✗'));
                $this->command->info("  Manage Users: " . ($canManageUsers ? '✓' : '✗'));
            } else {
                $this->command->error("✗ User {$email} not found");
            }
        }
        
        $this->command->info('=== ROLE SUMMARY ===');
        $this->command->info('Admin: Có toàn bộ quyền');
        $this->command->info('Manager: Quản lý phòng ban của mình');
        $this->command->info('Employee: Xem tickets phòng mình + unassigned');
        $this->command->info('Customer: Chỉ tạo tickets');
        
        $this->command->info('=== LOGIN INFO ===');
        $this->command->info('- Admin: <EMAIL> / password123');
        $this->command->info('- Manager: <EMAIL> / manager123');
        $this->command->info('- Employee: <EMAIL> / employee123');
        $this->command->info('- Customer: <EMAIL> / customer123');
    }
}
