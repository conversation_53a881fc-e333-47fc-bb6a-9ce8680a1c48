<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_audit_logs', function (Blueprint $table) {
            $table->id();
            
            // Request identification
            $table->string('request_id')->unique();
            $table->foreignId('user_id')->nullable()->constrained('users')->nullOnDelete();
            $table->string('token_name')->nullable();
            $table->string('token_id')->nullable();
            
            // Request details
            $table->string('method', 10);
            $table->string('endpoint');
            $table->string('ip_address', 45);
            $table->text('user_agent')->nullable();
            
            // Request/Response data
            $table->json('request_headers')->nullable();
            $table->longText('request_payload')->nullable();
            $table->integer('response_status');
            $table->longText('response_body')->nullable();
            
            // Timing and performance
            $table->decimal('response_time_ms', 8, 2)->nullable();
            $table->timestamp('requested_at');
            
            // Rate limiting info
            $table->integer('rate_limit_remaining')->nullable();
            $table->timestamp('rate_limit_reset_at')->nullable();
            
            // Error tracking
            $table->string('error_type')->nullable();
            $table->text('error_message')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance and querying
            $table->index(['user_id', 'requested_at']);
            $table->index(['endpoint', 'requested_at']);
            $table->index(['response_status', 'requested_at']);
            $table->index(['ip_address', 'requested_at']);
            $table->index('token_id');
            $table->index('requested_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_audit_logs');
    }
};
