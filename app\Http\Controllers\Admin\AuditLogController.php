<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\AuditLogService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class AuditLogController extends Controller
{

    use AuthorizesRequests;
    public function __construct(
        private AuditLogService $auditService
    ) {}

    /**
     * Display API audit logs
     */
    public function apiLogs(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $filters = $request->only([
            'user_id', 'method', 'endpoint', 'status_code', 'ip_address',
            'date_from', 'date_to', 'error_type', 'min_response_time', 'max_response_time'
        ]);

        $perPage = min($request->input('per_page', 50), 100);
        
        $logs = $this->auditService->searchApiLogs($filters, $perPage);
        $stats = $this->auditService->getApiUsageStats($filters);

        return Inertia::render('Admin/ApiAuditLogs', [
            'logs' => $logs,
            'stats' => $stats,
            'filters' => $filters,
        ]);
    }

    /**
     * Display token audit logs
     */
    public function tokenLogs(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $filters = $request->only([
            'tokenable_id', 'action', 'performed_by', 'token_name', 'date_from', 'date_to'
        ]);

        $perPage = min($request->input('per_page', 50), 100);
        
        $logs = $this->auditService->searchTokenLogs($filters, $perPage);

        return Inertia::render('Admin/TokenAuditLogs', [
            'logs' => $logs,
            'filters' => $filters,
        ]);
    }

    /**
     * Display security alerts
     */
    public function securityAlerts(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $hours = $request->input('hours', 24);
        $alerts = $this->auditService->getSecurityAlerts($hours);

        return Inertia::render('Admin/SecurityAlerts', [
            'alerts' => $alerts,
            'hours' => $hours,
        ]);
    }

    /**
     * Export API logs to CSV
     */
    public function exportApiLogs(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $filters = $request->only([
            'user_id', 'method', 'endpoint', 'status_code', 'ip_address',
            'date_from', 'date_to', 'error_type', 'min_response_time', 'max_response_time'
        ]);

        $csv = $this->auditService->exportApiLogs($filters);
        
        $filename = 'api_audit_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        return response($csv)
            ->header('Content-Type', 'text/csv')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }

    /**
     * Get API usage statistics
     */
    public function getApiStats(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $filters = $request->only([
            'user_id', 'date_from', 'date_to'
        ]);

        $stats = $this->auditService->getApiUsageStats($filters);

        return response()->json($stats);
    }

    /**
     * Get real-time monitoring data
     */
    public function getMonitoringData(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $hours = $request->input('hours', 1);
        
        // Get recent activity
        $recentLogs = $this->auditService->searchApiLogs([
            'date_from' => now()->subHours($hours),
        ], 100);

        // Get alerts
        $alerts = $this->auditService->getSecurityAlerts($hours);

        // Get stats
        $stats = $this->auditService->getApiUsageStats([
            'date_from' => now()->subHours($hours),
        ]);

        return response()->json([
            'recent_logs' => $recentLogs->items(),
            'alerts' => $alerts,
            'stats' => $stats,
            'timestamp' => now()->toISOString(),
        ]);
    }
}
