<?php

namespace App\Providers;

use App\Events\NewTicketCreated;
use App\Events\NewPostCreated;
use App\Listeners\SendTicketNotification;
use App\Listeners\SendPostNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        NewTicketCreated::class => [
            SendTicketNotification::class,
        ],
        NewPostCreated::class => [
            SendPostNotification::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
