# Ticket Intake API Documentation

## Overview

The Ticket Intake API allows external systems to create and manage support tickets programmatically. It features comprehensive authentication, rate limiting, audit logging, and token management capabilities.

## Features

- **System User Authentication**: Dedicated system users with API token-based authentication
- **Advanced Rate Limiting**: Per-user rate limiting with configurable tiers and burst protection
- **Comprehensive Audit Logging**: Full request/response logging with security monitoring
- **Token Management**: Web dashboard and CLI tools for token lifecycle management
- **Duplicate Prevention**: Automatic detection and prevention of duplicate tickets
- **Flexible Categorization**: Support for categories, tags, and custom metadata

## Authentication

### System Users

External systems are represented as "System Users" in the platform. Each system user:

- Has a unique email and system type (e.g., `payment`, `crm`, `monitoring`)
- Can have multiple API tokens with different permissions
- Has configurable rate limits based on tier (`standard`, `premium`, `enterprise`)
- Can be restricted to specific IP addresses

### API Tokens

All API requests must include a Bearer token in the Authorization header:

```
Authorization: Bearer <your-api-token>
```

Tokens can be:
- Created via admin dashboard or CLI commands
- Configured with expiration dates
- Restricted to specific IP addresses
- Rotated for security purposes
- Monitored for usage statistics

## Rate Limiting

### Tiers and Limits

| Tier | Per Minute | Burst | Daily Limit |
|------|------------|-------|-------------|
| Standard | 60 | 10 | 10,000 |
| Premium | 120 | 20 | 50,000 |
| Enterprise | 300 | 50 | 200,000 |

### Rate Limit Headers

All responses include rate limit information:

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1692123456
X-RateLimit-Tier: standard
X-RateLimit-Daily-Limit: 10000
X-RateLimit-Daily-Usage: 150
```

### Rate Limit Exceeded Response

```json
{
  "error": "Rate limit exceeded",
  "message": "Rate limit exceeded",
  "retry_after": 60,
  "rate_limit": {
    "tier": "standard",
    "limits": {
      "per_minute": 60,
      "burst": 10,
      "daily": 10000
    },
    "usage": {
      "daily": 150,
      "minute_remaining": 0,
      "burst_remaining": 0
    },
    "reset_times": {
      "minute_reset": 1692123456,
      "burst_reset": 1692123466,
      "daily_reset": 1692209856
    },
    "violations": 3
  }
}
```

## API Endpoints

### Base URL

```
https://your-domain.com/api/v1
```

### Create Ticket

**POST** `/tickets`

Creates a new support ticket.

#### Request Body

```json
{
  "title": "Payment processing error",
  "content": "Customer unable to complete payment with credit card ending in 1234",
  "requester_name": "John Doe",
  "requester_email": "<EMAIL>",
  "priority": "high",
  "category_key": "payment-issues",
  "tags": ["payment", "credit-card", "urgent"],
  "external_id": "PAY-ERROR-123456",
  "product_id": "PROD-001",
  "product_name": "Premium Subscription",
  "metadata": {
    "transaction_id": "TXN-789012",
    "amount": 99.99,
    "currency": "USD"
  },
  "attachments": [
    {
      "name": "error_screenshot.png",
      "url": "https://external-system.com/files/error_screenshot.png",
      "type": "image/png"
    }
  ]
}
```

#### Required Fields

- `title`: Ticket title (max 255 characters)
- `content`: Ticket description (max 65535 characters)
- `requester_name`: Name of the person requesting support
- `requester_email`: Email of the person requesting support

#### Optional Fields

- `priority`: `low`, `medium`, `high`, `urgent` (default: `medium`)
- `category_key`: Category identifier for automatic categorization
- `category_id`: Direct category ID reference
- `department_id`: Department ID for routing
- `tags`: Array of tags (max 10 tags, 50 characters each)
- `external_id`: Unique identifier from source system (for duplicate prevention)
- `source_system`: Override default source system
- `product_id`: Product identifier
- `product_name`: Product name
- `metadata`: Additional custom data
- `attachments`: Array of attachment metadata (max 5 attachments)

#### Success Response (201 Created)

```json
{
  "success": true,
  "message": "Ticket created successfully",
  "data": {
    "ticket_id": 12345,
    "ticket_url": "https://your-domain.com/tickets/12345",
    "status": "created",
    "ticket": {
      "id": 12345,
      "title": "Payment processing error",
      "slug": "payment-processing-error",
      "priority": "high",
      "status": "open",
      "created_at": "2023-08-15T10:30:00Z",
      "creator": {
        "id": 100,
        "name": "Payment System",
        "system_type": "payment"
      },
      "requester": {
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "category": {
        "id": 5,
        "name": "Payment Issues"
      },
      "department": {
        "id": 2,
        "name": "Billing Support"
      },
      "tags": [
        {"id": 10, "name": "payment"},
        {"id": 15, "name": "credit-card"},
        {"id": 20, "name": "urgent"}
      ]
    }
  }
}
```

#### Duplicate Response (409 Conflict)

```json
{
  "success": false,
  "message": "Ticket with this external ID already exists for this source system",
  "data": {
    "ticket_id": 12340,
    "ticket_url": "https://your-domain.com/tickets/12340",
    "status": "duplicate"
  }
}
```

### Get Ticket

**GET** `/tickets/{id}`

Retrieves a specific ticket created by the authenticated system user.

#### Success Response (200 OK)

```json
{
  "success": true,
  "data": {
    "ticket": {
      "id": 12345,
      "title": "Payment processing error",
      "content": "Customer unable to complete payment...",
      "slug": "payment-processing-error",
      "priority": "high",
      "status": "in_progress",
      "external_id": "PAY-ERROR-123456",
      "source_system": "payment",
      "created_at": "2023-08-15T10:30:00Z",
      "updated_at": "2023-08-15T11:15:00Z",
      "creator": {
        "id": 100,
        "name": "Payment System",
        "system_type": "payment"
      },
      "requester": {
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "assignee": {
        "id": 25,
        "name": "Support Agent",
        "email": "<EMAIL>"
      },
      "category": {
        "id": 5,
        "name": "Payment Issues"
      },
      "department": {
        "id": 2,
        "name": "Billing Support"
      },
      "tags": [
        {"id": 10, "name": "payment"},
        {"id": 15, "name": "credit-card"}
      ]
    }
  }
}
```

### List Tickets

**GET** `/tickets`

Lists tickets created by the authenticated system user.

#### Query Parameters

- `per_page`: Number of tickets per page (max 100, default 20)
- `status`: Filter by status (`open`, `in_progress`, `resolved`, `closed`)
- `priority`: Filter by priority (`low`, `medium`, `high`, `urgent`)
- `external_id`: Filter by external ID

#### Example Request

```
GET /api/v1/tickets?status=open&priority=high&per_page=50
```

#### Success Response (200 OK)

```json
{
  "success": true,
  "data": {
    "tickets": [
      {
        "id": 12345,
        "title": "Payment processing error",
        "priority": "high",
        "status": "open",
        "created_at": "2023-08-15T10:30:00Z",
        "category": {"id": 5, "name": "Payment Issues"},
        "assignee": null
      }
    ],
    "pagination": {
      "current_page": 1,
      "last_page": 3,
      "per_page": 50,
      "total": 125,
      "from": 1,
      "to": 50
    }
  }
}
```

### Get Statistics

**GET** `/tickets/statistics`

Returns ticket statistics for the authenticated system user.

#### Query Parameters

- `date_from`: Start date for statistics (ISO 8601 format)
- `date_to`: End date for statistics (ISO 8601 format)

#### Success Response (200 OK)

```json
{
  "success": true,
  "data": {
    "total_tickets": 1250,
    "by_priority": {
      "low": 300,
      "medium": 600,
      "high": 280,
      "urgent": 70
    },
    "by_status": {
      "open": 150,
      "in_progress": 200,
      "resolved": 800,
      "closed": 100
    }
  }
}
```

## Error Handling

### Error Response Format

```json
{
  "success": false,
  "message": "Error description",
  "errors": {
    "field_name": ["Validation error message"]
  },
  "error_code": "ERROR_CODE"
}
```

### Common Error Codes

- `VALIDATION_FAILED` (422): Request validation failed
- `UNAUTHORIZED` (401): Invalid or missing authentication token
- `FORBIDDEN` (403): Insufficient permissions
- `NOT_FOUND` (404): Resource not found
- `RATE_LIMIT_EXCEEDED` (429): Rate limit exceeded
- `DUPLICATE_TICKET` (409): Ticket with external_id already exists
- `INTERNAL_ERROR` (500): Server error

## Security

### IP Restrictions

System users can be restricted to specific IP addresses. Requests from unauthorized IPs will be rejected with a 403 Forbidden response.

### Token Security

- Tokens should be stored securely and never exposed in client-side code
- Use HTTPS for all API requests
- Rotate tokens regularly using the admin dashboard or CLI commands
- Monitor token usage through audit logs

### Audit Logging

All API requests are logged with:
- Request details (method, endpoint, headers, payload)
- Response details (status, body, response time)
- User identification and token information
- IP address and user agent
- Rate limiting information
- Error details for failed requests

## CLI Commands

### Issue Token

```bash
php artisan system:issue-token <EMAIL> token-name \
  --description="Token description" \
  --expires="2024-12-31 23:59:59" \
  --type=api \
  --ips=***********,********
```

### Revoke Token

```bash
php artisan system:revoke-token token-name \
  --user=<EMAIL> \
  --reason="Security rotation"
```

### Rotate Token

```bash
php artisan system:rotate-token token-name \
  --user=<EMAIL> \
  --new-name=token-name-rotated \
  --reason="Scheduled rotation"
```

### List Tokens

```bash
php artisan system:list-tokens \
  --user=<EMAIL> \
  --active-only \
  --format=table
```

## Admin Dashboard

The admin dashboard provides:

- **System User Management**: Create, edit, and manage system users
- **Token Management**: Issue, revoke, rotate, and monitor API tokens
- **Audit Logs**: Search and analyze API request logs
- **Security Alerts**: Monitor for suspicious activity and security issues
- **Usage Statistics**: View API usage patterns and performance metrics

Access the admin dashboard at `/admin` with appropriate permissions.

## Support

For technical support or questions about the API:

1. Check the audit logs for request/response details
2. Review rate limiting status and adjust if needed
3. Verify token permissions and expiration
4. Contact the development team with specific error details

## Setup Guide

### 1. Run Migrations

```bash
php artisan migrate
```

### 2. Create System User

```bash
php artisan tinker
```

```php
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

User::create([
    'name' => 'Payment System',
    'email' => '<EMAIL>',
    'password' => Hash::make(Str::random(40)),
    'is_system_user' => true,
    'system_type' => 'payment',
    'system_description' => 'Payment gateway integration',
    'rate_limit_tier' => 'standard',
    'is_active' => true,
]);
```

### 3. Issue API Token

```bash
php artisan system:issue-token <EMAIL> payment-api-token \
  --description="Payment system API access" \
  --type=api
```

### 4. Test API

```bash
curl -X POST https://your-domain.com/api/v1/tickets \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test ticket",
    "content": "This is a test ticket",
    "requester_name": "Test User",
    "requester_email": "<EMAIL>",
    "priority": "medium"
  }'
```

## Changelog

### Version 1.0.0 (2023-08-15)

- Initial release
- System user authentication
- Comprehensive rate limiting
- Audit logging and monitoring
- Token management dashboard
- CLI commands for token operations
