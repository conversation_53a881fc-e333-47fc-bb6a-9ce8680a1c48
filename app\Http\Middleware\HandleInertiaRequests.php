<?php

namespace App\Http\Middleware;

use Illuminate\Http\Request;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Define the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @return array<string, mixed>
     */
    public function share(Request $request): array
    {
        $department = null;
        $role = null;
        $permissions = null;

        if (auth()->check()) {
            $userDepartment = auth()->user()->departments->first();
            $role = auth()->user()->getRoleNames();
            $permissions = auth()->user()->getPermissionsViaRoles();
            if ($userDepartment) {
                $department = [
                    'name' => $userDepartment->name,
                    'slug' => $userDepartment->slug,
                ];
            }
        }

        return array_merge(parent::share($request), [
            'auth' => [
                'user' => $request->user() ? [
                    'id' => $request->user()->id,
                    'name' => $request->user()->name,
                    'email' => $request->user()->email,
                    'profile_photo_url' => $request->user()->profile_photo_url,
                    'permissions' => $request->user()->getAllPermissions()->map(function($permission) {
                        return [
                            'id' => $permission->id,
                            'name' => $permission->name
                        ];
                    }),
                    'roles' => $request->user()->roles->map(function($role) {
                        return [
                            'id' => $role->id,
                            'name' => $role->name,
                            'permissions' => $role->permissions->map(function($permission) {
                                return [
                                    'id' => $permission->id,
                                    'name' => $permission->name
                                ];
                            })
                        ];
                    }),
                    'departments' => $request->user()->departments,
                ] : null,
            ],
            'department' => $department,
            'roles' => $role,
            'permissions' => $permissions,
        ]);
    }
}
