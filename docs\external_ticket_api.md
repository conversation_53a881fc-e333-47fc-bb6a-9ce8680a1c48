# Giải pháp API Hợp nhất để Nhận Ticket từ Bên ngoài

## 1. <PERSON><PERSON><PERSON> cảnh và Vấn đề

Hiện tại, hệ thống đang sử dụng nhiều API endpoint riêng lẻ để nhận ticket từ các dịch vụ khác nhau như OpenAI và Claude.

-   **Endpoint OpenAI:** `POST /api/tickets` (Xác thực: Bearer <PERSON>)
-   **Endpoint Claude:** `POST /api/v2/tickets` (Xác thực: X-API-Key header)

Cách tiếp cận này dẫn đến các vấn đề sau:
-   **Không nhất quán:** Các endpoint có <PERSON>, phương thức xác thực và cấu trúc dữ liệu (payload) khác nhau.
-   **<PERSON>h<PERSON> bảo trì:** Việc quản lý nhiều endpoint làm tăng sự phức tạp khi cần cập nhật hoặc sửa lỗi.
-   **<PERSON><PERSON><PERSON> mở rộng:** T<PERSON><PERSON> hợp thêm một hệ thống mới đòi hỏi phải xây dựng hoặc điều chỉnh API, thay vì sử dụng lại một cấu trúc chung.

Tài liệu `unified_ticket_api_guide.md` đã đề xuất một hướng đi đúng đắn là chuẩn hóa quy trình này.

## 2. Giải pháp Đề xuất: API Hợp nhất

Để giải quyết các vấn đề trên, chúng tôi đề xuất xây dựng một **API endpoint hợp nhất** duy nhất, được thiết kế để trở thành cổng nhận ticket tiêu chuẩn từ mọi hệ thống bên ngoài (ví dụ: website, ứng dụng của đối tác, công cụ giám sát).

### Endpoint

-   **URL:** `POST /api/tickets/external`
-   **Method:** `POST`

### Xác thực

-   **Phương thức:** `Bearer Token`
-   Mỗi hệ thống/đối tác bên ngoài sẽ được cấp một API Key (token) riêng biệt. Điều này giúp:
    -   Tăng cường bảo mật.
    -   Dễ dàng quản lý và thu hồi quyền truy cập khi cần thiết.

### Cấu trúc Dữ liệu (Request Body)

Payload được thiết kế để vừa đầy đủ thông tin, vừa linh hoạt và có khả năng mở rộng.

```json
{
  "title": "string|required|max:255",
  "description": "string|required",
  "source": "string|required|max:100",
  "priority": "string|optional|in:Low,Medium,High,Urgent",
  "category": {
    "id": "integer|optional",
    "name": "string|optional"
  },
  "tags": [
    "string"
  ],
  "requester": {
    "name": "string|required|max:255",
    "email": "string|required|email"
  },
  "custom_fields": {
    "key": "value"
  }
}
```

### Logic Xử lý Nâng cao

#### Xử lý Category linh hoạt

Để tránh việc hệ thống bên ngoài phải biết `id` của category, API sẽ xử lý theo logic ưu tiên sau:

1.  **Ưu tiên `category.id`:** Nếu `id` được cung cấp, hệ thống sẽ dùng nó để tìm category.
2.  **Dùng đến `category.name`:** Nếu không có `id`, hệ thống sẽ tìm category dựa trên `name`.
3.  **Sử dụng Category Mặc định:** Nếu không tìm thấy category qua cả `id` và `name`, ticket sẽ được tự động gán vào một **category mặc định** (ví dụ: "Chưa phân loại"). Điều này đảm bảo ticket luôn được tạo thành công và có thể được phân loại lại sau bởi nhân viên hỗ trợ.

#### Xử lý Tag động (Tìm hoặc Tạo)

Để mang lại sự linh hoạt, hệ thống sẽ không yêu cầu tag phải tồn tại trước.

1.  Với mỗi chuỗi `tag` trong mảng `tags` nhận được, hệ thống sẽ tìm kiếm tag tương ứng (không phân biệt chữ hoa/thường).
2.  **Nếu tag đã tồn tại:** Sử dụng tag đó.
3.  **Nếu tag chưa tồn tại:** Tự động tạo một tag mới trong cơ sở dữ liệu.
4.  Gắn tất cả các tag (cũ và mới) vào ticket vừa tạo.

### Luồng Xử lý Chính

1.  **Nhận Request:** API nhận request `POST` tại `/api/tickets/external`.
2.  **Xác thực:** Kiểm tra `Bearer Token` trong header `Authorization`.
3.  **Validate Dữ liệu:** Kiểm tra payload dựa trên các quy tắc đã định nghĩa.
4.  **Xử lý Người dùng:** Tìm kiếm người dùng trong cơ sở dữ liệu bằng `requester.email`. Nếu không tìm thấy, tạo một tài khoản người dùng mới.
5.  **Xử lý Category & Tags:** Áp dụng logic xử lý nâng cao như đã mô tả ở trên.
6.  **Tạo Ticket:** Tạo một bản ghi ticket mới và liên kết với người dùng, category và tags tương ứng.
7.  **Phản hồi:** Trả về mã trạng thái `201 Created` cùng với thông báo thành công và ID của ticket vừa tạo.

**Ví dụ Response:**
```json
{
  "message": "Ticket created successfully.",
  "ticket_id": "TICKET-789"
}
```