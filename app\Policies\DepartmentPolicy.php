<?php

namespace App\Policies;

use App\Models\Departments;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class DepartmentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any departments.
     */
    public function viewAny(?User $user): bool
    {
        // Tất cả mọi người có thể xem danh sách departments
        return true;
    }

    /**
     * Determine whether the user can view the department.
     */
    public function view(?User $user, Departments $department): bool
    {
        // Tất cả mọi người có thể xem department
        return true;
    }

    /**
     * Determine whether the user can create departments.
     */
    public function create(User $user): bool
    {
        return $user->can('manage-departments');
    }

    /**
     * Determine whether the user can update the department.
     */
    public function update(User $user, Departments $department): bool
    {
        return $user->can('manage-departments');
    }

    /**
     * Determine whether the user can delete the department.
     */
    public function delete(User $user, Departments $department): bool
    {
        return $user->can('manage-departments');
    }

    /**
     * Determine whether the user can assign users to departments.
     */
    public function assignUsers(User $user, Departments $department): bool
    {
        return $user->can('assign-users-to-departments');
    }

    /**
     * Determine whether the user can view department posts.
     */
    public function viewDepartmentPosts(User $user, Departments $department): bool
    {
        // Admin/Employee có thể xem posts của tất cả departments
        if ($user->can('view-any-posts')) {
            return true;
        }

        // User thuộc department có thể xem posts của department đó
        return $user->departments()->where('departments.id', $department->id)->exists();
    }

    /**
     * Determine whether the user can restore the department.
     */
    public function restore(User $user, Departments $department): bool
    {
        return $user->can('manage-departments');
    }

    /**
     * Determine whether the user can permanently delete the department.
     */
    public function forceDelete(User $user, Departments $department): bool
    {
        // Chỉ Super Admin có thể force delete
        return $user->hasRole('Super Admin');
    }
}
