import React from 'react';
import { But<PERSON> } from '@/Components/ui/button';
import { UserPlus, Settings, AlertTriangle } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/Components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';

interface AssignableUser {
  id: number;
  name: string;
  email: string;
  profile_photo_url?: string;
}

interface TicketTableBulkActionsProps {
  selectedCount: number;
  canAssignPosts: boolean;
  canUpdateStatus: boolean;
  canUpdateAnyPosts: boolean;
  assignableUsers: AssignableUser[];
  onBulkAssign: (assigneeId: number) => void;
  onBulkStatusUpdate: (status: string) => void;
  onBulkPriorityUpdate: (priority: string) => void;
}

export default function TicketTableBulkActions({
  selectedCount,
  canAssignPosts,
  canUpdateStatus,
  canUpdateAnyPosts,
  assignableUsers,
  onBulkAssign,
  onBulkStatusUpdate,
  onBulkPriorityUpdate,
}: TicketTableBulkActionsProps) {
  if (selectedCount === 0) {
    return null;
  }

  return (
    <div className="flex items-center gap-2 p-4 bg-muted/50 border-b">
      <span className="text-sm text-muted-foreground">
        {selectedCount} tickets selected
      </span>

      {canAssignPosts && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" disabled={!assignableUsers.length}>
              <UserPlus className="h-4 w-4 mr-2" />
              Giao việc
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Giao việc cho</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {assignableUsers.length > 0 ? (
              assignableUsers.map(assignee => (
                <DropdownMenuItem
                  key={assignee.id}
                  onClick={() => onBulkAssign(assignee.id)}
                  className="cursor-pointer"
                >
                  <Avatar className="h-6 w-6 mr-2">
                    <AvatarImage src={assignee.profile_photo_url || ''} alt={assignee.name} />
                    <AvatarFallback>{assignee.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  {assignee.name}
                </DropdownMenuItem>
              ))
            ) : (
              <div className="px-2 py-1.5 text-sm text-muted-foreground">
                Không có nhân viên nào để giao việc
              </div>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {canUpdateStatus && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Trạng thái
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Thay đổi trạng thái thành</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onBulkStatusUpdate('open')}>
              Mở
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkStatusUpdate('in_progress')}>
              Đang xử lý
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkStatusUpdate('resolved')}>
              Đã giải quyết
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkStatusUpdate('closed')}>
              Đóng
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {canUpdateAnyPosts && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Độ ưu tiên
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>Thay đổi độ ưu tiên thành</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onBulkPriorityUpdate('urgent')}>
              Khẩn cấp
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkPriorityUpdate('high')}>
              Cao
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkPriorityUpdate('medium')}>
              Trung bình
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onBulkPriorityUpdate('low')}>
              Thấp
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
}
