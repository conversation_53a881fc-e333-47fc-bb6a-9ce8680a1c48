<?php

namespace App\Console\Commands;

use App\Models\TokenAuditLog;
use App\Models\User;
use Illuminate\Console\Command;
use Laravel\Sanctum\PersonalAccessToken;

class RevokeSystemToken extends Command
{
    protected $signature = 'system:revoke-token 
                            {token_name : Name of the token to revoke}
                            {--user= : Email of the system user (optional if token name is unique)}
                            {--reason= : Reason for revocation}';

    protected $description = 'Revoke a Sanctum API token for a system user';

    public function handle()
    {
        $tokenName = $this->argument('token_name');
        $userEmail = $this->option('user');
        $reason = $this->option('reason') ?? 'Revoked via CLI command';

        // Build query for the token
        $query = PersonalAccessToken::where('name', $tokenName);

        if ($userEmail) {
            $user = User::where('email', $userEmail)->first();
            if (!$user) {
                $this->error("User with email '{$userEmail}' not found.");
                return self::FAILURE;
            }
            $query->where('tokenable_id', $user->id);
        }

        $tokens = $query->get();

        if ($tokens->isEmpty()) {
            $this->error("No tokens found with name '{$tokenName}'" . ($userEmail ? " for user '{$userEmail}'" : ''));
            return self::FAILURE;
        }

        if ($tokens->count() > 1 && !$userEmail) {
            $this->error("Multiple tokens found with name '{$tokenName}'. Please specify --user option.");
            $this->line("Found tokens for users:");
            foreach ($tokens as $token) {
                $user = User::find($token->tokenable_id);
                $this->line("- {$user->email} ({$user->name})");
            }
            return self::FAILURE;
        }

        foreach ($tokens as $token) {
            $user = User::find($token->tokenable_id);
            
            // Log the revocation before deleting
            TokenAuditLog::logTokenOperation(
                $token->id,
                $token->name,
                $token->tokenable_id,
                'revoked',
                null, // CLI command, no user
                $reason,
                [
                    'last_used_at' => $token->last_used_at,
                    'created_at' => $token->created_at,
                ],
                null,
                [
                    'command' => 'system:revoke-token',
                    'revocation_method' => 'cli',
                ]
            );

            // Delete the token
            $token->delete();

            $this->info("Token '{$tokenName}' revoked for user: {$user->name} ({$user->email})");
        }

        return self::SUCCESS;
    }
}
