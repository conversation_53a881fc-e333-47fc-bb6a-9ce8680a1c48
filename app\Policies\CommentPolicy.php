<?php

namespace App\Policies;

use App\Models\Comments;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class CommentPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any comments.
     */
    public function viewAny(User $user): bool
    {
        // Tất cả user đã đăng nhập có thể xem comments
        return true;
    }

    /**
     * Determine whether the user can view the comment.
     */
    public function view(?User $user, Comments $comment): bool
    {
        // Tất cả mọi người có thể xem comments (kể cả guest)
        return true;
    }

    /**
     * Determine whether the user can create comments.
     */
    public function create(User $user): bool
    {
        return $user->can('create-comments');
    }

    /**
     * Determine whether the user can update the comment.
     */
    public function update(User $user, Comments $comment): bool
    {
        // User có thể update comment của chính mình
        if ($user->id === $comment->user_id) {
            return true;
        }

        // Admin/Employee có thể moderate comments
        return $user->can('moderate-comments');
    }

    /**
     * Determine whether the user can delete the comment.
     */
    public function delete(User $user, Comments $comment): bool
    {
        // User có thể xóa comment của chính mình
        if ($user->id === $comment->user_id) {
            return true;
        }

        // Admin/Employee có thể moderate comments
        return $user->can('moderate-comments');
    }

    /**
     * Determine whether the user can moderate comments.
     */
    public function moderate(User $user): bool
    {
        return $user->can('moderate-comments');
    }

    /**
     * Determine whether the user can restore the comment.
     */
    public function restore(User $user, Comments $comment): bool
    {
        return $user->can('moderate-comments');
    }

    /**
     * Determine whether the user can permanently delete the comment.
     */
    public function forceDelete(User $user, Comments $comment): bool
    {
        // Chỉ Super Admin có thể force delete
        return $user->hasRole('Super Admin');
    }
}
