<?php

namespace App\Console\Commands;

use App\Models\TokenAuditLog;
use App\Models\User;
use Illuminate\Console\Command;
use Laravel\Sanctum\PersonalAccessToken;

class RotateSystemToken extends Command
{
    protected $signature = 'system:rotate-token 
                            {token_name : Name of the token to rotate}
                            {--user= : Email of the system user (optional if token name is unique)}
                            {--reason= : Reason for rotation}
                            {--new-name= : New name for the rotated token}';

    protected $description = 'Rotate a Sanctum API token for a system user';

    public function handle()
    {
        $tokenName = $this->argument('token_name');
        $userEmail = $this->option('user');
        $reason = $this->option('reason') ?? 'Token rotation via CLI command';
        $newName = $this->option('new-name') ?? $tokenName . '_rotated_' . now()->format('YmdHis');

        // Build query for the token
        $query = PersonalAccessToken::where('name', $tokenName);

        if ($userEmail) {
            $user = User::where('email', $userEmail)->first();
            if (!$user) {
                $this->error("User with email '{$userEmail}' not found.");
                return self::FAILURE;
            }
            $query->where('tokenable_id', $user->id);
        }

        $tokens = $query->get();

        if ($tokens->isEmpty()) {
            $this->error("No tokens found with name '{$tokenName}'" . ($userEmail ? " for user '{$userEmail}'" : ''));
            return self::FAILURE;
        }

        if ($tokens->count() > 1 && !$userEmail) {
            $this->error("Multiple tokens found with name '{$tokenName}'. Please specify --user option.");
            return self::FAILURE;
        }

        foreach ($tokens as $oldToken) {
            $user = User::find($oldToken->tokenable_id);
            
            // Store old token data
            $oldTokenData = [
                'id' => $oldToken->id,
                'name' => $oldToken->name,
                'abilities' => $oldToken->abilities,
                'last_used_at' => $oldToken->last_used_at,
                'expires_at' => $oldToken->expires_at,
                'token_type' => $oldToken->token_type,
                'description' => $oldToken->description,
                'allowed_ips' => $oldToken->allowed_ips,
            ];

            // Create new token with same abilities and settings
            $newToken = $user->createToken($newName, $oldToken->abilities ?? ['*']);
            $newTokenModel = $newToken->accessToken;

            // Copy settings from old token
            $newTokenModel->update([
                'expires_at' => $oldToken->expires_at,
                'token_type' => $oldToken->token_type ?? 'api',
                'description' => $oldToken->description,
                'allowed_ips' => $oldToken->allowed_ips,
                'is_active' => true,
                'rotated_at' => now(),
                'rotation_reason' => $reason,
            ]);

            // Log the rotation
            TokenAuditLog::logTokenOperation(
                $oldToken->id,
                $oldToken->name,
                $user->id,
                'rotated',
                null, // CLI command, no user
                $reason,
                $oldTokenData,
                [
                    'new_token_id' => $newTokenModel->id,
                    'new_token_name' => $newName,
                ],
                [
                    'command' => 'system:rotate-token',
                    'rotation_method' => 'cli',
                ]
            );

            // Log the creation of new token
            TokenAuditLog::logTokenOperation(
                $newTokenModel->id,
                $newName,
                $user->id,
                'created',
                null,
                'Created as rotation of token: ' . $oldToken->name,
                null,
                [
                    'rotated_from' => $oldToken->id,
                    'rotated_from_name' => $oldToken->name,
                ],
                [
                    'command' => 'system:rotate-token',
                    'rotation_method' => 'cli',
                ]
            );

            // Delete the old token
            $oldToken->delete();

            $this->info("Token rotated successfully for user: {$user->name} ({$user->email})");
            $this->line("Old token '{$tokenName}' has been revoked");
            $this->line("New token name: {$newName}");
            $this->warn("New token: {$newToken->plainTextToken}");
            $this->info("Store this token securely. It will not be shown again.");
        }

        return self::SUCCESS;
    }
}
