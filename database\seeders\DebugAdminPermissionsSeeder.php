<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class DebugAdminPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('=== DEBUG ADMIN PERMISSIONS ===');
        
        // Tìm admin user
        $adminUser = User::where('email', '<EMAIL>')->first();
        
        if (!$adminUser) {
            $this->command->error('❌ Admin user not found!');
            return;
        }
        
        $this->command->info("✅ Found admin user: {$adminUser->name}");
        
        // Kiểm tra roles
        $roles = $adminUser->roles;
        $this->command->info("Roles: " . $roles->pluck('name')->join(', '));
        
        if ($roles->isEmpty()) {
            $this->command->error('❌ Admin user has no roles!');
            
            // <PERSON><PERSON> role Admin
            $adminRole = Role::where('name', 'Admin')->first();
            if ($adminRole) {
                $adminUser->assignRole('Admin');
                $this->command->info('✅ Assigned Admin role');
            } else {
                $this->command->error('❌ Admin role not found!');
                return;
            }
        }
        
        // Kiểm tra permission assign-posts có tồn tại không
        $assignPostsPermission = Permission::where('name', 'assign-posts')->first();
        if (!$assignPostsPermission) {
            $this->command->error('❌ Permission "assign-posts" not found!');
            $this->command->info('Creating assign-posts permission...');
            Permission::create(['name' => 'assign-posts', 'guard_name' => 'web']);
            $this->command->info('✅ Created assign-posts permission');
        } else {
            $this->command->info('✅ Permission "assign-posts" exists');
        }
        
        // Kiểm tra Admin role có permission assign-posts không
        $adminRole = Role::where('name', 'Admin')->first();
        if ($adminRole && !$adminRole->hasPermissionTo('assign-posts')) {
            $this->command->error('❌ Admin role does not have assign-posts permission!');
            $adminRole->givePermissionTo('assign-posts');
            $this->command->info('✅ Gave assign-posts permission to Admin role');
        }
        
        // Refresh user permissions
        $adminUser->refresh();
        
        // Test permissions
        $this->command->info('=== PERMISSION TESTS ===');
        $permissions = [
            'access-admin-dashboard',
            'assign-posts',
            'view-any-posts',
            'manage-users'
        ];
        
        foreach ($permissions as $permission) {
            $hasPermission = $adminUser->can($permission);
            $status = $hasPermission ? '✅' : '❌';
            $this->command->info("{$status} {$permission}: " . ($hasPermission ? 'YES' : 'NO'));
        }
        
        // Clear cache
        $this->command->info('=== CLEARING CACHE ===');
        \Artisan::call('cache:clear');
        \Artisan::call('config:clear');
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
        $this->command->info('✅ Cache cleared');
        
        $this->command->info('=== SOLUTION ===');
        $this->command->info('1. Login with: <EMAIL> / password123');
        $this->command->info('2. Go to /test-assign to check permissions');
        $this->command->info('3. Check browser console for CASL debug logs');
        $this->command->info('4. If still not working, check frontend ability.ts mapping');
    }
}
