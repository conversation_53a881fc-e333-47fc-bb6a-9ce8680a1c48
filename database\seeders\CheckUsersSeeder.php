<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;

class CheckUsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('=== DANH SÁCH USERS VÀ ROLES ===');
        
        $users = User::with('roles')->get(['id', 'name', 'email']);
        
        foreach ($users as $user) {
            $roles = $user->roles->pluck('name')->join(', ');
            $this->command->info("ID: {$user->id} | {$user->name} ({$user->email}) | Roles: {$roles}");
        }
        
        $this->command->info('=== KẾT THÚC ===');
    }
}
