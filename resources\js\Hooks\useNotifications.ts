// / hooks/useNotifications.ts
import { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { createApiClient } from '@/Utils/utils';
import { toast } from 'sonner';
import { CONSTANTS, ErrorState, Notification } from '@/types/Notification';


export const useNotifications = (initialNotifications: Notification[] = []) => {
  const [notifications, setNotifications] = useState<Notification[]>(initialNotifications);
  const [isLoading, setIsLoading] = useState(false);
  const [markingAsRead, setMarkingAsRead] = useState<Set<string>>(new Set());
  const [hidingNotification, setHidingNotification] = useState<Set<string>>(new Set());
  const [error, setError] = useState<ErrorState | null>(null);
  
  const apiClient = useMemo(() => createApiClient(), []);
  const errorTimeoutRef = useRef<ReturnType<typeof setTimeout>>();

  const showError = useCallback((errorState: ErrorState) => {
    setError(errorState);
    if (errorTimeoutRef.current) {
      clearTimeout(errorTimeoutRef.current);
    }
    errorTimeoutRef.current = setTimeout(() => {
      setError(null);
    }, CONSTANTS.ERROR_TIMEOUT);
  }, []);

  const fetchAllNotifications = useCallback(async (retryCount = 0, force = false) => {
    // Skip if already loading and not forced
    if (isLoading && !force) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await apiClient.get('/notifications', {
        params: { limit: CONSTANTS.MAX_NOTIFICATIONS },
      });
      setNotifications(response.data);
    } catch (error) {
      console.error('Error fetching notifications:', error);

      if (retryCount < CONSTANTS.MAX_RETRY_ATTEMPTS) {
        setTimeout(() => fetchAllNotifications(retryCount + 1, force), 1000 * (retryCount + 1));
        return;
      }

      showError({
        type: 'fetch',
        message: error instanceof Error ? error.message : 'Failed to load notifications. Please try again.',
        retryCount,
      });
    } finally {
      setIsLoading(false);
    }
  }, [apiClient, showError, isLoading]);

  const markAsRead = useCallback(async (notificationId: string) => {
    if (markingAsRead.has(notificationId)) return;
    
    const notificationToUpdate = notifications.find(n => n.id === notificationId);
    if (!notificationToUpdate || notificationToUpdate.read_at) return;
    
    try {
      setMarkingAsRead(prev => new Set([...prev, notificationId]));
      
      await apiClient.post(`/notifications/${notificationId}/read`);
      
      setNotifications(prev =>
        prev.map(notification =>
          notification.id === notificationId
            ? { ...notification, read_at: new Date().toISOString() }
            : notification,
        ),
      );
    } catch (error) {
      console.error('Error marking notification as read:', error);
      showError({
        type: 'mark-read',
        message: 'Failed to mark notification as read.',
      });
    } finally {
      setMarkingAsRead(prev => {
        const newSet = new Set(prev);
        newSet.delete(notificationId);
        return newSet;
      });
    }
  }, [apiClient, notifications, markingAsRead, showError]);

  const markAllAsRead = useCallback(async () => {
    try {
      setIsLoading(true);
      
      await apiClient.post('/notifications/read-all');
      
      setNotifications(prev =>
        prev.map(notification => ({
          ...notification,
          read_at: notification.read_at || new Date().toISOString(),
        })),
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      showError({
        type: 'mark-all-read',
        message: error instanceof Error ? error.message : 'Failed to mark all notifications as read.',
      });
    } finally {
      setIsLoading(false);
    }
  }, [apiClient, showError]);

  const hideNotification = useCallback(async (notificationId: string) => {
    if (hidingNotification.has(notificationId)) return;
    
    try {
      setHidingNotification(prev => new Set([...prev, notificationId]));
      
      await apiClient.delete(`/notifications/${notificationId}`);
      
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
    } catch (error) {
      console.error('Error hiding notification:', error);
      showError({
        type: 'hide',
        message: 'Failed to hide notification.'
      });
    } finally {
      setHidingNotification(prev => {
        const newSet = new Set(prev);
        newSet.delete(notificationId);
        return newSet;
      });
    }
  }, [apiClient, hidingNotification, showError]);

  const handleNewNotification = useCallback((notification: Notification) => {
    setNotifications(prev => {
      // Check for duplicate by ID first
      const existsById = prev.some(n => n.id === notification.id);
      if (existsById) {
        return prev;
      }

      // For ticket notifications, also check by ticket_id to prevent duplicates
      if (notification.data.ticket_id) {
        const existsByTicketId = prev.some(n =>
          n.data.ticket_id === notification.data.ticket_id &&
          n.data.type_notification === 'ticket'
        );
        if (existsByTicketId) {
          return prev; // Skip duplicate ticket notification
        }
      }

      const newNotifications = [notification, ...prev];
      return newNotifications.slice(0, CONSTANTS.MAX_NOTIFICATIONS);
    });
  }, []);

  // Auto-fetch notifications on mount to ensure fresh data
  useEffect(() => {
    // Only fetch if we don't have any notifications yet
    // This ensures the badge shows correct unread count after page reload
    // but doesn't interfere with real-time notifications
    if (notifications.length === 0) {
      fetchAllNotifications(0, true);
    }
  }, []); // Empty dependency array to run only once on mount

  // Auto mark notifications as read when user is actively using the app
  useEffect(() => {
    if (notifications.length === 0) return;

    const unreadNotifications = notifications.filter(n => !n.read_at);
    if (unreadNotifications.length === 0) return;

    // Auto mark as read after 30 seconds if user is active on page
    const autoMarkTimer = setTimeout(() => {
      if (document.visibilityState === 'visible') {
        unreadNotifications.forEach(notification => {
          markAsRead(notification.id);
        });
      }
    }, 30000); // 30 seconds

    return () => clearTimeout(autoMarkTimer);
  }, [notifications, markAsRead]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (errorTimeoutRef.current) {
        clearTimeout(errorTimeoutRef.current);
      }
    };
  }, []);

  return {
    notifications,
    setNotifications,
    isLoading,
    markingAsRead,
    hidingNotification,
    error,
    fetchAllNotifications,
    markAsRead,
    markAllAsRead,
    hideNotification,
    handleNewNotification,
    showError,
  };
};



