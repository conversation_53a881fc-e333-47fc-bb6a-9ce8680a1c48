<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tag;
use App\Models\User;
use App\Services\AdminService;
use App\Services\CategoryService;
use App\Services\TicketAutomationService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Gate;

class AdminController extends Controller
{
    use AuthorizesRequests;

    protected AdminService $adminService;

    protected CategoryService $categoryService;

    protected TicketAutomationService $automationService;

    public function __construct(AdminService $AdminService,
                                CategoryService $categoryService,
                                TicketAutomationService $automationService
                                )
    {
        $this->adminService = $AdminService;
        $this->categoryService = $categoryService;
        $this->automationService = $automationService;
    }

    public function dashboard()
    {
        // Check admin dashboard permission
        if (!auth()->user()->can('access-admin-dashboard')) {
            abort(403);
        }

        $users = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['Admin', 'Manager', 'Employee']);
        })
            ->with(['departments:id,name', 'roles:id,name'])
            ->withCount([
                // Workload = số ticket đang mở/đang xử lý
                'assignedTickets as workload' => function ($q) {
                    $q->whereIn('status', ['open', 'in_progress']);
                },
                // Tổng số ticket đã được giao
                'assignedTickets as assigned_total',
                // Số ticket đã giải quyết/đóng
                'assignedTickets as resolved_count' => function ($q) {
                    $q->whereIn('status', ['resolved', 'closed']);
                },
            ])
            ->orderBy('name')
            ->get(['id', 'name', 'email', 'profile_photo_path']);

        // Chuẩn hoá dữ liệu gửi ra FE theo cấu trúc cần dùng
        $assignableUsers = $users->map(function ($u) {
            $total = (int) ($u->assigned_total ?? 0);
            $resolved = (int) ($u->resolved_count ?? 0);
            $workload = (int) ($u->workload ?? 0);

            $performance = $total > 0 ? (int) round(($resolved / max(1, $total)) * 100) : 0;
            $availability = $workload <= 2 ? 'available' : ($workload <= 5 ? 'busy' : 'offline');

            $department = optional($u->departments->first());
            $role = optional($u->roles->first());

            return [
                'id' => $u->id,
                'name' => $u->name,
                'email' => $u->email,
                'profile_photo_url' => $u->profile_photo_url,
                'department' => $department ? [
                    'id' => $department->id,
                    'name' => $department->name,
                ] : null,
                'role' => $role ? $role->name : null,
                'workload' => $workload,
                'performance_score' => $performance,
                'availability' => $availability,
            ];
        });

        $data = $this->adminService->getDashboardData(auth()->user());
        // $data['tickets'] = $this->adminService->getTopTickets();
        // Add automation stats to dashboard
        $data['automation_stats'] = $this->automationService->getAutomationStats();

        return Inertia::render('Admin/Dashboard', [
            'assignableUsers' => $assignableUsers,
            'data' => $data,
        ]);

    }

    public function getAllTicket(Request $request)
    {
        $this->authorize('access-admin-dashboard');
        // Đơn giản - chỉ check view tickets
        // if (!auth()->user()->hasRole('Admin') || !auth()->user()->hasRole('Employee')) {
        //     abort(403);
        // }

        $perPage = $request->get('per_page', 10);
        $ticketData = $this->adminService->getAllTickets($request, $perPage);

        return Inertia::render('Admin/Tickets', $ticketData);

    }

    public function getAllCategory(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $perPage = $request->input('per_page', 10);
        $categoryData = $this->categoryService->getAllCategorise($request, $perPage);

        return Inertia::render('Admin/Categories', $categoryData);
    }

    public function getAllUsers(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $perPage = $request->input('per_page', 10);
        $users = $this->adminService->getAllUsers($request, $perPage);

        return Inertia::render('Admin/Users', $users);
    }

    public function getAllTags(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $currentUser = auth()->user();

        // if ($currentUser->isAdmin()) {
        //     // Admin xem tất cả Tag
           
        // } else {
        //     // Non-admin chỉ xem Tag của tickets trong phòng ban họ
        //     $departmentIds = $currentUser->departments()->pluck('departments.id');
        //     if ($departmentIds->isNotEmpty()) {
        //         $tags = Tag::whereHas('tickets', function($query) use ($departmentIds) {
        //             $query->whereIn('department_id', $departmentIds);
        //         })->get();
        //     } else {
        //         $tags = collect(); // Empty collection if user has no department
        //     }
        // }
        // $perPage = $request->input('per_page', 10);
        // $tags = $this->adminService->getAllTags($request, $perPage);
        $tags = Tag::all();
        return Inertia::render('Admin/Tags', [
            'tags' => $tags,
        ]);
    }

    public function getAllRolesAndPermissions(Request $request)
    {
        // Admin có toàn quyền - không cần check riêng
        if (!auth()->user()->can('access-admin-dashboard')) {
            abort(403);
        }
        
        $perPage = $request->input('per_page', 10);
        $search = $request->input('search', '');
        $page = $request->input('page', 1);
        $roles = \Spatie\Permission\Models\Role::with('permissions')->get();
        $permissions = \Spatie\Permission\Models\Permission::all();

        // Phân trang cho users
        $usersQuery = \App\Models\User::query()
            ->with(['roles', 'permissions']);

        // Lọc theo phòng ban nếu user không phải admin
        $currentUser = auth()->user();
        if (!$currentUser->isAdmin()) {
            $departmentIds = $currentUser->departments()->pluck('departments.id');
            if ($departmentIds->isNotEmpty()) {
                $usersQuery->whereHas('departments', function($q) use ($departmentIds) {
                    $q->whereIn('departments.id', $departmentIds);
                });
            } else {
                // Nếu user không thuộc phòng ban nào, không hiển thị users nào
                $usersQuery->whereRaw('1 = 0');
            }
        }

        $users = $usersQuery->when($search, function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate($perPage)
            ->withQueryString();

        return Inertia::render('Admin/RolesPermissions', [
            'roles' => $roles,
            'permissions' => $permissions,
            'users' => [
                'data' => $users->items(),
                'total' => $users->total(),
                'per_page' => $users->perPage(),
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'next_page_url' => $users->nextPageUrl(),
                'prev_page_url' => $users->previousPageUrl(),
            ],
            'filters' => [
                'search' => $search,
                'per_page' => $perPage,
            ],
        ]);
    }

    public function notifications()
    {
        $this->authorize('access-admin-dashboard');

        $user = auth()->user();
        $notifications = $user->notifications()
            ->orderByRaw('read_at IS NULL DESC') // Unread first
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'data' => $notification->data,
                    'read_at' => $notification->read_at,
                    'created_at' => $notification->created_at->diffForHumans(),
                ];
            });

        // Get tickets related to these notifications
        $ticketIds = $notifications->pluck('data.ticket_id')->filter()->unique()->values()->toArray();
        $tickets = [];

        if (! empty($ticketIds)) {
            $ticketsQuery = \App\Models\Ticket::whereIn('id', $ticketIds)
                ->with(['user', 'categories', 'Tag'])
                ->withCount('upvotes');

            // Lọc theo phòng ban nếu user không phải admin
            if (!$user->isAdmin()) {
                $departmentIds = $user->departments()->pluck('departments.id');
                if ($departmentIds->isNotEmpty()) {
                    $ticketsQuery->whereIn('department_id', $departmentIds);
                } else {
                    // Nếu user không thuộc phòng ban nào, không hiển thị tickets nào
                    $ticketsQuery->whereRaw('1 = 0');
                }
            }

            $tickets = $ticketsQuery->get()
                ->map(function ($ticket) {
                    $comments = $ticket->getFormattedComments();
                    $hasUpvote = auth()->check() ? $ticket->isUpvotedBy(auth()->id()) : false;

                    return [
                        'id' => $ticket->id,
                        'title' => $ticket->title,
                        'content' => $ticket->content,
                        'created_at' => $ticket->created_at->diffForHumans(),
                        'updated_at' => $ticket->updated_at,
                        'user' => $ticket->user,
                        'categories' => $ticket->categories,
                        'tags' => $ticket->tags,
                        'comments' => $comments,
                        'upvote_count' => $ticket->upvotes_count,
                        'has_upvote' => $hasUpvote,
                    ];
                });
        }

        return Inertia::render('Admin/Notifications', [
            'notifications' => $notifications,
            'tickets' => $tickets,
        ]);
    }

    public function getTicket($id)
    {
        $this->authorize('access-admin-dashboard');

        $ticket = \App\Models\Ticket::with(['user', 'categories', 'tags'])
            ->withCount('upvotes')
            ->findOrFail($id);

        $comments = $ticket->getFormattedComments();
        $hasUpvote = auth()->check() ? $ticket->isUpvotedBy(auth()->id()) : false;

        return response()->json([
            'ticket' => [
                'id' => $ticket->id,
                'title' => $ticket->title,
                'content' => $ticket->content,
                'created_at' => $ticket->created_at->diffForHumans(),
                'updated_at' => $ticket->updated_at,
                'user' => $ticket->user,
                'categories' => $ticket->categories,
                'tags' => $ticket->tags,
                'comments' => $comments,
                'upvote_count' => $ticket->upvotes_count,
                'has_upvote' => $hasUpvote,
            ],
        ]);
    }

    /**
     * Get departments and users for ticket assignment
     */
    public function getAssignmentData()
    {
        $this->authorize('access-admin-dashboard');

        $departments = \App\Models\Departments::select(['id', 'name'])
            ->orderBy('name')
            ->get();

        $users = \App\Models\User::select(['id', 'name', 'email', 'profile_photo_path'])
            ->with(['departments:id,name'])
            ->whereHas('roles', function ($query) {
                $query->whereIn('name', ['Admin', 'Manager', 'Employee']);
            })
            ->orderBy('name')
            ->get();

        return response()->json([
            'departments' => $departments,
            'users' => $users,
        ]);
    }


    public function getReport(){
        return Inertia::render('Report/index');
    }
}
