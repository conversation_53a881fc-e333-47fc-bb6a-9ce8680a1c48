<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ApiAuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'request_id',
        'user_id',
        'token_name',
        'token_id',
        'method',
        'endpoint',
        'ip_address',
        'user_agent',
        'request_headers',
        'request_payload',
        'response_status',
        'response_body',
        'response_time_ms',
        'requested_at',
        'rate_limit_remaining',
        'rate_limit_reset_at',
        'error_type',
        'error_message',
    ];

    protected $casts = [
        'request_headers' => 'array',
        'requested_at' => 'datetime',
        'rate_limit_reset_at' => 'datetime',
        'response_time_ms' => 'decimal:2',
    ];

    /**
     * Get the user that made the API call
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for successful requests
     */
    public function scopeSuccessful($query)
    {
        return $query->whereBetween('response_status', [200, 299]);
    }

    /**
     * Scope for failed requests
     */
    public function scopeFailed($query)
    {
        return $query->where('response_status', '>=', 400);
    }

    /**
     * Scope for specific endpoint
     */
    public function scopeForEndpoint($query, string $endpoint)
    {
        return $query->where('endpoint', 'like', "%{$endpoint}%");
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('requested_at', [$startDate, $endDate]);
    }

    /**
     * Get logs for a specific user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Get logs for a specific IP
     */
    public function scopeForIp($query, string $ip)
    {
        return $query->where('ip_address', $ip);
    }
}
