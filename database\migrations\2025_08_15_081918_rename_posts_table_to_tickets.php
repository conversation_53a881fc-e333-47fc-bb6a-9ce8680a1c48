<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Rename the posts table to tickets
        Schema::rename('posts', 'tickets');

        // Update foreign key references in related tables

        // Update comments table
        // Drop foreign key constraints safely
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        Schema::table('comments', function (Blueprint $table) {
            $table->renameColumn('post_id', 'ticket_id');
        });

        Schema::table('comments', function (Blueprint $table) {
            $table->foreign('ticket_id')->references('id')->on('tickets')->onDelete('cascade');
        });

        DB::statement('SET FOREIGN_KEY_CHECKS=1');

        // Update post_upvotes table to ticket_upvotes
        Schema::rename('post_upvotes', 'ticket_upvotes');
        Schema::table('ticket_upvotes', function (Blueprint $table) {
            $table->renameColumn('post_id', 'ticket_id');
        });
        Schema::table('ticket_upvotes', function (Blueprint $table) {
            $table->foreign('ticket_id')->references('id')->on('tickets')->onDelete('cascade');
        });

        // Update post_tag table to ticket_tag
        Schema::rename('post_tag', 'ticket_tag');
        Schema::table('ticket_tag', function (Blueprint $table) {
            $table->renameColumn('post_id', 'ticket_id');
        });
        Schema::table('ticket_tag', function (Blueprint $table) {
            $table->foreign('ticket_id')->references('id')->on('tickets')->onDelete('cascade');
        });

        // Update category_post table to category_ticket
        Schema::rename('category_post', 'category_ticket');
        Schema::table('category_ticket', function (Blueprint $table) {
            $table->renameColumn('post_id', 'ticket_id');
        });
        Schema::table('category_ticket', function (Blueprint $table) {
            $table->foreign('ticket_id')->references('id')->on('tickets')->onDelete('cascade');
        });

        // Update post_tag_priorities table to ticket_tag_priorities
        Schema::rename('post_tag_priorities', 'ticket_tag_priorities');
        Schema::table('ticket_tag_priorities', function (Blueprint $table) {
            $table->renameColumn('post_id', 'ticket_id');
        });
        Schema::table('ticket_tag_priorities', function (Blueprint $table) {
            $table->foreign('ticket_id')->references('id')->on('tickets')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the foreign key updates

        // Revert ticket_tag_priorities table to post_tag_priorities
        Schema::table('ticket_tag_priorities', function (Blueprint $table) {
            $table->dropForeign(['ticket_id']);
            $table->renameColumn('ticket_id', 'post_id');
            $table->foreign('post_id')->references('id')->on('posts')->onDelete('cascade');
        });
        Schema::rename('ticket_tag_priorities', 'post_tag_priorities');

        // Revert category_ticket table to category_post
        Schema::table('category_ticket', function (Blueprint $table) {
            $table->dropForeign(['ticket_id']);
            $table->renameColumn('ticket_id', 'post_id');
            $table->foreign('post_id')->references('id')->on('posts')->onDelete('cascade');
        });
        Schema::rename('category_ticket', 'category_post');

        // Revert ticket_tag table to post_tag
        Schema::table('ticket_tag', function (Blueprint $table) {
            $table->dropForeign(['ticket_id']);
            $table->renameColumn('ticket_id', 'post_id');
            $table->foreign('post_id')->references('id')->on('posts')->onDelete('cascade');
        });
        Schema::rename('ticket_tag', 'post_tag');

        // Revert ticket_upvotes table to post_upvotes
        Schema::table('ticket_upvotes', function (Blueprint $table) {
            $table->dropForeign(['ticket_id']);
            $table->renameColumn('ticket_id', 'post_id');
            $table->foreign('post_id')->references('id')->on('posts')->onDelete('cascade');
        });
        Schema::rename('ticket_upvotes', 'post_upvotes');

        // Revert comments table
        Schema::table('comments', function (Blueprint $table) {
            $table->dropForeign(['ticket_id']);
            $table->renameColumn('ticket_id', 'post_id');
            $table->foreign('post_id')->references('id')->on('posts')->onDelete('cascade');
        });

        // Rename the tickets table back to posts
        Schema::rename('tickets', 'posts');
    }
};
