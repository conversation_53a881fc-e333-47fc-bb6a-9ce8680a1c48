<?php

namespace App\Data\Comment;

use <PERSON><PERSON>\LaravelData\Data;

class CommentData extends Data
{
    public function __construct(
        public string $comment,
        public string $ticket_id,
        public int $user_id,
        public ?int $parent_id = null
    ) {}

    public static function rules(): array
    {
        return [
            'comment' => 'required|string|max:1000',
            'ticket_id' => 'required|exists:tickets,id',
            'user_id' => 'required|exists:users,id',
            'parent_id' => 'nullable|exists:comments,id',
        ];
    }
}
