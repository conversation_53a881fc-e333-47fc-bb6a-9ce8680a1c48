<?php

namespace App\Notifications;

use App\Models\Ticket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewTicketNotification extends Notification implements ShouldBroadcast, ShouldQueue
{
    use Queueable;

    public Ticket $ticket;

    public function __construct(Ticket $ticket)
    {
        $this->ticket = $ticket;
    }

    public function via($notifiable): array
    {
        return ['database']; // Chỉ lưu vào database, broadcast sẽ được handle riêng
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject("New Ticket Created: {$this->ticket->title}")
            ->greeting("Hello {$notifiable->name}!")
            ->line("A new ticket has been created by {$this->ticket->user->name}:")
            ->line("**Title:** {$this->ticket->title}")
            ->line("**Priority:** " . ucfirst($this->ticket->priority))
            ->line("**Department:** " . ($this->ticket->department->name ?? 'No Department'))
            ->action('View Ticket', url('/tickets/'.$this->ticket->slug))
            ->line('Thank you for using our support system!');
    }

    public function toArray($notifiable): array
    {
        return [
            'ticket_id' => $this->ticket->id,
            'title' => $this->ticket->title,
            'message' => "New ticket created: {$this->ticket->title}",
            'slug' => $this->ticket->slug,
            'priority' => $this->ticket->priority,
            'status' => $this->ticket->status,
            'name' => $this->ticket->user->name ?? 'Unknown User',
            'user_email' => $this->ticket->user->email ?? '',
            'profile_photo_url' => $this->ticket->user->profile_photo_path
                ? asset('storage/'.$this->ticket->user->profile_photo_path)
                : null,
            'department_name' => $this->ticket->department->name ?? 'No Department',
            'categories' => $this->ticket->categories->pluck('title')->toArray(),
            'tags' => $this->ticket->tags->pluck('name')->toArray(),
            'type_notification' => 'ticket',
        ];
    }

    public function toBroadcast($notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id, // Notification ID generated by Laravel
            'data' => $this->toArray($notifiable),
            'type' => 'ticket',
            'read_at' => null,
            'created_at' => $this->ticket->created_at->diffForHumans(),
        ]);
    }

    public function broadcastOn()
    {
        // Sử dụng channel chung như các notification khác
        return new \Illuminate\Broadcasting\Channel('notifications');
    }

    public function broadcastAs(): string
    {
        return 'new-ticket-notification';
    }
}
