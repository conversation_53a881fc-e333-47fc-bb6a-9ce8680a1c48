# 🔐 Hệ Thống Phân Quyền - Support Payment System

## 📋 Tổng Quan

Hệ thống sử dụng **Laravel Spatie Permission** với **CASL** cho frontend để quản lý phân quyền chi tiết theo từng role.

---

## 👥 Roles và Permissions

### 🔴 **Admin** (Toà<PERSON> quyền)
**<PERSON><PERSON> tả**: Quản trị viên hệ thống, có toàn bộ quyền

**Permissions (11 quyền)**:
- ✅ `access-admin-dashboard` - Truy cập admin panel
- ✅ `view-any-posts` - Xem tất cả tickets
- ✅ `view-department-posts` - Xem tickets theo phòng ban
- ✅ `update-any-posts` - Sửa tất cả tickets
- ✅ `delete-any-posts` - Xóa tất cả tickets
- ✅ `update-status-posts` - Cập nhật trạng thái tickets
- ✅ `assign-posts` - <PERSON><PERSON> tickets cho user
- ✅ `manage-users` - Quản lý users
- ✅ `manage-roles-permissions` - Quản lý roles & permissions
- ✅ `manage-categories` - Quản lý categories
- ✅ `manage-tags` - <PERSON><PERSON><PERSON><PERSON> lý tags
- ✅ `manage-departments` - Quản lý departments
- ✅ `manage-department-users` - Quản lý users trong phòng ban
- ✅ `manage-own-department` - Quản lý phòng ban của mình
- ✅ `moderate-comments` - Kiểm duyệt comments

**Có thể làm gì**:
- 🎯 Truy cập toàn bộ admin dashboard
- 📊 Xem tất cả tickets trong hệ thống
- 👥 Quản lý users, roles, permissions
- 🏢 Quản lý tất cả departments
- 🎫 Tạo, sửa, xóa, gán tickets
- 🏷️ Quản lý categories và tags
- 💬 Kiểm duyệt comments

---

### 🟡 **Manager** (Quản lý phòng ban)
**Mô tả**: Quản lý phòng ban, có quyền quản lý nhân viên và tickets trong phòng

**Permissions (8 quyền)**:
- ✅ `access-admin-dashboard` - Truy cập admin panel
- ✅ `view-department-posts` - Xem tickets phòng mình
- ✅ `update-any-posts` - Sửa tickets
- ✅ `update-status-posts` - Cập nhật trạng thái
- ✅ `assign-posts` - Gán tickets cho user
- ✅ `manage-department-users` - Thêm/xóa user trong phòng
- ✅ `manage-own-department` - Quản lý phòng mình
- ✅ `moderate-comments` - Kiểm duyệt comments

**Có thể làm gì**:
- 🎯 Truy cập admin dashboard (hạn chế)
- 📊 Xem tickets của phòng ban mình
- 👥 Thêm/xóa nhân viên trong phòng ban
- 🎫 Gán tickets cho nhân viên
- 📝 Cập nhật trạng thái tickets
- 💬 Kiểm duyệt comments

**Không thể làm**:
- ❌ Xem tickets của phòng ban khác
- ❌ Quản lý users toàn hệ thống
- ❌ Quản lý roles & permissions
- ❌ Quản lý departments khác

---

### 🟢 **Employee** (Nhân viên)
**Mô tả**: Nhân viên xử lý tickets, chỉ thấy tickets phòng mình + unassigned

**Permissions (4 quyền)**:
- ✅ `access-admin-dashboard` - Truy cập admin panel
- ✅ `view-department-posts` - Xem tickets phòng mình + unassigned
- ✅ `update-status-posts` - Cập nhật trạng thái
- ✅ `moderate-comments` - Kiểm duyệt comments

**Có thể làm gì**:
- 🎯 Truy cập admin dashboard (rất hạn chế)
- 📊 Xem tickets của phòng mình
- 📊 Xem tickets chưa được gán (unassigned)
- 📝 Cập nhật trạng thái tickets
- 💬 Kiểm duyệt comments

**Không thể làm**:
- ❌ Xem tickets phòng ban khác
- ❌ Gán tickets cho ai
- ❌ Xóa tickets
- ❌ Quản lý users/departments
- ❌ Quản lý categories/tags

---

### 🔵 **Customer** (Khách hàng)
**Mô tả**: Người dùng cuối, chỉ có thể tạo tickets

**Permissions (0 quyền admin)**:
- ❌ Không có quyền admin nào

**Có thể làm gì**:
- 🎫 Tạo tickets mới (không cần permission)
- 👀 Xem tickets của mình
- 💬 Comment vào tickets của mình

**Không thể làm**:
- ❌ Truy cập admin dashboard
- ❌ Xem tickets của người khác
- ❌ Quản lý bất cứ thứ gì

---

## 🎯 Logic Hiển Thị UI

### Navigation Header
```typescript
// Link "Admin" chỉ hiển thị khi:
ability.can('access-admin-dashboard', 'AdminDashboard')
```

### Admin Sidebar
- **Tickets**: `ability.can('view-any', 'Post')` hoặc `ability.can('view-department', 'Post')`
- **Users**: `ability.can('manage', 'User')`
- **Roles & Permissions**: `ability.can('manage-roles-permissions', 'RolePermission')`
- **Departments**: `ability.can('manage', 'Department')`

### Ticket Actions
- **Gán tickets**: `ability.can('assign', 'Post')`
- **Cập nhật trạng thái**: `ability.can('update-status', 'Post')`
- **Xóa tickets**: `ability.can('delete-any', 'Post')`

---

## 🔑 Thông Tin Đăng Nhập

```bash
# Admin (Toàn quyền)
Email: <EMAIL>
Password: password123

# Manager (Quản lý phòng ban)
Email: <EMAIL>
Password: manager123

# Employee (Nhân viên)
Email: <EMAIL>
Password: employee123

# Customer (Khách hàng)
Email: <EMAIL>
Password: customer123
```

---

## 📊 Ma Trận Quyền

| Permission | Admin | Manager | Employee | Customer |
|------------|-------|---------|----------|----------|
| access-admin-dashboard | ✅ | ✅ | ✅ | ❌ |
| view-any-posts | ✅ | ❌ | ❌ | ❌ |
| view-department-posts | ✅ | ✅ | ✅ | ❌ |
| update-any-posts | ✅ | ✅ | ❌ | ❌ |
| delete-any-posts | ✅ | ❌ | ❌ | ❌ |
| assign-posts | ✅ | ✅ | ❌ | ❌ |
| manage-users | ✅ | ❌ | ❌ | ❌ |
| manage-roles-permissions | ✅ | ❌ | ❌ | ❌ |
| manage-departments | ✅ | ❌ | ❌ | ❌ |
| manage-own-department | ✅ | ✅ | ❌ | ❌ |

---

## 🚀 Cách Test

1. **Đăng nhập** với từng role
2. **Kiểm tra navigation** - Link "Admin" có hiển thị không?
3. **Vào admin dashboard** - Menu sidebar hiển thị gì?
4. **Test actions** - Buttons nào có thể click?
5. **Test permissions** - Có bị chặn không?

---

*Cập nhật lần cuối: 2025-01-08*
