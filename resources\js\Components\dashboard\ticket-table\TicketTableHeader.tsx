import React from 'react';
import { <PERSON>, CardHeader, CardTitle, CardDescription } from '@/Components/ui/card';
import { Button } from '@/Components/ui/button';
import { RefreshCw, UserPlus } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/Components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';

interface AssignableUser {
  id: number;
  name: string;
  email: string;
  profile_photo_url?: string;
}

interface TicketTableHeaderProps {
  filteredTicketsCount: number;
  totalTicketsCount: number;
  selectedTicketsCount: number;
  lastRefresh: Date | null;
  isRefreshing: boolean;
  isLoading: boolean;
  canAssignPosts: boolean;
  assignableUsers: AssignableUser[];
  onRefresh: () => void;
  onQuickAssign: (assigneeId: number) => void;
}

export default function TicketTableHeader({
  filteredTicketsCount,
  totalTicketsCount,
  selectedTicketsCount,
  lastRefresh,
  isRefreshing,
  isLoading,
  canAssignPosts,
  assignableUsers,
  onRefresh,
  onQuickAssign,
}: TicketTableHeaderProps) {
  return (
    <CardHeader>
      <div className="flex items-center justify-between">
        <div>
          <CardTitle className="flex items-center gap-2">
            <span className="h-5 w-5">🎫</span>
            Quản lý Tickets
          </CardTitle>
          <CardDescription>
            {filteredTicketsCount} of {totalTicketsCount} tickets
            {selectedTicketsCount > 0 && ` • ${selectedTicketsCount} selected`}
            {lastRefresh && (
              <span className="text-xs text-muted-foreground ml-2">
                • Cập nhật lúc {lastRefresh.toLocaleTimeString('vi-VN')}
              </span>
            )}
          </CardDescription>
        </div>
        
        <div className="flex items-center gap-2">
          {canAssignPosts && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="default" size="sm" disabled={!assignableUsers.length}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Giao việc nhanh
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Chọn nhân viên</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {assignableUsers.length > 0 ? (
                  assignableUsers.map(assignee => (
                    <DropdownMenuItem
                      key={assignee.id}
                      onClick={() => onQuickAssign(assignee.id)}
                      className="cursor-pointer"
                    >
                      <Avatar className="h-6 w-6 mr-2">
                        <AvatarImage src={assignee.profile_photo_url || ''} alt={assignee.name} />
                        <AvatarFallback>{assignee.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span className="text-sm font-medium">{assignee.name}</span>
                        <span className="text-xs text-muted-foreground">{assignee.email}</span>
                      </div>
                    </DropdownMenuItem>
                  ))
                ) : (
                  <div className="px-2 py-1.5 text-sm text-muted-foreground">
                    Không có nhân viên nào để giao việc
                  </div>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={onRefresh} 
            disabled={isRefreshing || isLoading}
            className="relative"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Đang tải...' : 'Refresh'}
          </Button>
        </div>
      </div>
    </CardHeader>
  );
}
