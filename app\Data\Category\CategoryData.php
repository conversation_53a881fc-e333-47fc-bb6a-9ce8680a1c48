<?php

namespace App\Data\Category;

use Illuminate\Foundation\Http\FormRequest;

class CategoryData extends FormRequest
{
    public function rules(): array
    {
        $id = $this->route('category')?->id;

        return [
            'title' => 'required|string|min:2|max:100|unique:categories,title,' . $id,
            'slug' => [
                'required',
                'string',
                'min:2',
                'max:100',
                'regex:/^[a-z0-9]+(?:-[a-z0-9]+)*$/',
                'unique:categories,slug,' . $id,
            ],
            'description' => 'nullable|string|max:500',
            'logo' => 'nullable|image|max:10240', // max 10MB
        ];
    }

    public function messages()
    {
        return [
            'slug.regex' => 'Đường dẫn chỉ được chứa chữ thường, số và dấu gạch ngang.',
        ];
    }
}
