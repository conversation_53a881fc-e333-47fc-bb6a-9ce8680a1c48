<?php

namespace Tests\Feature;

use Database\Seeders\RolesAndPermissionsSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class RolesAndPermissionsSeederTest extends TestCase
{
    use RefreshDatabase;

    public function test_seeder_creates_all_required_permissions()
    {
        $this->seed(RolesAndPermissionsSeeder::class);

        $expectedPermissions = [
            // Posts permissions
            'view-public-posts',
            'view-any-posts',
            'view-own-posts',
            // 'create-posts', // Bỏ vì ai cũng có thể tạo tickets
            'update-any-posts',
            'update-own-posts',
            'delete-any-posts',
            'delete-own-posts',
            'update-status-posts',
            
            // Admin Panel
            'access-admin-dashboard',
            
            // Users
            'view-users',
            'update-users',
            'delete-users',
            'assign-roles',
            
            // Roles & Permissions
            'manage-roles-permissions',
            
            // Categories & Tags
            'manage-categories',
            'manage-tags',
            
            // Departments
            'manage-departments',
            'assign-users-to-departments',
            
            // Comments
            'create-comments',
            'moderate-comments',
        ];

        foreach ($expectedPermissions as $permission) {
            $this->assertDatabaseHas('permissions', ['name' => $permission]);
        }
    }

    public function test_seeder_creates_all_required_roles()
    {
        $this->seed(RolesAndPermissionsSeeder::class);

        $expectedRoles = [
            'Super Admin',
            'Admin',
            'Employee',
            'Customer'
        ];

        foreach ($expectedRoles as $role) {
            $this->assertDatabaseHas('roles', ['name' => $role]);
        }
    }

    public function test_super_admin_has_all_permissions()
    {
        $this->seed(RolesAndPermissionsSeeder::class);

        $superAdmin = Role::findByName('Super Admin');
        $allPermissions = Permission::all();

        $this->assertEquals($allPermissions->count(), $superAdmin->permissions->count());
    }

    public function test_admin_has_correct_permissions()
    {
        $this->seed(RolesAndPermissionsSeeder::class);

        $admin = Role::findByName('Admin');
        
        $expectedPermissions = [
            'view-public-posts',
            'view-any-posts',
            // 'create-posts', // Bỏ vì ai cũng có thể tạo
            'update-any-posts',
            'delete-any-posts',
            'update-status-posts',
            'access-admin-dashboard',
            'view-users',
            'update-users',
            'delete-users',
            'assign-roles',
            'manage-categories',
            'manage-tags',
            'manage-departments',
            'assign-users-to-departments',
            'create-comments',
            'moderate-comments',
        ];

        foreach ($expectedPermissions as $permission) {
            $this->assertTrue($admin->hasPermissionTo($permission));
        }

        // Admin should NOT have manage-roles-permissions
        $this->assertFalse($admin->hasPermissionTo('manage-roles-permissions'));
    }

    public function test_employee_has_correct_permissions()
    {
        $this->seed(RolesAndPermissionsSeeder::class);

        $employee = Role::findByName('Employee');
        
        $expectedPermissions = [
            'view-public-posts',
            'view-any-posts',
            // 'create-posts', // Bỏ vì ai cũng có thể tạo
            'update-any-posts',
            'update-status-posts',
            'access-admin-dashboard',
            'manage-categories',
            'manage-tags',
            'create-comments',
            'moderate-comments',
        ];

        foreach ($expectedPermissions as $permission) {
            $this->assertTrue($employee->hasPermissionTo($permission));
        }

        // Employee should NOT have user management permissions
        $this->assertFalse($employee->hasPermissionTo('view-users'));
        $this->assertFalse($employee->hasPermissionTo('delete-any-posts'));
    }

    public function test_customer_has_correct_permissions()
    {
        $this->seed(RolesAndPermissionsSeeder::class);

        $customer = Role::findByName('Customer');
        
        $expectedPermissions = [
            'view-public-posts',
            'view-own-posts',
            // 'create-posts', // Bỏ vì ai cũng có thể tạo
            'update-own-posts',
            'delete-own-posts',
            'create-comments',
        ];

        foreach ($expectedPermissions as $permission) {
            $this->assertTrue($customer->hasPermissionTo($permission));
        }

        // Customer should NOT have admin permissions
        $this->assertFalse($customer->hasPermissionTo('access-admin-dashboard'));
        $this->assertFalse($customer->hasPermissionTo('view-any-posts'));
        $this->assertFalse($customer->hasPermissionTo('moderate-comments'));
    }
}
