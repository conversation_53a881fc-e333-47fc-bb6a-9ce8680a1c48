# Thiết kế hệ thống nhận Tickets từ hệ thống ngoài

## 1. Tổng quan hệ thống

### Mục tiêu
- <PERSON>h<PERSON>n tickets từ các hệ thống bên ngoài (payment, order, notification, etc.)
- <PERSON><PERSON><PERSON> bảo tính bảo mật và tránh tạo tài khoản rác
- Du<PERSON> trì tính nhất quán của dữ liệu
- Hỗ trợ truy vết và audit log

### Kiến trúc tổng thể
```
[External Systems] → [API Gateway] → [Ticket Service] → [Database]
                          ↓
                   [Authentication]
                          ↓
                   [System User Manager]
```

## 2. Phân tích vấn đề hiện tại

### Vấn đề chính
1. **Tạo tài khoản rác**: Mỗi lần gửi ticket từ hệ thống khác cần tạo user mới
2. **<PERSON><PERSON><PERSON> mật**: <PERSON><PERSON><PERSON> x<PERSON><PERSON> thự<PERSON> hệ thống gửi ticket
3. **<PERSON>u<PERSON>n lý danh tính**: <PERSON><PERSON> biệt user thật và system user
4. **Truy vết**: <PERSON> dõi nguồn gốc ticket

### Yêu cầu ticket
- `title`: Tiêu đề ticket
- `content`: Nội dung chi tiết
- `category`: Loại hệ thống (payment, order, etc.)
- `tag`: Nhãn phân loại
- `is_publish`: Trạng thái công khai
- `created_by`: Người tạo (bắt buộc)

## 3. Giải pháp đề xuất

### 3.1 Kiến trúc System User
Thay vì tạo user mới cho mỗi request, sử dụng **System User** đại diện cho từng hệ thống:

```sql
-- Bảng system_users
CREATE TABLE system_users (
    id BIGINT PRIMARY KEY,
    system_name VARCHAR(100) NOT NULL UNIQUE, -- 'payment', 'order', etc.
    display_name VARCHAR(200), -- 'Payment System', 'Order System'
    api_key VARCHAR(255) UNIQUE NOT NULL,
    api_secret VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    permissions JSON, -- Quyền của hệ thống
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Bảng categories (đã có)
CREATE TABLE categories (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    system_type VARCHAR(50), -- 'internal', 'external'
    created_at TIMESTAMP
);

-- Bảng tickets (mở rộng)
CREATE TABLE tickets (
    id BIGINT PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    content TEXT,
    category_id BIGINT REFERENCES categories(id),
    tag_ids JSON, -- Array of tag IDs
    is_publish BOOLEAN DEFAULT false,
    created_by_type ENUM('user', 'system') DEFAULT 'user',
    created_by_id BIGINT, -- User ID hoặc System User ID
    external_reference VARCHAR(255), -- Reference từ hệ thống ngoài
    source_system VARCHAR(100), -- Tên hệ thống gửi
    metadata JSON, -- Thông tin bổ sung
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    
    INDEX idx_source_system (source_system),
    INDEX idx_external_ref (external_reference)
);
```

### 3.2 API Authentication
Sử dụng **API Key + Secret** cho xác thực hệ thống:

```
Authorization: ApiKey {api_key}:{signature}
X-Timestamp: {unix_timestamp}
X-System-Name: payment

# Signature = HMAC-SHA256(api_secret, api_key + timestamp + request_body)
```

### 3.3 API Endpoint Design

#### Endpoint tạo ticket từ hệ thống ngoài
```http
POST /api/v1/external/tickets
Content-Type: application/json
Authorization: ApiKey payment_api_key:signature_hash
X-Timestamp: 1692123456
X-System-Name: payment

{
    "title": "Payment failed for order #12345",
    "content": "Payment processing failed due to insufficient funds",
    "category": "payment", // Tên category
    "tags": ["urgent", "payment-failed"], // Array tag names
    "is_publish": false,
    "external_reference": "payment_error_12345",
    "metadata": {
        "order_id": "12345",
        "customer_id": "67890",
        "error_code": "INSUFFICIENT_FUNDS",
        "amount": 150.00
    }
}
```

#### Response
```json
{
    "success": true,
    "data": {
        "ticket_id": "T-202308-001",
        "internal_id": 12345,
        "status": "created",
        "created_at": "2023-08-15T10:30:00Z"
    }
}
```

## 4. Luồng xử lý chi tiết

### 4.1 Luồng khởi tạo hệ thống
```mermaid
sequenceDiagram
    participant Admin
    participant TicketSystem
    participant Database
    
    Admin->>TicketSystem: Đăng ký hệ thống mới
    TicketSystem->>Database: Tạo system_user
    TicketSystem->>Database: Tạo category tương ứng
    TicketSystem->>Admin: Trả về API Key + Secret
```

### 4.2 Luồng tạo ticket từ hệ thống ngoài
```mermaid
sequenceDiagram
    participant ExtSystem as External System
    participant Gateway as API Gateway
    participant Auth as Auth Service
    participant TicketService as Ticket Service
    participant DB as Database
    
    ExtSystem->>Gateway: POST /api/v1/external/tickets
    Gateway->>Auth: Verify API Key & Signature
    
    alt Authentication Success
        Auth->>Gateway: System User Info
        Gateway->>TicketService: Process ticket creation
        TicketService->>DB: Validate category & tags
        TicketService->>DB: Create ticket record
        TicketService->>Gateway: Ticket created
        Gateway->>ExtSystem: Success response
    else Authentication Failed
        Auth->>Gateway: Auth error
        Gateway->>ExtSystem: 401 Unauthorized
    end
```

### 4.3 Validation Logic
```javascript
// Pseudo code validation
async function validateTicketRequest(request, systemUser) {
    // 1. Validate required fields
    if (!request.title || !request.content) {
        throw new ValidationError('Missing required fields');
    }
    
    // 2. Validate category exists and belongs to system
    const category = await Category.findByName(request.category);
    if (!category || category.system_type !== systemUser.system_name) {
        throw new ValidationError('Invalid category for system');
    }
    
    // 3. Validate tags exist
    const tags = await Tag.findByNames(request.tags);
    if (tags.length !== request.tags.length) {
        throw new ValidationError('Some tags do not exist');
    }
    
    // 4. Check permissions
    if (!systemUser.permissions.includes('create_ticket')) {
        throw new PermissionError('System does not have create permission');
    }
    
    return { category, tags };
}
```

## 5. Bảo mật và Rate Limiting

### 5.1 Security Measures
- **API Key Rotation**: Hỗ trợ rotation API key định kỳ
- **IP Whitelist**: Chỉ cho phép IP của hệ thống đã đăng ký
- **Request Signing**: Signature để đảm bảo integrity
- **Timestamp Validation**: Chỉ chấp nhận request trong timeframe (5 phút)

### 5.2 Rate Limiting
```yaml
# Rate limiting config
rate_limits:
  payment_system:
    requests_per_minute: 100
    burst_limit: 20
  order_system:
    requests_per_minute: 200
    burst_limit: 50
  default:
    requests_per_minute: 50
    burst_limit: 10
```

## 6. Monitoring và Logging

### 6.1 Audit Log
```sql
CREATE TABLE system_audit_logs (
    id BIGINT PRIMARY KEY,
    system_name VARCHAR(100),
    action VARCHAR(100), -- 'create_ticket', 'auth_failed'
    resource_id VARCHAR(100), -- ticket_id nếu có
    request_data JSON,
    response_data JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    status VARCHAR(20), -- 'success', 'failed'
    error_message TEXT,
    created_at TIMESTAMP
);
```

### 6.2 Metrics để monitor
- Request count per system
- Success/failure rates
- Response times
- Authentication failures
- Rate limit hits

## 7. Implementation Plan

### Phase 1: Core Infrastructure
1. Tạo bảng system_users và cập nhật ticket schema
2. Implement authentication service
3. Tạo API endpoint cơ bản

### Phase 2: Security & Validation
1. Implement request signing
2. Add rate limiting
3. IP whitelisting
4. Validation logic

### Phase 3: Monitoring & Optimization
1. Audit logging
2. Monitoring dashboard
3. Performance optimization
4. Error handling improvement

## 8. Lợi ích của giải pháp

### 8.1 Giải quyết vấn đề tài khoản rác
- Sử dụng system users thay vì tạo user mới
- Mỗi hệ thống có 1 system user duy nhất
- Dễ quản lý và audit

### 8.2 Bảo mật cao
- API key + secret authentication
- Request signing để tránh tampering
- IP whitelisting và rate limiting

### 8.3 Truy vết tốt
- Lưu external_reference và source_system
- Audit log đầy đủ
- Metadata linh hoạt

### 8.4 Scalable
- Dễ thêm hệ thống mới
- Rate limiting per system
- Horizontal scaling friendly

## 9. Consideration & Risk

### 9.1 Risks
- **API Key compromise**: Cần rotation mechanism
- **System impersonation**: Cần IP whitelisting nghiêm ngặt
- **Data consistency**: Cần transaction handling

### 9.2 Monitoring Alerts
- Authentication failure spikes
- Rate limit exceeded
- Unusual traffic patterns
- System downtime

## 10. Sample Code Structure

```
/src
  /controllers
    ExternalTicketController.js
  /services
    TicketCreationService.js
    SystemAuthService.js
  /middleware
    SystemAuthMiddleware.js
    RateLimitMiddleware.js
  /models
    SystemUser.js
    Ticket.js
  /validators
    ExternalTicketValidator.js
  /utils
    SignatureValidator.js
```

Thiết kế này đảm bảo tính bảo mật cao, tránh tạo tài khoản rác, và dễ dàng mở rộng cho các hệ thống mới trong tương lai.