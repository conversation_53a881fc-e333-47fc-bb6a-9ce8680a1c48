<?php

namespace App\Notifications;

use App\Models\Ticket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class ApiTicketCreatedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public Ticket $ticket;

    public function __construct(Ticket $ticket)
    {
        $this->ticket = $ticket;
        $this->onQueue('notifications');
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        $ticketUrl = route('admin.tickets.show', $this->ticket->slug);
        
        $mailMessage = (new MailMessage)
            ->subject('New Ticket Created via API: ' . $this->ticket->title)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('A new ticket has been created via API and requires your attention.')
            ->line('**Ticket Details:**')
            ->line('Title: ' . $this->ticket->title)
            ->line('Priority: ' . ucfirst($this->ticket->priority))
            ->line('Status: ' . ucfirst($this->ticket->status));

        // Add requester information if available
        if ($this->ticket->requester_name || $this->ticket->requester_email) {
            $mailMessage->line('**Requester Information:**');
            if ($this->ticket->requester_name) {
                $mailMessage->line('Name: ' . $this->ticket->requester_name);
            }
            if ($this->ticket->requester_email) {
                $mailMessage->line('Email: ' . $this->ticket->requester_email);
            }
        }

        // Add department information
        if ($this->ticket->department) {
            $mailMessage->line('Department: ' . $this->ticket->department->name);
        }

        // Add source system information
        if ($this->ticket->source_system) {
            $mailMessage->line('Source System: ' . $this->ticket->source_system);
        }

        // Add automation information if rules were applied
        if ($this->ticket->automation_applied && count($this->ticket->automation_applied) > 0) {
            $mailMessage->line('**Automation Applied:**');
            foreach ($this->ticket->automation_applied as $rule) {
                $mailMessage->line('- ' . $rule['rule_name']);
            }
        }

        $mailMessage
            ->action('View Ticket', $ticketUrl)
            ->line('Please review and take appropriate action on this ticket.')
            ->line('Thank you for using our support system!');

        return $mailMessage;
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'ticket_id' => $this->ticket->id,
            'title' => $this->ticket->title,
            'content' => $this->ticket->getExcerpt(),
            'slug' => $this->ticket->slug,
            'priority' => $this->ticket->priority,
            'status' => $this->ticket->status,
            'department' => $this->ticket->department?->name,
            'requester_name' => $this->ticket->requester_name,
            'requester_email' => $this->ticket->requester_email,
            'source_system' => $this->ticket->source_system,
            'external_id' => $this->ticket->external_id,
            'product_name' => $this->ticket->product_name,
            'automation_applied' => $this->ticket->automation_applied ?? [],
            'message' => "New API ticket created: {$this->ticket->title}",
            'created_at' => $this->ticket->created_at->diffForHumans(),
            'ticket_url' => route('admin.tickets.show', $this->ticket->slug),
            'notification_type' => 'api_ticket_created',
        ];
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('API ticket notification failed', [
            'ticket_id' => $this->ticket->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
