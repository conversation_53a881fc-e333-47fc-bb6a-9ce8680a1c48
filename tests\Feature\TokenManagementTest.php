<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\TokenManagementService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;
use Tests\TestCase;

class TokenManagementTest extends TestCase
{
    use RefreshDatabase;

    private TokenManagementService $tokenService;
    private User $systemUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->tokenService = app(TokenManagementService::class);
        
        $this->systemUser = User::factory()->create([
            'is_system_user' => true,
            'system_type' => 'payment',
            'is_active' => true,
        ]);
    }

    public function test_can_create_token_for_system_user()
    {
        $result = $this->tokenService->createToken(
            $this->systemUser,
            'test-token',
            ['*'],
            'Test token description',
            now()->addDays(30)->toDateTimeString(),
            'api',
            ['***********'],
            1
        );

        $this->assertArrayHasKey('token', $result);
        $this->assertArrayHasKey('token_model', $result);
        $this->assertNotEmpty($result['token']);

        $tokenModel = $result['token_model'];
        $this->assertEquals('test-token', $tokenModel->name);
        $this->assertEquals('Test token description', $tokenModel->description);
        $this->assertEquals('api', $tokenModel->token_type);
        $this->assertEquals(['***********'], $tokenModel->allowed_ips);
        $this->assertTrue($tokenModel->is_active);
    }

    public function test_cannot_create_token_for_inactive_system_user()
    {
        $this->systemUser->update(['is_active' => false]);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('User must be an active system user');

        $this->tokenService->createToken($this->systemUser, 'test-token');
    }

    public function test_cannot_create_token_for_regular_user()
    {
        $regularUser = User::factory()->create(['is_system_user' => false]);

        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('User must be an active system user');

        $this->tokenService->createToken($regularUser, 'test-token');
    }

    public function test_can_revoke_token()
    {
        $result = $this->tokenService->createToken($this->systemUser, 'test-token');
        $token = $result['token_model'];

        $revoked = $this->tokenService->revokeToken($token, 'Test revocation', 1);

        $this->assertTrue($revoked);
        $this->assertDatabaseMissing('personal_access_tokens', ['id' => $token->id]);
    }

    public function test_can_rotate_token()
    {
        $result = $this->tokenService->createToken(
            $this->systemUser,
            'original-token',
            ['*'],
            'Original token',
            now()->addDays(30)->toDateTimeString(),
            'api'
        );
        
        $originalToken = $result['token_model'];
        $originalTokenId = $originalToken->id;

        $rotateResult = $this->tokenService->rotateToken(
            $originalToken,
            'rotated-token',
            'Token rotation test',
            1
        );

        $this->assertArrayHasKey('token', $rotateResult);
        $this->assertArrayHasKey('token_model', $rotateResult);

        $newToken = $rotateResult['token_model'];
        $this->assertEquals('rotated-token', $newToken->name);
        $this->assertEquals('Original token', $newToken->description);
        $this->assertEquals('api', $newToken->token_type);
        $this->assertTrue($newToken->is_active);

        // Original token should be deleted
        $this->assertDatabaseMissing('personal_access_tokens', ['id' => $originalTokenId]);
    }

    public function test_can_update_token()
    {
        $result = $this->tokenService->createToken($this->systemUser, 'test-token');
        $token = $result['token_model'];

        $updateData = [
            'description' => 'Updated description',
            'is_active' => false,
            'allowed_ips' => ['********', '********'],
        ];

        $updated = $this->tokenService->updateToken($token, $updateData, 1);

        $this->assertTrue($updated);
        
        $token->refresh();
        $this->assertEquals('Updated description', $token->description);
        $this->assertFalse($token->is_active);
        $this->assertEquals(['********', '********'], $token->allowed_ips);
    }

    public function test_can_get_user_tokens()
    {
        // Create multiple tokens
        $this->tokenService->createToken($this->systemUser, 'token-1');
        $this->tokenService->createToken($this->systemUser, 'token-2');

        $tokens = $this->tokenService->getUserTokens($this->systemUser);

        $this->assertCount(2, $tokens);
        $this->assertEquals('token-2', $tokens->first()->name); // Should be ordered by created_at desc
        $this->assertEquals('token-1', $tokens->last()->name);
    }

    public function test_can_get_token_statistics()
    {
        // Create tokens with different states
        $result1 = $this->tokenService->createToken($this->systemUser, 'active-token');
        $result2 = $this->tokenService->createToken($this->systemUser, 'expired-token');
        
        // Make one token expired
        $result2['token_model']->update(['expires_at' => now()->subDay()]);

        $stats = $this->tokenService->getTokenStatistics($this->systemUser);

        $this->assertEquals(2, $stats['total']);
        $this->assertEquals(1, $stats['active']);
        $this->assertEquals(1, $stats['expired']);
    }

    public function test_can_get_global_token_statistics()
    {
        // Create another system user
        $anotherSystemUser = User::factory()->create([
            'is_system_user' => true,
            'is_active' => true,
        ]);

        // Create tokens for both users
        $this->tokenService->createToken($this->systemUser, 'token-1');
        $this->tokenService->createToken($anotherSystemUser, 'token-2');

        $stats = $this->tokenService->getTokenStatistics(); // No user specified = global stats

        $this->assertEquals(2, $stats['total']);
        $this->assertEquals(2, $stats['active']);
    }
}
