<?php

namespace App\Http\Middleware;

use App\Services\RateLimitService;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SystemUserRateLimit
{
    public function __construct(
        private RateLimitService $rateLimitService
    ) {}

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user || !$user->isSystemUser()) {
            return $next($request);
        }

        // Check if user can make request
        $rateLimitCheck = $this->rateLimitService->canMakeRequest($user);

        if (!$rateLimitCheck['allowed']) {
            $this->rateLimitService->recordViolation($user, $rateLimitCheck['limit_type'] ?? 'unknown');

            return $this->buildRateLimitResponse(
                $rateLimitCheck['reason'],
                429,
                $rateLimitCheck['retry_after'] ?? 60,
                $user
            );
        }

        // Process the request
        $response = $next($request);

        // Record successful request
        $this->rateLimitService->recordRequest($user);

        // Add rate limit headers to response
        $this->addRateLimitHeaders($response, $user);

        // Update user's last API call timestamp
        $user->updateLastApiCall();

        return $response;
    }

    /**
     * Build rate limit exceeded response
     */
    private function buildRateLimitResponse(
        string $message,
        int $status,
        int $retryAfter,
        $user
    ): Response {
        $rateLimitStatus = $this->rateLimitService->getRateLimitStatus($user);

        $response = response()->json([
            'error' => $message,
            'message' => $message,
            'retry_after' => $retryAfter,
            'rate_limit' => [
                'tier' => $rateLimitStatus['tier'],
                'limits' => $rateLimitStatus['limits'],
                'usage' => $rateLimitStatus['usage'],
                'reset_times' => $rateLimitStatus['reset_times'],
                'violations' => $rateLimitStatus['violations'],
            ]
        ], $status);

        $response->headers->set('Retry-After', $retryAfter);
        $response->headers->set('X-RateLimit-Limit', $rateLimitStatus['limits']['per_minute']);
        $response->headers->set('X-RateLimit-Remaining', $rateLimitStatus['usage']['minute_remaining']);
        $response->headers->set('X-RateLimit-Reset', $rateLimitStatus['reset_times']['minute_reset']);
        $response->headers->set('X-RateLimit-Tier', $rateLimitStatus['tier']);

        return $response;
    }

    /**
     * Add rate limit headers to successful response
     */
    private function addRateLimitHeaders(Response $response, $user): void
    {
        $rateLimitStatus = $this->rateLimitService->getRateLimitStatus($user);

        $response->headers->set('X-RateLimit-Limit', $rateLimitStatus['limits']['per_minute']);
        $response->headers->set('X-RateLimit-Remaining', $rateLimitStatus['usage']['minute_remaining']);
        $response->headers->set('X-RateLimit-Reset', $rateLimitStatus['reset_times']['minute_reset']);
        $response->headers->set('X-RateLimit-Tier', $rateLimitStatus['tier']);
        $response->headers->set('X-RateLimit-Daily-Limit', $rateLimitStatus['limits']['daily']);
        $response->headers->set('X-RateLimit-Daily-Usage', $rateLimitStatus['usage']['daily']);
    }
}
