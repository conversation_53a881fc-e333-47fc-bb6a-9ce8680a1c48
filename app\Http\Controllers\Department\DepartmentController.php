<?php

namespace App\Http\Controllers\Department;

use App\Http\Controllers\Controller;
use App\Models\Departments;
use App\Models\Ticket;
use App\Models\User;
use App\Services\TicketService;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Spatie\Permission\Exceptions\UnauthorizedException;

class DepartmentController extends Controller
{
    use AuthorizesRequests;

    protected $ticketService;

    public function __construct(TicketService $ticketService)
    {
        $this->ticketService = $ticketService;
    }

    public function index(Request $request)
    {
        // Kiểm tra vai trò admin
        // if (! auth()->user()->hasRole('admin') || ! auth()->user()->hasRole('employee') ) {
        //     throw UnauthorizedException::forRoles(['admin']);
        // }

        $search = $request->input('search', '');

        $departments = Departments::query()
            ->when($search, function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('Departments/Index', [
            'departments' => [
                'data' => $departments->items(),
                'total' => $departments->total(),
                'per_page' => $departments->perPage(),
                'current_page' => $departments->currentPage(),
                'last_page' => $departments->lastPage(),
                'next_page_url' => $departments->nextPageUrl(),
                'prev_page_url' => $departments->previousPageUrl(),
            ],
            'keyword' => $search,
        ]);
    }

    // public function create()
    // {
    //     if (! auth()->user()->hasRole('admin')) {
    //         throw UnauthorizedException::forRoles(['admin']);
    //     }

    //     return Inertia::render('Departments/Create');
    // }

    public function store(Request $request)
    {
        // Đơn giản - chỉ Admin
        if (!auth()->user()->can('manage-departments')) {
            abort(403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:departments',
            'description' => 'nullable|string',

        ]);

        Departments::create([
            'name' => $validated['name'],
            'description' => $validated['description'],
            'slug' => Str::slug($validated['name']),
        ]);

        return redirect()->route('departments.index')->with('success', 'Department created successfully.');
    }

    public function show(string $slug)
    {

        $department = Departments::where('slug', $slug)->firstOrFail();
        $user = auth()->user();

        // Sử dụng policy để kiểm tra quyền
        // $this->authorize('', $department);       
        // Get notifications for the current user
        $notifications = auth()->user()->unreadNotifications;

        // Get all tickets belonging to this department
        $tickets = Ticket::where('department_id', $department->id)
            ->with(['user', 'categories', 'tags', 'assignee', 'department'])
            ->withCount('upvotes')
            ->withCount('comments')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($ticket) {
                // Get paginated comments
                $comments = $ticket->comments()
                    ->whereNull('parent_id')
                    ->with([
                        'user.roles',
                        'user.departments',
                        'replies.user.roles',
                        'replies.user.departments',
                    ])
                    ->latest()
                    ->paginate(5);

                $commentsData = [
                    'data' => $comments->items(),
                    'next_page_url' => $comments->nextPageUrl(),
                    'prev_page_url' => $comments->previousPageUrl(),
                    'current_page' => $comments->currentPage(),
                    'last_page' => $comments->lastPage(),
                    'per_page' => $comments->perPage(),
                    'total' => $comments->total(),
                    'from' => $comments->firstItem(),
                    'to' => $comments->lastItem(),
                ];

                $hasUpvoted = auth()->check() ? $ticket->isUpvotedBy(auth()->id()) : false;

                return [
                    'id' => $ticket->id,
                    'title' => $ticket->title,
                    'content' => $ticket->content,
                    'status' => $ticket->status,
                    'priority' => $ticket->priority,
                    'created_at' => $ticket->created_at->diffForHumans(),
                    'updated_at' => $ticket->updated_at,
                    'published_at' => $ticket->published_at,
                    'user' => $ticket->user,
                    'assignee' => $ticket->assignee,
                    'department' => $ticket->department,
                    'categories' => $ticket->categories,
                    'tags' => $ticket->tags,
                    'comments' => $commentsData,
                    'upvote_count' => $ticket->upvotes_count,
                    'has_upvote' => $hasUpvoted,
                    'product_name' => $ticket->product_name,
                ];
            });

        return Inertia::render('Departments/Show', [
            'department' => $department,
            'notifications' => $notifications,
            'tickets' => $tickets,
        ]);
    }

    public function edit(Departments $department)
    {
        // Đơn giản - chỉ Admin
        if (!auth()->user()->can('manage-departments')) {
            abort(403);
        }

        return Inertia::render('Departments/Edit', [
            'department' => $department,
        ]);
    }

    public function update(Request $request, Departments $department)
    {
        // Đơn giản - chỉ Admin
        if (!auth()->user()->can('manage-departments')) {
            abort(403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:departments,name,'.$department->id,
            'description' => 'nullable|string',
        ]);

        $department->update([
            'name' => $validated['name'],
            'description' => $validated['description'],
            'slug' => Str::slug($validated['name']),
        ]);

        return redirect()->route('departments.index')->with('success', 'Department updated successfully.');
    }

    public function destroy(Departments $department)
    {
        // Đơn giản - chỉ Admin
        if (!auth()->user()->can('manage-departments')) {
            abort(403);
        }

        $department->delete();

        return redirect()->route('departments.index')->with('success', 'Department deleted successfully.');
    }

    public function getTicketBySlug(string $slug)
    {
        $ticket = Ticket::getTicketBySlug($slug);
        $data = $this->ticketService->prepareTicketData($ticket);

        return Inertia::render('Departments/Show', $data);
    }

    /**
     * @throws AuthorizationException
     */
    public function getAvailableUsers(Departments $department)
    {
        // Đơn giản - chỉ Admin
        if (!auth()->user()->can('manage-departments')) {
            abort(403);
        }

        // Chỉ lấy danh sách người dùng chưa thuộc phòng ban nào
        $users = User::whereDoesntHave('departments')
            ->select(['id', 'name', 'email'])
            ->get();

        return response()->json($users);
    }

    public function getEmployee(string $slug)
    {

        // Lấy thông tin phòng ban
        $department = Departments::where('slug', $slug)->firstOrFail();
        $user = auth()->user();

        // Sử dụng policy để kiểm tra quyền
        // $this->authorize('viewDepartmentPosts', $department);

        // Lấy nhân sự trong phòng ban với phân trang
        $users = User::whereHas('departments', function ($query) use ($department) {
            $query->where('departments.id', $department->id);
        })
            ->with('roles')
            ->paginate(10)
            ->through(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'profile_photo_path' => $user->profile_photo_path,
                    'roles' => $user->roles->pluck('name')->implode(', '), // trả về chuỗi role
                ];
            });

        return Inertia::render('Departments/Employee', [
            'users' => $users,
            'department' => $department,
        ]);
    }

    public function addUser(Request $request, Departments $department)
    {
        // Đơn giản - chỉ Admin
        if (!auth()->user()->can('manage-departments')) {
            abort(403);
        }

        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $user = User::findOrFail($request->user_id);

        // Kiểm tra xem user đã thuộc phòng ban khác chưa
        // (Logic này đã được kiểm tra ở authorize trên)


        // Thêm user vào phòng ban
        $department->users()->attach($user->id);

        // Gán vai trò Employee nếu chưa có vai trò
        if (! $user->hasAnyRole(['Admin', 'department_manager', 'Employee'])) {
            $user->assignRole('Employee');
        }

        return redirect()->route('departments.employees', ['slug' => $department->slug])->with('success', 'User added to department');
    }

    public function removeUser(Request $request, Departments $department, User $user)
    {
        //        if (! auth()->user()->hasRole('admin')) {
        //            throw UnauthorizedException::forRoles(['admin']);
        //        }
        // $this->authorize('remove-users-from-department', $department);

        // Kiểm tra xem user có thuộc phòng ban này không
        if (! $user->departments()->where('departments.id', $department->id)->exists()) {
            return response()->json(['error' => 'User does not belong to this department'], 422);
        }

        // Không cho phép xóa chính mình khỏi phòng ban
        if ($user->id === auth()->id()) {
            return response()->json(['error' => 'You cannot remove yourself from the department'], 422);
        }

        // Không cho phép xóa Admin khỏi phòng ban
        if ($user->hasRole('Admin')) {
            return response()->json(['error' => 'Cannot remove Admin users from department'], 422);
        }

        // Xóa user khỏi phòng ban
        $department->users()->detach($user->id);

        return redirect()->back()->with('success', 'User removed from department');
    }
}
