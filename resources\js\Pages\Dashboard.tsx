import { AppSidebar } from "@/Components/dashboard/app-sidebar"
import { ChartAreaInteractive } from "@/Components/chart-area-interactive"
import { DataTable } from "@/Components/dashboard/data-table"
import { SectionCards } from "@/Components/dashboard/section-cards"
import { SiteHeader } from "@/Components/dashboard/site-header"
import { SidebarInset, SidebarProvider } from "@/Components/ui/sidebar"


import React from "react"
import AppLayout from "@/Layouts/AppLayout"

import { Category, Tag } from "@/types"


export default function Page({categories,tags}: {categories: Category[]  ,tags: Tag[]}) {
  return (
    <AppLayout title={"Hệ thống hỗ trợ khách hàng"} canLogin={true} canRegister={true} notifications={[]}>
       <div>
        <h1>Trung tâm support khác hàng</h1>
       </div>
    </AppLayout>
  )
}
