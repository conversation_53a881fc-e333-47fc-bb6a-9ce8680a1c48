<?php

namespace App\Notifications;

use App\Models\Ticket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class TicketCreatedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public Ticket $ticket;

    public function __construct(Ticket $ticket)
    {
        $this->ticket = $ticket;
    }

    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('New Ticket Created: ' . $this->ticket->title)
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line('A new ticket has been created in your department.')
            ->line('Title: ' . $this->ticket->title)
            ->line('Priority: ' . $this->ticket->priority)
            ->line('Status: ' . $this->ticket->status)
            ->action('View Ticket', route('tickets.show', $this->ticket->slug))
            ->line('Thank you for using our support system!');
    }

    public function toArray($notifiable)
    {
        return [
            'ticket_id' => $this->ticket->id,
            'title' => $this->ticket->title,
            'content' => $this->ticket->getExcerpt(),
            'slug' => $this->ticket->slug,
            'message' => "New ticket created: {$this->ticket->title}",
            'name' => $this->ticket->user->name,
            'profile_photo_url' => $this->ticket->user->profile_photo_path
                ? asset('storage/'.$this->ticket->user->profile_photo_path)
                : null,
            'tags' => $this->ticket->tags->pluck('name')->toArray(),
            'categories' => $this->ticket->categories->pluck('title')->toArray(),
            'product_id' => $this->ticket->product_id,
            'product_name' => $this->ticket->product_name,
            'created_at' => $this->ticket->created_at->diffForHumans(),
        ];
    }
}
