<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class UserPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any users.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view-users');
    }

    /**
     * Determine whether the user can view the specific user.
     */
    public function view(User $user, User $model): bool
    {
        // User có thể xem thông tin của chính mình
        if ($user->id === $model->id) {
            return true;
        }

        // Admin có thể xem thông tin của tất cả users
        return $user->can('view-users');
    }

    /**
     * Determine whether the user can create users.
     */
    public function create(User $user): bool
    {
        // Chỉ Super Admin và Admin có thể tạo user mới
        return $user->can('view-users');
    }

    /**
     * Determine whether the user can update the user.
     */
    public function update(User $user, User $model): bool
    {
        // User có thể update thông tin của chính mình
        if ($user->id === $model->id) {
            return true;
        }

        // Admin có thể update thông tin của users khác
        return $user->can('update-users');
    }

    /**
     * Determine whether the user can delete the user.
     */
    public function delete(User $user, User $model): bool
    {
        // Không thể tự xóa chính mình
        if ($user->id === $model->id) {
            return false;
        }

        // Super Admin không thể bị xóa bởi ai khác
        if ($model->hasRole('Super Admin')) {
            return false;
        }

        // Chỉ Super Admin và Admin có thể xóa users
        return $user->can('delete-users');
    }

    /**
     * Determine whether the user can assign roles to users.
     */
    public function assignRoles(User $user, User $model): bool
    {
        // Không thể tự gán role cho chính mình
        if ($user->id === $model->id) {
            return false;
        }

        // Super Admin không thể bị thay đổi role bởi ai khác
        if ($model->hasRole('Super Admin')) {
            return $user->hasRole('Super Admin');
        }

        return $user->can('assign-roles');
    }

    /**
     * Determine whether the user can restore the user.
     */
    public function restore(User $user, User $model): bool
    {
        return $user->can('delete-users');
    }

    /**
     * Determine whether the user can permanently delete the user.
     */
    public function forceDelete(User $user, User $model): bool
    {
        // Chỉ Super Admin có thể force delete
        return $user->hasRole('Super Admin');
    }
}
