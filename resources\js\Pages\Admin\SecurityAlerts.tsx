import React, { useState, useEffect } from 'react';
import { Head, router } from '@inertiajs/react';

import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Badge } from '@/Components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/Components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { RefreshCw, AlertTriangle, Shield, Activity, Eye, Clock } from 'lucide-react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';

interface SecurityAlert {
    type: string;
    severity: 'info' | 'warning' | 'error';
    message: string;
    details: Record<string, any>;
}

interface Props {
    alerts: SecurityAlert[];
    hours: number;
}

export default function SecurityAlerts({ alerts, hours }: Props) {
    const [autoRefresh, setAutoRefresh] = useState(false);
    const [selectedHours, setSelectedHours] = useState(hours);

    useEffect(() => {
        let interval: NodeJS.Timeout;
        
        if (autoRefresh) {
            interval = setInterval(() => {
                router.reload({ only: ['alerts'] });
            }, 30000); // Refresh every 30 seconds
        }

        return () => {
            if (interval) {
                clearInterval(interval);
            }
        };
    }, [autoRefresh]);

    const getSeverityIcon = (severity: string) => {
        switch (severity) {
            case 'error':
                return <AlertTriangle className="h-4 w-4 text-red-500" />;
            case 'warning':
                return <Shield className="h-4 w-4 text-yellow-500" />;
            case 'info':
                return <Activity className="h-4 w-4 text-blue-500" />;
            default:
                return <Eye className="h-4 w-4 text-gray-500" />;
        }
    };

    const getSeverityColor = (severity: string) => {
        switch (severity) {
            case 'error':
                return 'border-red-200 bg-red-50';
            case 'warning':
                return 'border-yellow-200 bg-yellow-50';
            case 'info':
                return 'border-blue-200 bg-blue-50';
            default:
                return 'border-gray-200 bg-gray-50';
        }
    };

    const getAlertTypeTitle = (type: string) => {
        const titles = {
            high_error_rate: 'High Error Rate Detected',
            suspicious_ip: 'Suspicious IP Activity',
            rate_limit_violations: 'Rate Limit Violations',
            auth_failures: 'Authentication Failures',
            token_abuse: 'Token Abuse Detected',
            unusual_activity: 'Unusual Activity Pattern',
        };
        return titles[type as keyof typeof titles] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    };

    const formatAlertDetails = (alert: SecurityAlert) => {
        switch (alert.type) {
            case 'high_error_rate':
                return `Error rate: ${alert.details.error_rate}%`;
            case 'suspicious_ip':
                return `IP: ${Object.keys(alert.details)[0]}, Requests: ${Object.values(alert.details)[0]}`;
            case 'rate_limit_violations':
                return `${alert.details.violations} violations detected`;
            case 'auth_failures':
                return `${alert.details.failures} failed authentication attempts`;
            default:
                return JSON.stringify(alert.details);
        }
    };

    const handleHoursChange = (newHours: string) => {
        setSelectedHours(parseInt(newHours));
        router.get(route('admin.audit.security-alerts'), { hours: newHours });
    };

    const alertsByType = alerts.reduce((acc, alert) => {
        if (!acc[alert.type]) {
            acc[alert.type] = [];
        }
        acc[alert.type].push(alert);
        return acc;
    }, {} as Record<string, SecurityAlert[]>);

    const severityCounts = alerts.reduce((acc, alert) => {
        acc[alert.severity] = (acc[alert.severity] || 0) + 1;
        return acc;
    }, {} as Record<string, number>);

    return (
        <AuthenticatedLayout
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                        Security Alerts
                    </h2>
                    <div className="flex gap-2 items-center">
                        <Select value={selectedHours.toString()} onValueChange={handleHoursChange}>
                            <SelectTrigger className="w-32">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="1">Last 1 hour</SelectItem>
                                <SelectItem value="6">Last 6 hours</SelectItem>
                                <SelectItem value="24">Last 24 hours</SelectItem>
                                <SelectItem value="72">Last 3 days</SelectItem>
                                <SelectItem value="168">Last week</SelectItem>
                            </SelectContent>
                        </Select>
                        <Button
                            variant="outline"
                            onClick={() => setAutoRefresh(!autoRefresh)}
                            className={autoRefresh ? 'bg-green-50 border-green-200' : ''}
                        >
                            <RefreshCw className={`w-4 h-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
                            {autoRefresh ? 'Auto Refresh On' : 'Auto Refresh Off'}
                        </Button>
                        <Button variant="outline" onClick={() => router.reload()}>
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Refresh
                        </Button>
                    </div>
                </div>
            }
        >
            <Head title="Security Alerts" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Alert Summary */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">Total Alerts</p>
                                        <p className="text-2xl font-bold">{alerts.length}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <AlertTriangle className="h-4 w-4 text-red-500" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">Critical</p>
                                        <p className="text-2xl font-bold text-red-600">{severityCounts.error || 0}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <Shield className="h-4 w-4 text-yellow-500" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">Warning</p>
                                        <p className="text-2xl font-bold text-yellow-600">{severityCounts.warning || 0}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <Activity className="h-4 w-4 text-blue-500" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">Info</p>
                                        <p className="text-2xl font-bold text-blue-600">{severityCounts.info || 0}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Time Range Info */}
                    <Card className="mb-6">
                        <CardContent className="pt-6">
                            <div className="flex items-center text-sm text-gray-600">
                                <Clock className="w-4 h-4 mr-2" />
                                Showing alerts from the last {selectedHours} hour{selectedHours !== 1 ? 's' : ''}
                                {autoRefresh && (
                                    <Badge variant="secondary" className="ml-4">
                                        Auto-refreshing every 30 seconds
                                    </Badge>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Alerts */}
                    {alerts.length === 0 ? (
                        <Card>
                            <CardContent className="pt-6">
                                <div className="text-center py-8">
                                    <Shield className="w-12 h-12 text-green-500 mx-auto mb-4" />
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Security Alerts</h3>
                                    <p className="text-gray-500">
                                        No security alerts detected in the last {selectedHours} hour{selectedHours !== 1 ? 's' : ''}. 
                                        Your system appears to be secure.
                                    </p>
                                </div>
                            </CardContent>
                        </Card>
                    ) : (
                        <div className="space-y-4">
                            {Object.entries(alertsByType).map(([type, typeAlerts]) => (
                                <Card key={type}>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2">
                                            {getSeverityIcon(typeAlerts[0].severity)}
                                            {getAlertTypeTitle(type)}
                                            <Badge variant="secondary">{typeAlerts.length}</Badge>
                                        </CardTitle>
                                        <CardDescription>
                                            {typeAlerts[0].message}
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-3">
                                            {typeAlerts.map((alert, index) => (
                                                <Alert key={index} className={getSeverityColor(alert.severity)}>
                                                    <div className="flex items-start justify-between">
                                                        <div className="flex items-start gap-2">
                                                            {getSeverityIcon(alert.severity)}
                                                            <div>
                                                                <AlertTitle className="text-sm font-medium">
                                                                    {alert.message}
                                                                </AlertTitle>
                                                                <AlertDescription className="text-sm mt-1">
                                                                    {formatAlertDetails(alert)}
                                                                </AlertDescription>
                                                            </div>
                                                        </div>
                                                        <Badge 
                                                            variant={
                                                                alert.severity === 'error' ? 'destructive' :
                                                                alert.severity === 'warning' ? 'default' : 'secondary'
                                                            }
                                                        >
                                                            {alert.severity}
                                                        </Badge>
                                                    </div>
                                                </Alert>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    )}

                    {/* Security Recommendations */}
                    <Card className="mt-6">
                        <CardHeader>
                            <CardTitle>Security Recommendations</CardTitle>
                            <CardDescription>
                                Best practices to maintain system security
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-3">
                                <div className="flex items-start gap-3">
                                    <Shield className="w-5 h-5 text-blue-500 mt-0.5" />
                                    <div>
                                        <h4 className="font-medium">Regular Token Rotation</h4>
                                        <p className="text-sm text-gray-600">
                                            Rotate API tokens regularly to minimize security risks from compromised tokens.
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <Activity className="w-5 h-5 text-green-500 mt-0.5" />
                                    <div>
                                        <h4 className="font-medium">Monitor Rate Limits</h4>
                                        <p className="text-sm text-gray-600">
                                            Keep an eye on rate limit violations to detect potential abuse or misconfigured clients.
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5" />
                                    <div>
                                        <h4 className="font-medium">IP Restrictions</h4>
                                        <p className="text-sm text-gray-600">
                                            Use IP restrictions for system users to limit access to known, trusted sources.
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start gap-3">
                                    <Eye className="w-5 h-5 text-purple-500 mt-0.5" />
                                    <div>
                                        <h4 className="font-medium">Regular Audit Reviews</h4>
                                        <p className="text-sm text-gray-600">
                                            Review audit logs regularly to identify patterns and potential security issues.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
