import React from 'react';
import { Input } from '@/Components/ui/input';
import { Search } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';

interface TicketTableFiltersProps {
  searchTerm: string;
  statusFilter: string;
  priorityFilter: string;
  assigneeFilter: string;
  departmentFilter: string;
  onSearchChange: (value: string) => void;
  onStatusFilterChange: (value: string) => void;
  onPriorityFilterChange: (value: string) => void;
  onAssigneeFilterChange: (value: string) => void;
  onDepartmentFilterChange: (value: string) => void;
}

export default function TicketTableFilters({
  searchTerm,
  statusFilter,
  priorityFilter,
  assigneeFilter,
  departmentFilter,
  onSearchChange,
  onStatusFilterChange,
  onPriorityFilterChange,
  onAssigneeFilterChange,
  onDepartmentFilterChange,
}: TicketTableFiltersProps) {
  return (
    <div className="flex flex-wrap gap-4 items-center">
      {/* Search */}
      <div className="flex-1 min-w-[200px]">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tickets..."
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Status Filter */}
      <Select value={statusFilter} onValueChange={onStatusFilterChange}>
        <SelectTrigger className="w-[160px]">
          <SelectValue placeholder="Status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tất cả trạng thái</SelectItem>
          <SelectItem value="open">Mở</SelectItem>
          <SelectItem value="in_progress">Đang xử lý</SelectItem>
          <SelectItem value="resolved">Đã giải quyết</SelectItem>
          <SelectItem value="closed">Đóng</SelectItem>
        </SelectContent>
      </Select>

      {/* Priority Filter */}
      <Select value={priorityFilter} onValueChange={onPriorityFilterChange}>
        <SelectTrigger className="w-[160px]">
          <SelectValue placeholder="Priority" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tất cả độ ưu tiên</SelectItem>
          <SelectItem value="urgent">Khẩn cấp</SelectItem>
          <SelectItem value="high">Cao</SelectItem>
          <SelectItem value="medium">Trung bình</SelectItem>
          <SelectItem value="low">Thấp</SelectItem>
        </SelectContent>
      </Select>

      {/* Assignee Filter */}
      <Select value={assigneeFilter} onValueChange={onAssigneeFilterChange}>
        <SelectTrigger className="w-[160px]">
          <SelectValue placeholder="Assignee" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tất cả người được giao</SelectItem>
          <SelectItem value="assigned">Đã giao</SelectItem>
          <SelectItem value="unassigned">Chưa giao</SelectItem>
        </SelectContent>
      </Select>

      {/* Department Filter */}
      <Select value={departmentFilter} onValueChange={onDepartmentFilterChange}>
        <SelectTrigger className="w-[160px]">
          <SelectValue placeholder="Department" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tất cả phòng ban</SelectItem>
          <SelectItem value="it">IT</SelectItem>
          <SelectItem value="hr">HR</SelectItem>
          <SelectItem value="finance">Finance</SelectItem>
          <SelectItem value="support">Support</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}
