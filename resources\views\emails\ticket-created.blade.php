<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Support Ticket Created - {{ config('app.name') }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .content {
            background-color: #ffffff;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .button {
            display: inline-block;
            background-color: #28a745;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 0;
        }
        .button:hover {
            background-color: #218838;
        }
        .ticket-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Support Ticket Created</h1>
        <p>Your support request has been successfully submitted.</p>
    </div>

    <div class="content">
        <h2>Hello {{ $user->name }},</h2>
        
        <p>Thank you for contacting our support team. Your ticket has been created and our team will review it shortly.</p>

        <div class="ticket-info">
            <h3>📋 Ticket Details</h3>
            <p><strong>Title:</strong> {{ $ticket->title }}</p>
            <p><strong>Ticket ID:</strong> #{{ $ticket->id }}</p>
            <p><strong>Status:</strong> {{ ucfirst($ticket->status) }}</p>
            <p><strong>Priority:</strong> {{ ucfirst($ticket->priority) }}</p>
            <p><strong>Created:</strong> {{ $ticket->created_at->format('M d, Y H:i') }}</p>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ $ticketUrl }}" class="button">View Your Ticket</a>
        </div>

        <h3>What happens next?</h3>
        <ul>
            <li>Our support team will review your ticket</li>
            <li>You'll receive email notifications for any updates</li>
            <li>You can track progress and add comments by clicking the link above</li>
            <li>Expected response time: Within 24 hours</li>
        </ul>

        <p>If you need to add more information to your ticket, please use the link above rather than replying to this email.</p>
    </div>

    <div class="footer">
        <p>This email was sent from {{ config('app.name') }} Support System.</p>
        <p>Ticket Reference: #{{ $ticket->id }}</p>
    </div>
</body>
</html>
