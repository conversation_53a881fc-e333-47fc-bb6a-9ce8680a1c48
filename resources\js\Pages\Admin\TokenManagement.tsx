import React, { useState } from 'react';
import { Head, router, useForm } from '@inertiajs/react';


import { But<PERSON> } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Badge } from '@/Components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/Components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Textarea } from '@/Components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/Components/ui/table';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/Components/ui/tabs';
import { Alert, AlertDescription } from '@/Components/ui/alert';
import { Copy, Plus, RotateCcw, Trash2, Eye, Key, Activity, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';
import { route } from 'ziggy-js';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';

interface SystemUser {
    id: number;
    name: string;
    email: string;
    system_type: string;
    tokens: Token[];
}

interface Token {
    id: number;
    name: string;
    description?: string;
    token_type: string;
    is_active: boolean;
    expires_at?: string;
    last_used_at?: string;
    usage_count: number;
    allowed_ips?: string[];
    created_at: string;
}

interface TokenStats {
    total: number;
    active: number;
    expired: number;
    recently_used: number;
    inactive: number;
}

interface Props {
    systemUsers: {
        data: SystemUser[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    tokenStats: TokenStats;
    filters: {
        search?: string;
        per_page: number;
    };
}

export default function TokenManagement({ systemUsers, tokenStats, filters }: Props) {
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showTokenModal, setShowTokenModal] = useState(false);
    const [newToken, setNewToken] = useState<string>('');
    const [selectedUser, setSelectedUser] = useState<SystemUser | null>(null);

    const { data, setData, post, processing, errors, reset } = useForm({
        user_id: '',
        name: '',
        description: '',
        expires_at: '',
        token_type: 'api',
        allowed_ips: [] as string[],
    });

    const handleCreateToken = (e: React.FormEvent) => {
        e.preventDefault();
        
        post(route('admin.tokens.store'), {
            onSuccess: (page) => {
                setShowCreateModal(false);
                reset();
                // Show the new token
                if (page.props.token) {
                    setNewToken(page.props.token as string);
                    setShowTokenModal(true);
                }
                toast.success('Token created successfully');
            },
            onError: () => toast.error('Failed to create token'),
        });
    };

    const handleRevokeToken = (token: Token) => {
        if (confirm(`Are you sure you want to revoke token "${token.name}"?`)) {
            router.delete(route('admin.tokens.destroy', token.id), {
                data: { reason: 'Revoked via admin dashboard' },
                onSuccess: () => toast.success('Token revoked successfully'),
                onError: () => toast.error('Failed to revoke token'),
            });
        }
    };

    const handleRotateToken = (token: Token) => {
        if (confirm(`Are you sure you want to rotate token "${token.name}"? The old token will be invalidated.`)) {
            router.post(route('admin.tokens.rotate', token.id), {
                reason: 'Token rotation via admin dashboard',
            }, {
                onSuccess: (page) => {
                    if (page.props.token) {
                        setNewToken(page.props.token as string);
                        setShowTokenModal(true);
                    }
                    toast.success('Token rotated successfully');
                },
                onError: () => toast.error('Failed to rotate token'),
            });
        }
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text);
        toast.success('Token copied to clipboard');
    };

    const getTokenStatus = (token: Token) => {
        if (!token.is_active) {
            return <Badge variant="secondary">Inactive</Badge>;
        }
        if (token.expires_at && new Date(token.expires_at) <= new Date()) {
            return <Badge variant="destructive">Expired</Badge>;
        }
        return <Badge variant="default">Active</Badge>;
    };

    const getTokenUsageInfo = (token: Token) => (
        <div className="space-y-1">
            <div className="text-sm font-medium">{token.usage_count} calls</div>
            <div className="text-xs text-gray-500">
                Last used: {token.last_used_at ? new Date(token.last_used_at).toLocaleDateString() : 'Never'}
            </div>
        </div>
    );
    console.log(systemUsers);
    return (
        <AuthenticatedLayout
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl  leading-tight">
                        API Token Management
                    </h2>
                    <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
                        <DialogTrigger asChild>
                            <Button>
                                <Plus className="w-4 h-4 mr-2" />
                                Create Token
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                            <DialogHeader>
                                <DialogTitle>Create New API Token</DialogTitle>
                                <DialogDescription>
                                    Create a new API token for a system user.
                                </DialogDescription>
                            </DialogHeader>
                            <form onSubmit={handleCreateToken} className="space-y-4">
                                <div>
                                    <Label htmlFor="user_id">System User</Label>
                                    <Select value={data.user_id} onValueChange={(value) => setData('user_id', value)}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select a system user" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {systemUsers.data.map((user) => (
                                                <SelectItem key={user.id} value={user.id.toString()}>
                                                    {user.name} ({user.email})
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.user_id && <p className="text-sm text-red-600">{errors.user_id}</p>}
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="name">Token Name</Label>
                                        <Input
                                            id="name"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            placeholder="payment-api-token"
                                        />
                                        {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                                    </div>
                                    <div>
                                        <Label htmlFor="token_type">Token Type</Label>
                                        <Select value={data.token_type} onValueChange={(value) => setData('token_type', value)}>
                                            <SelectTrigger>
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="api">API</SelectItem>
                                                <SelectItem value="webhook">Webhook</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Description of the token's purpose"
                                    />
                                </div>

                                <div>
                                    <Label htmlFor="expires_at">Expiration Date (Optional)</Label>
                                    <Input
                                        id="expires_at"
                                        type="datetime-local"
                                        value={data.expires_at}
                                        onChange={(e) => setData('expires_at', e.target.value)}
                                    />
                                </div>

                                <DialogFooter>
                                    <Button type="button" variant="outline" onClick={() => setShowCreateModal(false)}>
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        Create Token
                                    </Button>
                                </DialogFooter>
                            </form>
                        </DialogContent>
                    </Dialog>
                </div>
            }
        >
            <Head title="Token Management" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Token Statistics */}
                    <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <Key className="h-4 w-4 text-muted-foreground" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">Total Tokens</p>
                                        <p className="text-2xl font-bold">{tokenStats.total}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <Activity className="h-4 w-4 text-green-600" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">Active</p>
                                        <p className="text-2xl font-bold text-green-600">{tokenStats.active}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <AlertTriangle className="h-4 w-4 text-red-600" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">Expired</p>
                                        <p className="text-2xl font-bold text-red-600">{tokenStats.expired}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <Activity className="h-4 w-4 text-blue-600" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">Recently Used</p>
                                        <p className="text-2xl font-bold text-blue-600">{tokenStats.recently_used}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <div className="h-4 w-4 bg-gray-400 rounded-full" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">Inactive</p>
                                        <p className="text-2xl font-bold text-gray-600">{tokenStats.inactive}</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Search and Filters */}
                    <Card className="mb-6">
                        <CardContent className="pt-6">
                            <div className="flex gap-4">
                                <Input
                                    placeholder="Search system users..."
                                    defaultValue={filters.search}
                                    onChange={(e) => {
                                        router.get(route('admin.tokens.index'), 
                                            { ...filters, search: e.target.value },
                                            { preserveState: true, replace: true }
                                        );
                                    }}
                                    className="max-w-sm"
                                />
                            </div>
                        </CardContent>
                    </Card>

                    {/* System Users and Their Tokens */}
                    <div className="space-y-6">
                        {systemUsers.data.map((user) => (
                            <Card key={user.id}>
                                <CardHeader>
                                    <div className="flex justify-between items-center">
                                        <div>
                                            <CardTitle className="flex items-center gap-2">
                                                {user.name}
                                                <Badge variant="outline">{user.system_type}</Badge>
                                            </CardTitle>
                                            <CardDescription>{user.email}</CardDescription>
                                        </div>
                                        <Badge variant="secondary">
                                            {user.tokens.length} token{user.tokens.length !== 1 ? 's' : ''}
                                        </Badge>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    {user.tokens.length === 0 ? (
                                        <div className="text-center py-8 text-gray-500">
                                            No tokens created for this user yet.
                                        </div>
                                    ) : (
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead>Token Name</TableHead>
                                                    <TableHead>Type</TableHead>
                                                    <TableHead>Status</TableHead>
                                                    <TableHead>Usage</TableHead>
                                                    <TableHead>Expires</TableHead>
                                                    <TableHead>Created</TableHead>
                                                    <TableHead>Actions</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {user.tokens.map((token) => (
                                                    <TableRow key={token.id}>
                                                        <TableCell>
                                                            <div>
                                                                <div className="font-medium">{token.name}</div>
                                                                {token.description && (
                                                                    <div className="text-sm text-gray-500">{token.description}</div>
                                                                )}
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <Badge variant="outline">{token.token_type}</Badge>
                                                        </TableCell>
                                                        <TableCell>{getTokenStatus(token)}</TableCell>
                                                        <TableCell>{getTokenUsageInfo(token)}</TableCell>
                                                        <TableCell>
                                                            {token.expires_at ? (
                                                                <div className="text-sm">
                                                                    {new Date(token.expires_at).toLocaleDateString()}
                                                                </div>
                                                            ) : (
                                                                <span className="text-gray-400">Never</span>
                                                            )}
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="text-sm">
                                                                {new Date(token.created_at).toLocaleDateString()}
                                                            </div>
                                                        </TableCell>
                                                        <TableCell>
                                                            <div className="flex items-center gap-2">
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => handleRotateToken(token)}
                                                                    title="Rotate Token"
                                                                >
                                                                    <RotateCcw className="w-4 h-4" />
                                                                </Button>
                                                                <Button
                                                                    variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => handleRevokeToken(token)}
                                                                    className="text-red-600 hover:text-red-700"
                                                                    title="Revoke Token"
                                                                >
                                                                    <Trash2 className="w-4 h-4" />
                                                                </Button>
                                                            </div>
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    )}
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>
            </div>

            {/* New Token Modal */}
            <Dialog open={showTokenModal} onOpenChange={setShowTokenModal}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>New Token Created</DialogTitle>
                        <DialogDescription>
                            Please copy this token now. You won't be able to see it again!
                        </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                        <Alert>
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>
                                Store this token securely. It will not be shown again.
                            </AlertDescription>
                        </Alert>
                        <div className="flex items-center space-x-2">
                            <Input value={newToken} readOnly className="font-mono text-sm" />
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(newToken)}
                            >
                                <Copy className="w-4 h-4" />
                            </Button>
                        </div>
                    </div>
                    <DialogFooter>
                        <Button onClick={() => setShowTokenModal(false)}>
                            I've copied the token
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </AuthenticatedLayout>
    );
}
