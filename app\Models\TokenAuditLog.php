<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TokenAuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'token_id',
        'token_name',
        'tokenable_id',
        'tokenable_type',
        'action',
        'performed_by',
        'ip_address',
        'user_agent',
        'reason',
        'old_values',
        'new_values',
        'metadata',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the user who owns the token
     */
    public function tokenable(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tokenable_id');
    }

    /**
     * Get the user who performed the action
     */
    public function performedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'performed_by');
    }

    /**
     * Scope for specific action
     */
    public function scopeAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope for specific token
     */
    public function scopeForToken($query, string $tokenId)
    {
        return $query->where('token_id', $tokenId);
    }

    /**
     * Scope for specific user's tokens
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('tokenable_id', $userId);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Create audit log for token operation
     */
    public static function logTokenOperation(
        string $tokenId,
        string $tokenName,
        int $tokenableId,
        string $action,
        ?int $performedBy = null,
        ?string $reason = null,
        ?array $oldValues = null,
        ?array $newValues = null,
        ?array $metadata = null
    ): self {
        return self::create([
            'token_id' => $tokenId,
            'token_name' => $tokenName,
            'tokenable_id' => $tokenableId,
            'tokenable_type' => 'App\\Models\\User',
            'action' => $action,
            'performed_by' => $performedBy,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'reason' => $reason,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'metadata' => $metadata,
        ]);
    }
}
