<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolesAndPermissionsSeeder extends Seeder
{
    public function run()
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Xóa tất cả roles và permissions cũ để tránh conflict guard
        \DB::table('model_has_permissions')->delete();
        \DB::table('model_has_roles')->delete();
        \DB::table('role_has_permissions')->delete();
        Permission::query()->delete();
        Role::query()->delete();

        // Tạo các quyền hạn (Permissions) theo chiến lược
        $this->createPermissions();

        // Tạo các vai trò (Roles)
        $this->createRoles();

        // <PERSON><PERSON> quyền cho các vai trò
        $this->assignPermissionsToRoles();
    }

    private function createPermissions()
    {
        // Permissions theo yêu cầu mới
        $permissions = [
            // Admin Dashboard
            'access-admin-dashboard',

            // Posts/Tickets
            'view-any-posts',           // Admin: tất cả tickets
            'view-department-posts',    // Employee: tickets phòng mình + unassigned
            'update-any-posts',
            'delete-any-posts',
            'update-status-posts',
            'assign-tickets',             // Manager: gán tickets cho user

            // Users
            'manage-users',             // Admin: quản lý tất cả users
            'manage-department-users',  // Manager: thêm/xóa user trong phòng mình

            // Roles & Permissions
            'manage-roles-permissions', // Admin: quản lý roles

            // Categories & Tags
            'manage-categories',
            'manage-tags',

            // Departments
            'manage-departments',       // Admin: tất cả departments
            'manage-own-department',    // Manager: chỉ phòng mình

            // Comments
            'moderate-comments',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission, 'guard_name' => 'web']);
        }
    }

    private function createRoles()
    {
        $roles = [
            'Admin',      // Có toàn bộ quyền
            'Manager',    // Quản lý phòng ban của mình
            'Employee',   // Xem tickets của phòng mình + unassigned
            'Customer'    // Chỉ tạo tickets
        ];

        foreach ($roles as $role) {
            Role::firstOrCreate(['name' => $role, 'guard_name' => 'web']);
        }
    }

    private function assignPermissionsToRoles()
    {
        // Admin - có toàn bộ quyền
        $admin = Role::findByName('Admin');
        $admin->givePermissionTo(Permission::all());

        // Manager - quản lý phòng ban của mình
        $manager = Role::findByName('Manager');
        $managerPermissions = [
            'access-admin-dashboard',
            'view-department-posts',    // Xem tickets phòng mình
            'update-any-posts',
            'update-status-posts',
            'assign-tickets',             // Gán tickets cho user
            'manage-department-users',  // Thêm/xóa user trong phòng
            'manage-own-department',    // Quản lý phòng mình
            'moderate-comments',
        ];
        $manager->givePermissionTo($managerPermissions);

        // Employee - xem tickets phòng mình + unassigned
        $employee = Role::findByName('Employee');
        $employeePermissions = [
            'access-admin-dashboard',
            'view-department-posts',    // Tickets phòng mình + unassigned
            'update-status-posts',      // Cập nhật trạng thái
            'moderate-comments',
        ];
        $employee->givePermissionTo($employeePermissions);

        // Customer - chỉ tạo tickets
        $customer = Role::findByName('Customer');
        $customerPermissions = [
            // Không có permission admin nào
        ];
        $customer->givePermissionTo($customerPermissions);
    }
    
}
