<?php

namespace App\Console\Commands;

use App\Models\TokenAuditLog;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class IssueSystemToken extends Command
{
    protected $signature = 'system:issue-token 
                            {email : Email of the system user}
                            {token_name : Name for the token}
                            {--description= : Description of the token}
                            {--expires= : Expiration date (Y-m-d H:i:s)}
                            {--type=api : Token type (api, webhook)}
                            {--ips=* : Allowed IP addresses}';

    protected $description = 'Issue a Sanctum API token for a system user';

    public function handle()
    {
        $email = $this->argument('email');
        $tokenName = $this->argument('token_name');
        $description = $this->option('description');
        $expires = $this->option('expires');
        $type = $this->option('type');
        $allowedIps = $this->option('ips');

        // Find the user
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("User with email '{$email}' not found.");
            return self::FAILURE;
        }

        if (!$user->is_system_user) {
            $this->error("User '{$email}' is not a system user.");
            return self::FAILURE;
        }

        if (!$user->is_active) {
            $this->error("System user '{$email}' is not active.");
            return self::FAILURE;
        }

        // Create the token
        $token = $user->createToken($tokenName);
        $tokenModel = $token->accessToken;

        // Update token with additional fields
        $updateData = [
            'token_type' => $type,
            'description' => $description,
            'created_by_ip' => '127.0.0.1', // CLI command
            'is_active' => true,
        ];

        if ($expires) {
            $updateData['expires_at'] = $expires;
        }

        if (!empty($allowedIps)) {
            $updateData['allowed_ips'] = $allowedIps;
        }

        $tokenModel->update($updateData);

        // Log the token creation
        TokenAuditLog::logTokenOperation(
            $tokenModel->id,
            $tokenName,
            $user->id,
            'created',
            null, // CLI command, no user
            'Created via CLI command',
            null,
            $updateData,
            [
                'command' => 'system:issue-token',
                'expires_at' => $expires,
                'allowed_ips' => $allowedIps,
            ]
        );

        // Display the results
        $this->info("Token created successfully for system user: {$user->name}");
        $this->line("Email: {$user->email}");
        $this->line("System Type: {$user->system_type}");
        $this->line("Token Name: {$tokenName}");
        $this->line("Token Type: {$type}");
        
        if ($description) {
            $this->line("Description: {$description}");
        }
        
        if ($expires) {
            $this->line("Expires: {$expires}");
        }
        
        if (!empty($allowedIps)) {
            $this->line("Allowed IPs: " . implode(', ', $allowedIps));
        }

        $this->warn("Token: {$token->plainTextToken}");
        $this->info("Store this token securely. It will not be shown again.");

        return self::SUCCESS;
    }
}
