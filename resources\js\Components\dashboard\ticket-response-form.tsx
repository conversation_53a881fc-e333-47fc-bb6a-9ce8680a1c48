import React, { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/Components/ui/button"
import { Textarea } from "@/Components/ui/textarea"
import { Label } from "@/Components/ui/label"
import { Send, User, MessageSquare, AlertCircle, X } from "lucide-react"
import { useForm } from "@inertiajs/react"
import { toast } from "sonner"
import { CommentItem } from "./comment-item"
import { Ticket } from "@/types/ticket"
import { Comment } from "@/types/CommentTypes"

interface TicketResponseFormProps {
  ticket: Ticket
  onCommentAdded?: (comment: Comment) => void
  currentUser?: {
    id: number
    name: string
    email: string
    profile_photo_path?: string
  }
}

export function TicketResponseForm({ ticket, onCommentAdded, currentUser }: TicketResponseFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const initialComments = Array.isArray(ticket.comments)
    ? ticket.comments
    : (ticket.comments?.data || []);
  const [localComments, setLocalComments] = useState<Comment[]>(initialComments)
  const [replyingTo, setReplyingTo] = useState<string | null>(null)

  const channelRef = useRef<any>(null)
  const isUnmountedRef = useRef(false)

  const { data, setData, post, processing, errors, reset } = useForm({
    content: "",
    is_hr_response: false,
  })

  // Xử lý real-time comment
  useEffect(() => {
    if (!window.Echo || !ticket.id) return
    const channelName = `ticket.${ticket.id}`
    isUnmountedRef.current = false

    try {
      const channel = window.Echo.channel(channelName)
      channelRef.current = channel

      const handleCommentPosted = (e: { comment: Comment }) => {
        if (!isUnmountedRef.current && e.comment) {
          setLocalComments(prev => prev.filter(c => !(parseInt(c.id) > 1000000000000)))
          fetch(`/admin/tickets/${ticket.slug}/comments`)
            .then(response => response.json())
            .then(data => {
              setLocalComments(data.ticket.comments || [])
            })
            .catch(error => {
              console.error('Không tải được bình luận:', error)
            })

          if (onCommentAdded) {
            onCommentAdded(e.comment)
          }
        }
      }

      channel.listen('.CommentPosted', handleCommentPosted)

      return () => {
        isUnmountedRef.current = true
        if (channelRef.current) {
          try {
            channelRef.current.stopListening('.CommentPosted')
            window.Echo.leaveChannel(channelName)
          } catch (error) {
            console.warn('Lỗi khi hủy đăng ký channel:', error)
          }
        }
        channelRef.current = null
      }
    } catch (error) {
      console.error('Lỗi khi kết nối real-time:', error)
    }
  }, [ticket.id, onCommentAdded])

  useEffect(() => {
    const newComments = Array.isArray(ticket.comments)
      ? ticket.comments
      : (ticket.comments?.data || []);
    setLocalComments(newComments)
  }, [ticket.comments])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!data.content.trim()) {
      toast.error("Vui lòng nhập nội dung phản hồi.")
      return
    }

    const optimisticComment: Comment = {
      id: Date.now().toString(),
      content: data.content,
      created_at: new Date().toISOString(),
      user: currentUser ? {
        ...currentUser,
        profile_photo_path: currentUser.profile_photo_path || null
      } : {
        id: 0,
        name: 'Bạn',
        email: '',
        profile_photo_path: null
      },
      is_hr_response: data.is_hr_response,
      replies: []
    }

    setLocalComments(prev => [...prev, optimisticComment])
    setIsSubmitting(true)

    post(`/admin/tickets/${ticket.slug}/respond`, {
      onSuccess: () => {
        reset()
        setTimeout(() => {
          setLocalComments(prev => prev.filter(c => c.id !== optimisticComment.id))
        }, 3000)
      },
      onError: () => {
        toast.error("Gửi phản hồi thất bại. Vui lòng thử lại.")
        setLocalComments(prev => prev.filter(c => c.id !== optimisticComment.id))
      },
      onFinish: () => {
        setIsSubmitting(false)
      },
    })
  }

  return (
    <div className="container mx-auto p-6 bg-white dark:bg-[#0a0a0a] min-h-screen">
      {/* Tiêu đề ticket */}
      <div className="mb-8">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2 dark:text-white">{ticket.title}</h1>
            <p className="text-gray-600 dark:text-white">
              Phiếu #{ticket.id} • Tạo bởi {ticket.user.name}
            </p>
          </div>
        </div>

        <div className="prose max-w-none mb-6">
          <div className="text-gray-700 dark:text-white leading-relaxed text-lg" 
          dangerouslySetInnerHTML={{ __html: ticket.content }} />
        </div>

        <div className="flex items-center gap-6 text-sm text-gray-500 pb-8 border-b border-gray-200">
          {ticket.assignee && (
            <div className="flex items-center gap-2">
              <User className="h-4 w-4" />
              <span>Giao cho {ticket.assignee.name}</span>
            </div>
          )}
          {ticket.department && (
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <span>{ticket.department.name}</span>
            </div>
          )}
        </div>
      </div>

      {/* Form phản hồi (đưa lên trên) */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2 flex items-center gap-2">
          <Send className="h-5 w-5" />
          Thêm phản hồi
        </h2>
        <p className="text-gray-600 mb-6">
          Nhập phản hồi cho phiếu này. Nội dung sẽ hiển thị với người tạo phiếu.
        </p>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            {/* <Label htmlFor="content" className="text-base font-medium text-gray-700 mb-3 block">
              Nội dung phản hồi
            </Label> */}
            <Textarea
              id="content"
              placeholder="Nhập phản hồi..."
              value={data.content}
              onChange={(e) => setData("content", e.target.value)}
              rows={4} // textarea nhỏ lại
              className="resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-base"
              disabled={processing || isSubmitting}
            />
            {errors.content && (
              <p className="text-red-600 mt-2 flex items-center gap-1">
                <AlertCircle className="h-4 w-4" />
                {errors.content}
              </p>
            )}
          </div>

          <div className="flex items-center justify-between pt-6">
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="is_hr_response"
                checked={data.is_hr_response}
                onChange={(e) => setData("is_hr_response", e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 h-4 w-4"
              />
              <Label htmlFor="is_hr_response" className="text-gray-700">
                Đánh dấu là phản hồi của HR
              </Label>
            </div>

            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => reset()}
                disabled={processing || isSubmitting}
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                <X className="h-4 w-4 mr-2" />
                Xóa nội dung
              </Button>
              <Button
                type="submit"
                disabled={processing || isSubmitting || !data.content.trim()}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6"
              >
                <Send className="h-4 w-4 mr-2" />
                {processing || isSubmitting ? "Đang gửi..." : "Gửi phản hồi"}
              </Button>
            </div>
          </div>
        </form>
      </div>

      {/* Danh sách bình luận */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Các phản hồi ({localComments?.length || 0})
        </h2>

        {localComments && localComments.length > 0 ? (
          <div className="space-y-6">
            {localComments.filter(comment => !comment.parent_id).map((comment) => (
              <CommentItem
                key={comment.id}
                comment={comment}
                currentUser={currentUser}
                onReply={(commentId) => setReplyingTo(commentId)}
                replyingTo={replyingTo}
                onCancelReply={() => setReplyingTo(null)}
                onSubmitReply={() => {}}
                allComments={localComments}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500 border border-gray-200 rounded-lg bg-gray-50">
            <MessageSquare className="w-12 h-12 mx-auto mb-4 text-gray-400" />
            <p>Chưa có phản hồi nào. Hãy là người đầu tiên phản hồi.</p>
          </div>
        )}
      </div>
    </div>
  )
}
