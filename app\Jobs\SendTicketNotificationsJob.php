<?php

namespace App\Jobs;

use App\Models\Ticket;
use App\Models\User;
use App\Notifications\TicketCreatedNotification;
use App\Notifications\ApiTicketCreatedNotification;
use App\Services\AdminService;
use App\Services\TicketLoggingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class SendTicketNotificationsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public Ticket $ticket;
    public int $tries = 3;
    public int $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct(Ticket $ticket)
    {
        $this->ticket = $ticket;
        $this->onQueue('notifications');
    }

    /**
     * Execute the job.
     */
    public function handle(AdminService $adminService, TicketLoggingService $loggingService): void
    {
        try {
            // Get users to notify
            $usersToNotify = $adminService->getUsersToNotify($this->ticket);

            if (empty($usersToNotify)) {
                Log::warning('No users found to notify for ticket', [
                    'ticket_id' => $this->ticket->id,
                ]);
                return;
            }

            // Log notification dispatch attempt
            $loggingService->logNotificationDispatchAttempt($this->ticket, count($usersToNotify));

            // Send notifications to all users
            $notificationsSent = 0;
            $notificationsFailed = 0;

            foreach ($usersToNotify as $user) {
                try {
                    // Use enhanced notification for API-created tickets
                    $notification = $this->ticket->source_system
                        ? new ApiTicketCreatedNotification($this->ticket)
                        : new TicketCreatedNotification($this->ticket);

                    $notificationType = $this->ticket->source_system ? 'api' : 'standard';

                    $user->notify($notification);
                    $notificationsSent++;

                    // Log successful notification
                    $loggingService->logNotificationDispatchSuccess($this->ticket, $user, $notificationType);

                } catch (\Exception $e) {
                    $notificationsFailed++;

                    // Log failed notification
                    $notificationType = $this->ticket->source_system ? 'api' : 'standard';
                    $loggingService->logNotificationDispatchFailure($this->ticket, $user, $e->getMessage(), $notificationType);
                }
            }

            // Log job completion
            $loggingService->logNotificationJobCompletion(
                $this->ticket,
                $notificationsSent,
                $notificationsFailed,
                count($usersToNotify)
            );

        } catch (\Exception $e) {
            // Log job failure
            $loggingService->logNotificationJobFailure($this->ticket, $e->getMessage(), $this->attempts());

            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        $loggingService = app(TicketLoggingService::class);
        $loggingService->logNotificationJobFailure($this->ticket, $exception->getMessage(), $this->attempts());
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'ticket:' . $this->ticket->id,
            'notifications',
            'ticket-created',
        ];
    }
}
