import React, { useState, useEffect } from 'react';
import { Head, router } from '@inertiajs/react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Badge } from '@/Components/ui/badge';
import { Progress } from '@/Components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/Components/ui/tabs';
import { 
    Activity, 
    Users, 
    Key, 
    AlertTriangle, 
    TrendingUp, 
    Clock, 
    Shield, 
    RefreshCw,
    Eye,
    CheckCircle,
    XCircle
} from 'lucide-react';

interface DashboardStats {
    system_users: {
        total: number;
        active: number;
        inactive: number;
    };
    tokens: {
        total: number;
        active: number;
        expired: number;
        recently_used: number;
    };
    api_requests: {
        total_today: number;
        successful_today: number;
        failed_today: number;
        success_rate: number;
        avg_response_time: number;
    };
    security: {
        alerts_count: number;
        rate_limit_violations: number;
        auth_failures: number;
        suspicious_ips: number;
    };
    top_users: Array<{
        id: number;
        name: string;
        system_type: string;
        requests_today: number;
        success_rate: number;
    }>;
    recent_activities: Array<{
        id: number;
        type: string;
        message: string;
        timestamp: string;
        severity: string;
    }>;
}

interface Props {
    stats: DashboardStats;
}

export default function ApiDashboard({ stats }: Props) {
    const [autoRefresh, setAutoRefresh] = useState(false);
    const [lastUpdated, setLastUpdated] = useState(new Date());

    useEffect(() => {
        let interval: NodeJS.Timeout;
        
        if (autoRefresh) {
            interval = setInterval(() => {
                router.reload({ only: ['stats'] });
                setLastUpdated(new Date());
            }, 30000); // Refresh every 30 seconds
        }

        return () => {
            if (interval) {
                clearInterval(interval);
            }
        };
    }, [autoRefresh]);

    const getActivityIcon = (type: string) => {
        switch (type) {
            case 'token_created':
                return <Key className="w-4 h-4 text-green-500" />;
            case 'token_revoked':
                return <XCircle className="w-4 h-4 text-red-500" />;
            case 'user_created':
                return <Users className="w-4 h-4 text-blue-500" />;
            case 'security_alert':
                return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
            default:
                return <Activity className="w-4 h-4 text-gray-500" />;
        }
    };

    const getSeverityColor = (severity: string) => {
        switch (severity) {
            case 'error':
                return 'text-red-600';
            case 'warning':
                return 'text-yellow-600';
            case 'info':
                return 'text-blue-600';
            default:
                return 'text-gray-600';
        }
    };

    return (
        <AuthenticatedLayout
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                        API Management Dashboard
                    </h2>
                    <div className="flex gap-2 items-center">
                        <span className="text-sm text-gray-500">
                            Last updated: {lastUpdated.toLocaleTimeString()}
                        </span>
                        <Button
                            variant="outline"
                            onClick={() => setAutoRefresh(!autoRefresh)}
                            className={autoRefresh ? 'bg-green-50 border-green-200' : ''}
                        >
                            <RefreshCw className={`w-4 h-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
                            {autoRefresh ? 'Auto Refresh On' : 'Auto Refresh Off'}
                        </Button>
                        <Button variant="outline" onClick={() => router.reload()}>
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Refresh
                        </Button>
                    </div>
                </div>
            }
        >
            <Head title="API Dashboard" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Overview Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <Users className="h-4 w-4 text-muted-foreground" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">System Users</p>
                                        <p className="text-2xl font-bold">{stats.system_users.total}</p>
                                        <p className="text-xs text-muted-foreground">
                                            {stats.system_users.active} active
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <Key className="h-4 w-4 text-muted-foreground" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">API Tokens</p>
                                        <p className="text-2xl font-bold">{stats.tokens.total}</p>
                                        <p className="text-xs text-muted-foreground">
                                            {stats.tokens.active} active
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">Requests Today</p>
                                        <p className="text-2xl font-bold">{stats.api_requests.total_today.toLocaleString()}</p>
                                        <p className="text-xs text-muted-foreground">
                                            {stats.api_requests.success_rate}% success rate
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardContent className="pt-6">
                                <div className="flex items-center">
                                    <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                                    <div className="ml-2">
                                        <p className="text-sm font-medium leading-none">Security Alerts</p>
                                        <p className="text-2xl font-bold">{stats.security.alerts_count}</p>
                                        <p className="text-xs text-muted-foreground">
                                            Last 24 hours
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    <Tabs defaultValue="overview" className="space-y-6">
                        <TabsList>
                            <TabsTrigger value="overview">Overview</TabsTrigger>
                            <TabsTrigger value="performance">Performance</TabsTrigger>
                            <TabsTrigger value="security">Security</TabsTrigger>
                            <TabsTrigger value="activity">Activity</TabsTrigger>
                        </TabsList>

                        <TabsContent value="overview" className="space-y-6">
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                {/* API Performance */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>API Performance Today</CardTitle>
                                        <CardDescription>Request statistics and response times</CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm">Success Rate</span>
                                            <span className="font-medium">{stats.api_requests.success_rate}%</span>
                                        </div>
                                        <Progress value={stats.api_requests.success_rate} className="h-2" />
                                        
                                        <div className="grid grid-cols-2 gap-4 pt-4">
                                            <div className="text-center">
                                                <p className="text-2xl font-bold text-green-600">
                                                    {stats.api_requests.successful_today.toLocaleString()}
                                                </p>
                                                <p className="text-sm text-muted-foreground">Successful</p>
                                            </div>
                                            <div className="text-center">
                                                <p className="text-2xl font-bold text-red-600">
                                                    {stats.api_requests.failed_today.toLocaleString()}
                                                </p>
                                                <p className="text-sm text-muted-foreground">Failed</p>
                                            </div>
                                        </div>

                                        <div className="pt-4 border-t">
                                            <div className="flex justify-between items-center">
                                                <span className="text-sm">Avg Response Time</span>
                                                <span className="font-medium">{stats.api_requests.avg_response_time.toFixed(0)}ms</span>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Top System Users */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Top System Users</CardTitle>
                                        <CardDescription>Most active users today</CardDescription>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-4">
                                            {stats.top_users.map((user, index) => (
                                                <div key={user.id} className="flex items-center justify-between">
                                                    <div className="flex items-center gap-3">
                                                        <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center text-sm font-medium">
                                                            {index + 1}
                                                        </div>
                                                        <div>
                                                            <p className="font-medium text-sm">{user.name}</p>
                                                            <p className="text-xs text-muted-foreground">{user.system_type}</p>
                                                        </div>
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="font-medium text-sm">{user.requests_today.toLocaleString()}</p>
                                                        <p className="text-xs text-muted-foreground">{user.success_rate}% success</p>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>

                        <TabsContent value="security" className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <Card>
                                    <CardContent className="pt-6">
                                        <div className="flex items-center">
                                            <Shield className="h-4 w-4 text-red-500" />
                                            <div className="ml-2">
                                                <p className="text-sm font-medium leading-none">Rate Limit Violations</p>
                                                <p className="text-2xl font-bold">{stats.security.rate_limit_violations}</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardContent className="pt-6">
                                        <div className="flex items-center">
                                            <XCircle className="h-4 w-4 text-yellow-500" />
                                            <div className="ml-2">
                                                <p className="text-sm font-medium leading-none">Auth Failures</p>
                                                <p className="text-2xl font-bold">{stats.security.auth_failures}</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardContent className="pt-6">
                                        <div className="flex items-center">
                                            <AlertTriangle className="h-4 w-4 text-orange-500" />
                                            <div className="ml-2">
                                                <p className="text-sm font-medium leading-none">Suspicious IPs</p>
                                                <p className="text-2xl font-bold">{stats.security.suspicious_ips}</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>

                        <TabsContent value="activity" className="space-y-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Recent Activities</CardTitle>
                                    <CardDescription>Latest system events and operations</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-4">
                                        {stats.recent_activities.map((activity) => (
                                            <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg border">
                                                {getActivityIcon(activity.type)}
                                                <div className="flex-1">
                                                    <p className={`text-sm font-medium ${getSeverityColor(activity.severity)}`}>
                                                        {activity.message}
                                                    </p>
                                                    <p className="text-xs text-muted-foreground">
                                                        {new Date(activity.timestamp).toLocaleString()}
                                                    </p>
                                                </div>
                                                <Badge variant={activity.severity === 'error' ? 'destructive' : 'secondary'}>
                                                    {activity.severity}
                                                </Badge>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>

                    {/* Quick Actions */}
                    <Card className="mt-6">
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                            <CardDescription>Common administrative tasks</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex-col"
                                    onClick={() => router.visit(route('admin.system-users.index'))}
                                >
                                    <Users className="w-6 h-6 mb-2" />
                                    Manage Users
                                </Button>
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex-col"
                                    onClick={() => router.visit(route('admin.tokens.index'))}
                                >
                                    <Key className="w-6 h-6 mb-2" />
                                    Manage Tokens
                                </Button>
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex-col"
                                    onClick={() => router.visit(route('admin.audit.api-logs'))}
                                >
                                    <Eye className="w-6 h-6 mb-2" />
                                    View Logs
                                </Button>
                                <Button 
                                    variant="outline" 
                                    className="h-20 flex-col"
                                    onClick={() => router.visit(route('admin.audit.security-alerts'))}
                                >
                                    <AlertTriangle className="w-6 h-6 mb-2" />
                                    Security Alerts
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
