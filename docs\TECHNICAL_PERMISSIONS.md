# 🔧 Technical Documentation - Permission System

## 🏗️ Architecture

### Backend (Laravel)
- **Package**: `spatie/laravel-permission`
- **Models**: `Role`, `Permission`, `User`
- **Middleware**: `admin.access`, `role.permission.access`

### Frontend (React)
- **Package**: `@casl/ability`, `@casl/react`
- **Context**: `AbilityContext`
- **Hook**: `useAbility()`

---

## 📁 File Structure

```
app/
├── Models/User.php                    # hasRole(), can() methods
├── Http/Middleware/
│   ├── CheckAdminAccess.php          # admin.access middleware
│   └── CheckRolePermissionAccess.php # role.permission.access
├── Http/Controllers/
│   ├── Admin/AdminController.php     # Dashboard với permission checks
│   ├── Role/RoleController.php       # Role management
│   └── Permission/PermissionController.php
└── Services/AdminService.php         # Business logic với role filtering

database/seeders/
├── RolesAndPermissionsSeeder.php     # Tạo roles & permissions
├── AdminUserSeeder.php               # Tạo test users
└── NewSystemCheckSeeder.php          # Debug permissions

resources/js/
├── Context/AbilityContext.tsx        # CASL context provider
├── Utils/ability.ts                  # Permission mapping logic
├── Components/Navigation/
│   ├── DesktopNavigation.tsx         # Desktop nav với permissions
│   └── MobileNavigation.tsx          # Mobile nav với permissions
└── Components/dashboard/
    ├── app-sidebar.tsx               # Admin sidebar filtering
    └── advanced-ticket-table.tsx     # Table actions filtering
```

---

## 🔑 Permission Mapping

### Laravel → CASL Mapping
```typescript
// app/Utils/ability.ts
function mapPermissionToRule(permission: string, can: Function) {
  switch (permission) {
    case 'access-admin-dashboard':
      can('access-admin-dashboard', 'AdminDashboard');
      break;
    case 'view-any-posts':
      can('view-any', 'Post');
      break;
    case 'assign-posts':
      can('assign', 'Post');
      break;
    // ... more mappings
  }
}
```

### Usage in Components
```typescript
// Check permission
const ability = useAbility();
const canAccess = ability.can('access-admin-dashboard', 'AdminDashboard');

// Conditional rendering
{canAccess && <AdminLink />}
```

---

## 🛡️ Middleware

### admin.access
```php
// app/Http/Middleware/CheckAdminAccess.php
if (!$user->can('access-admin-dashboard')) {
    abort(403, 'Bạn không có quyền truy cập vào khu vực quản trị.');
}
```

### role.permission.access
```php
// app/Http/Middleware/CheckRolePermissionAccess.php
if (!$user->can('manage-roles-permissions')) {
    abort(403, 'Bạn không có quyền quản lý vai trò và phân quyền.');
}
```

---

## 📊 Database Schema

### Roles Table
```sql
roles: id, name, guard_name, created_at, updated_at
```

### Permissions Table  
```sql
permissions: id, name, guard_name, created_at, updated_at
```

### Role-Permission Pivot
```sql
role_has_permissions: permission_id, role_id
```

### User-Role Pivot
```sql
model_has_roles: role_id, model_type, model_id
```

---

## 🎯 Business Logic

### AdminService Filtering
```php
// app/Services/AdminService.php
if ($currentUser && !$currentUser->hasRole('Admin')) {
    if ($currentUser->hasRole('Manager')) {
        // Manager: chỉ thấy tickets phòng mình
        $departmentIds = $currentUser->departments()->pluck('departments.id');
        $query->whereIn('department_id', $departmentIds);
    } elseif ($currentUser->hasRole('Employee')) {
        // Employee: tickets phòng mình + unassigned
        $query->where(function($q) use ($departmentIds) {
            $q->whereIn('department_id', $departmentIds)
              ->orWhereNull('department_id');
        });
    }
}
```

---

## 🔄 Seeder Commands

```bash
# Tạo roles & permissions
php artisan db:seed --class=RolesAndPermissionsSeeder

# Tạo test users
php artisan db:seed --class=AdminUserSeeder

# Debug permissions
php artisan db:seed --class=NewSystemCheckSeeder
```

---

## 🧪 Testing Permissions

### Backend Test
```php
// Test user permissions
$user = User::find(1);
$user->can('access-admin-dashboard'); // true/false
$user->hasRole('Admin'); // true/false
```

### Frontend Test
```typescript
// Test CASL abilities
const ability = useAbility();
ability.can('access-admin-dashboard', 'AdminDashboard'); // true/false
```

### Debug Route
```
GET /admin-test
// Returns user permissions info
```

---

## 🚨 Common Issues

### 1. Permission Not Working
```bash
# Clear cache
php artisan cache:clear
php artisan config:clear

# Re-run seeders
php artisan db:seed --class=RolesAndPermissionsSeeder
```

### 2. UI Not Updating
```bash
# Check CASL mapping in ability.ts
# Ensure permission name matches exactly
```

### 3. Middleware Blocking
```bash
# Check middleware registration in routes/web.php
# Verify user has correct role assigned
```

---

## 📝 Adding New Permission

### 1. Backend
```php
// Add to RolesAndPermissionsSeeder.php
'new-permission-name',

// Add to role assignment
$role->givePermissionTo(['new-permission-name']);
```

### 2. Frontend
```typescript
// Add to ability.ts
case 'new-permission-name':
  can('new-action', 'NewSubject');
  break;
```

### 3. Usage
```typescript
// Use in component
{ability.can('new-action', 'NewSubject') && <NewButton />}
```

---

*Hệ thống được thiết kế để dễ mở rộng và bảo trì*
