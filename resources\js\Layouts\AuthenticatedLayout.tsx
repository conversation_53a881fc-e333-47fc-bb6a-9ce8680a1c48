import React, { PropsWithChildren, ReactNode } from 'react';
import { User } from '@/types';

import { AppSidebar } from '@/Components/dashboard/app-sidebar';
import { SidebarInset, SidebarProvider } from '@/Components/ui/sidebar';
import { Separator } from '@/Components/ui/separator';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/Components/ui/breadcrumb';
import { SidebarTrigger } from '@/Components/ui/sidebar';
import useTypedPage from '@/Hooks/useTypedPage';

interface Props {
    header?: ReactNode;
    children: ReactNode;
}

export default function AuthenticatedLayout({ header, children }: PropsWithChildren<Props>) {
    const user = useTypedPage().props.auth.user ;

    return (
        <SidebarProvider>
            <AppSidebar  />
            <SidebarInset>
                <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 dark:bg-black">
                    <div className="flex items-center gap-2 px-4">
                        <SidebarTrigger className="-ml-1" />
                        <Separator orientation="vertical" className="mr-2 h-4" />
                        <Breadcrumb>
                            <BreadcrumbList>
                                <BreadcrumbItem className="hidden md:block">
                                    <BreadcrumbLink href="/">
                                        Dashboard
                                    </BreadcrumbLink>
                                </BreadcrumbItem>
                                <BreadcrumbSeparator className="hidden md:block" />
                                <BreadcrumbItem>
                                    <BreadcrumbPage>Admin</BreadcrumbPage>
                                </BreadcrumbItem>
                            </BreadcrumbList>
                        </Breadcrumb>
                    </div>
                </header>
                
                {header && (
                    <div className="flex flex-col gap-4 p-4 pt-0">
                        <div className="min-h-[100vh] flex-1 rounded-xl  md:min-h-min">
                            <div className=" shadow">
                                <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 dark:text-white">
                                    {header}
                                </div>
                            </div>
                        </div>
                    </div>
                )}
                
                <div className="flex flex-1 flex-col gap-4 p-4 pt-0 ">
                    <div className="min-h-[100vh] flex-1 rounded-xl  md:min-h-min">
                        <main className=" min-h-screen">
                            {children}
                        </main>
                    </div>
                </div>
            </SidebarInset>
        </SidebarProvider>
    );
}
