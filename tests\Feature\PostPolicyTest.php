<?php

namespace Tests\Feature;

use App\Models\Post;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class PostPolicyTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Tạo permissions
        Permission::create(['name' => 'view-public-posts']);
        Permission::create(['name' => 'view-any-posts']);
        Permission::create(['name' => 'view-own-posts']);
        Permission::create(['name' => 'create-posts']);
        Permission::create(['name' => 'update-any-posts']);
        Permission::create(['name' => 'update-own-posts']);
        Permission::create(['name' => 'delete-any-posts']);
        Permission::create(['name' => 'delete-own-posts']);

        // Tạo roles
        $superAdmin = Role::create(['name' => 'Super Admin']);
        $admin = Role::create(['name' => 'Admin']);
        $employee = Role::create(['name' => 'Employee']);
        $customer = Role::create(['name' => 'Customer']);

        // Gán permissions cho roles
        $superAdmin->givePermissionTo(Permission::all());
        $admin->givePermissionTo([
            'view-public-posts', 'view-any-posts', 'create-posts',
            'update-any-posts', 'delete-any-posts'
        ]);
        $employee->givePermissionTo([
            'view-public-posts', 'view-any-posts', 'create-posts', 'update-any-posts'
        ]);
        $customer->givePermissionTo([
            'view-public-posts', 'view-own-posts', 'create-posts',
            'update-own-posts', 'delete-own-posts'
        ]);
    }

    public function test_guest_can_view_public_posts()
    {
        $post = Post::factory()->create(['is_published' => true]);

        $this->assertTrue($this->app['auth']->guard()->guest());
        $this->assertTrue($this->app['gate']->forUser(null)->allows('view', $post));
    }

    public function test_guest_cannot_view_private_posts()
    {
        $post = Post::factory()->create(['is_published' => false]);

        $this->assertTrue($this->app['auth']->guard()->guest());
        $this->assertFalse($this->app['gate']->forUser(null)->allows('view', $post));
    }

    public function test_guest_can_create_posts()
    {
        // Ai cũng có thể tạo tickets/posts, kể cả guest
        $this->assertTrue($this->app['gate']->forUser(null)->allows('create', Post::class));
    }

    public function test_customer_can_view_own_private_posts()
    {
        $customer = User::factory()->create();
        $customer->assignRole('Customer');
        
        $post = Post::factory()->create([
            'user_id' => $customer->id,
            'is_published' => false
        ]);
        
        $this->assertTrue($this->app['gate']->forUser($customer)->allows('view', $post));
    }

    public function test_customer_cannot_view_others_private_posts()
    {
        $customer = User::factory()->create();
        $customer->assignRole('Customer');
        
        $otherUser = User::factory()->create();
        $post = Post::factory()->create([
            'user_id' => $otherUser->id,
            'is_published' => false
        ]);
        
        $this->assertFalse($this->app['gate']->forUser($customer)->allows('view', $post));
    }

    public function test_admin_can_view_any_posts()
    {
        $admin = User::factory()->create();
        $admin->assignRole('Admin');
        
        $post = Post::factory()->create(['is_published' => false]);
        
        $this->assertTrue($this->app['gate']->forUser($admin)->allows('view', $post));
        $this->assertTrue($this->app['gate']->forUser($admin)->allows('viewAny', Post::class));
    }

    public function test_customer_can_update_own_posts()
    {
        $customer = User::factory()->create();
        $customer->assignRole('Customer');
        
        $post = Post::factory()->create(['user_id' => $customer->id]);
        
        $this->assertTrue($this->app['gate']->forUser($customer)->allows('update', $post));
    }

    public function test_customer_cannot_update_others_posts()
    {
        $customer = User::factory()->create();
        $customer->assignRole('Customer');
        
        $otherUser = User::factory()->create();
        $post = Post::factory()->create(['user_id' => $otherUser->id]);
        
        $this->assertFalse($this->app['gate']->forUser($customer)->allows('update', $post));
    }

    public function test_admin_can_update_any_posts()
    {
        $admin = User::factory()->create();
        $admin->assignRole('Admin');
        
        $post = Post::factory()->create();
        
        $this->assertTrue($this->app['gate']->forUser($admin)->allows('update', $post));
    }

    public function test_customer_can_delete_own_posts()
    {
        $customer = User::factory()->create();
        $customer->assignRole('Customer');
        
        $post = Post::factory()->create(['user_id' => $customer->id]);
        
        $this->assertTrue($this->app['gate']->forUser($customer)->allows('delete', $post));
    }

    public function test_customer_cannot_delete_others_posts()
    {
        $customer = User::factory()->create();
        $customer->assignRole('Customer');
        
        $otherUser = User::factory()->create();
        $post = Post::factory()->create(['user_id' => $otherUser->id]);
        
        $this->assertFalse($this->app['gate']->forUser($customer)->allows('delete', $post));
    }

    public function test_admin_can_delete_any_posts()
    {
        $admin = User::factory()->create();
        $admin->assignRole('Admin');
        
        $post = Post::factory()->create();
        
        $this->assertTrue($this->app['gate']->forUser($admin)->allows('delete', $post));
    }
}
