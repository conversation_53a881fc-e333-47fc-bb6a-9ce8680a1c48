<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Laravel\Sanctum\PersonalAccessToken;

class ListSystemTokens extends Command
{
    protected $signature = 'system:list-tokens 
                            {--user= : Email of specific system user}
                            {--active-only : Show only active tokens}
                            {--expired : Show only expired tokens}
                            {--format=table : Output format (table, json)}';

    protected $description = 'List API tokens for system users';

    public function handle()
    {
        $userEmail = $this->option('user');
        $activeOnly = $this->option('active-only');
        $expiredOnly = $this->option('expired');
        $format = $this->option('format');

        // Build query
        $query = PersonalAccessToken::with('tokenable')
            ->whereHasMorph('tokenable', [User::class], function ($q) {
                $q->where('is_system_user', true);
            });

        if ($userEmail) {
            $user = User::where('email', $userEmail)->where('is_system_user', true)->first();
            if (!$user) {
                $this->error("System user with email '{$userEmail}' not found.");
                return self::FAILURE;
            }
            $query->where('tokenable_id', $user->id);
        }

        if ($activeOnly) {
            $query->where('is_active', true)
                  ->where(function ($q) {
                      $q->whereNull('expires_at')
                        ->orWhere('expires_at', '>', now());
                  });
        }

        if ($expiredOnly) {
            $query->where(function ($q) {
                $q->where('is_active', false)
                  ->orWhere('expires_at', '<=', now());
            });
        }

        $tokens = $query->orderBy('created_at', 'desc')->get();

        if ($tokens->isEmpty()) {
            $this->info('No tokens found.');
            return self::SUCCESS;
        }

        if ($format === 'json') {
            $this->line(json_encode($tokens->map(function ($token) {
                return [
                    'id' => $token->id,
                    'name' => $token->name,
                    'user_email' => $token->tokenable->email,
                    'user_name' => $token->tokenable->name,
                    'system_type' => $token->tokenable->system_type,
                    'token_type' => $token->token_type,
                    'is_active' => $token->is_active,
                    'expires_at' => $token->expires_at?->toISOString(),
                    'last_used_at' => $token->last_used_at?->toISOString(),
                    'created_at' => $token->created_at->toISOString(),
                    'usage_count' => $token->usage_count ?? 0,
                ];
            }), JSON_PRETTY_PRINT));
            return self::SUCCESS;
        }

        // Table format
        $headers = [
            'ID',
            'Token Name',
            'User',
            'System Type',
            'Type',
            'Status',
            'Last Used',
            'Expires',
            'Usage Count',
            'Created'
        ];

        $rows = $tokens->map(function ($token) {
            $status = $token->is_active ? 'Active' : 'Inactive';
            if ($token->expires_at && $token->expires_at <= now()) {
                $status = 'Expired';
            }

            return [
                $token->id,
                $token->name,
                $token->tokenable->email,
                $token->tokenable->system_type ?? 'N/A',
                $token->token_type ?? 'api',
                $status,
                $token->last_used_at ? $token->last_used_at->diffForHumans() : 'Never',
                $token->expires_at ? $token->expires_at->format('Y-m-d H:i') : 'Never',
                $token->usage_count ?? 0,
                $token->created_at->format('Y-m-d H:i'),
            ];
        });

        $this->table($headers, $rows);

        $this->info("Total tokens: {$tokens->count()}");
        $activeCount = $tokens->where('is_active', true)->count();
        $expiredCount = $tokens->filter(function ($token) {
            return $token->expires_at && $token->expires_at <= now();
        })->count();
        
        $this->line("Active: {$activeCount}, Expired: {$expiredCount}");

        return self::SUCCESS;
    }
}
