<?php

require_once 'vendor/autoload.php';

use App\Models\User;
use App\Models\Ticket;
use App\Notifications\NewPostNotification;
use Illuminate\Support\Facades\Notification;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "Testing NewPostNotification...\n";

try {
    // Create a test ticket
    $user = User::first();
    if (!$user) {
        echo "No users found in database\n";
        exit(1);
    }

    $ticket = Ticket::create([
        'title' => 'Test Notification Ticket',
        'content' => 'This is a test ticket for notification testing',
        'user_id' => $user->id,
        'slug' => 'test-notification-ticket-' . time(),
        'is_published' => true,
        'status' => 'open',
        'priority' => 'medium',
    ]);

    echo "Created ticket: {$ticket->title}\n";

    // Get admin users
    $adminUsers = User::whereHas('roles', function ($query) {
        $query->whereIn('name', ['admin', 'moderator']);
    })->get();

    echo "Found " . $adminUsers->count() . " admin users\n";

    if ($adminUsers->isEmpty()) {
        echo "No admin users found. Creating test admin...\n";
        
        $admin = User::create([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        
        // Assign admin role if exists
        if (class_exists('Spatie\Permission\Models\Role')) {
            $adminRole = \Spatie\Permission\Models\Role::firstOrCreate(['name' => 'admin']);
            $admin->assignRole($adminRole);
        }
        
        $adminUsers = collect([$admin]);
    }

    // Send notification to each admin
    foreach ($adminUsers as $admin) {
        echo "Sending notification to: {$admin->name} ({$admin->email})\n";
        
        $notification = new NewPostNotification($ticket);
        $admin->notify($notification);
        
        echo "Notification sent!\n";
        
        // Check if notification was saved to database
        $dbNotification = $admin->notifications()->latest()->first();
        if ($dbNotification) {
            echo "Notification saved to database with ID: {$dbNotification->id}\n";
            echo "Notification data: " . json_encode($dbNotification->data, JSON_PRETTY_PRINT) . "\n";
        } else {
            echo "ERROR: Notification not found in database\n";
        }
    }

    // Clean up
    $ticket->delete();
    echo "Test completed successfully!\n";

} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}
