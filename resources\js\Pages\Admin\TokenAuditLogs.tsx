import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { route } from 'ziggy-js';
import { Button } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Badge } from '@/Components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/Components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/Components/ui/table';
import { Filter, Refresh<PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, Plus, Shield } from 'lucide-react';

interface TokenAuditLog {
    id: number;
    token_id: string;
    token_name: string;
    tokenable: {
        id: number;
        name: string;
        email: string;
        system_type: string;
    };
    performed_by: {
        id: number;
        name: string;
        email: string;
    } | null;
    action: 'created' | 'updated' | 'revoked' | 'rotated' | 'expired' | 'activated' | 'deactivated';
    reason?: string;
    old_values?: Record<string, any>;
    new_values?: Record<string, any>;
    metadata?: Record<string, any>;
    ip_address?: string;
    user_agent?: string;
    created_at: string;
}

interface Props {
    logs: {
        data: TokenAuditLog[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    filters: {
        tokenable_id?: string;
        action?: string;
        performed_by?: string;
        token_name?: string;
        date_from?: string;
        date_to?: string;
    };
}

export default function TokenAuditLogs({ logs, filters }: Props) {
    const [showFilters, setShowFilters] = useState(false);
    const [selectedLog, setSelectedLog] = useState<TokenAuditLog | null>(null);

    const getActionBadge = (action: string) => {
        const actionColors = {
            created: 'bg-green-100 text-green-800',
            updated: 'bg-blue-100 text-blue-800',
            revoked: 'bg-red-100 text-red-800',
            rotated: 'bg-purple-100 text-purple-800',
            expired: 'bg-gray-100 text-gray-800',
            activated: 'bg-green-100 text-green-800',
            deactivated: 'bg-yellow-100 text-yellow-800',
        };

        const actionIcons = {
            created: Plus,
            updated: Shield,
            revoked: Trash2,
            rotated: RotateCcw,
            expired: Shield,
            activated: Shield,
            deactivated: Shield,
        };

        const Icon = actionIcons[action as keyof typeof actionIcons] || Shield;

        return (
            <Badge className={actionColors[action as keyof typeof actionColors] || 'bg-gray-100 text-gray-800'}>
                <Icon className="w-3 h-3 mr-1" />
                {action.charAt(0).toUpperCase() + action.slice(1)}
            </Badge>
        );
    };

    const applyFilters = (newFilters: Partial<typeof filters>) => {
        router.get(route('admin.audit.token-logs'), 
            { ...filters, ...newFilters },
            { preserveState: true, replace: true }
        );
    };

    const formatJsonValue = (value: any) => {
        if (typeof value === 'object' && value !== null) {
            return JSON.stringify(value, null, 2);
        }
        return String(value);
    };

    return (
        <AuthenticatedLayout
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                        Token Audit Logs
                    </h2>
                    <div className="flex gap-2">
                        <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
                            <Filter className="w-4 h-4 mr-2" />
                            Filters
                        </Button>
                        <Button variant="outline" onClick={() => router.reload()}>
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Refresh
                        </Button>
                    </div>
                </div>
            }
        >
            <Head title="Token Audit Logs" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    {/* Filters */}
                    {showFilters && (
                        <Card className="mb-6">
                            <CardHeader>
                                <CardTitle>Filters</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <Label htmlFor="action">Action</Label>
                                        <Select value={filters.action || ''} onValueChange={(value) => applyFilters({ action: value || undefined })}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="All actions" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="">All actions</SelectItem>
                                                <SelectItem value="created">Created</SelectItem>
                                                <SelectItem value="updated">Updated</SelectItem>
                                                <SelectItem value="revoked">Revoked</SelectItem>
                                                <SelectItem value="rotated">Rotated</SelectItem>
                                                <SelectItem value="expired">Expired</SelectItem>
                                                <SelectItem value="activated">Activated</SelectItem>
                                                <SelectItem value="deactivated">Deactivated</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div>
                                        <Label htmlFor="token_name">Token Name</Label>
                                        <Input
                                            id="token_name"
                                            placeholder="Filter by token name..."
                                            value={filters.token_name || ''}
                                            onChange={(e) => applyFilters({ token_name: e.target.value || undefined })}
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="date_from">Date From</Label>
                                        <Input
                                            id="date_from"
                                            type="datetime-local"
                                            value={filters.date_from || ''}
                                            onChange={(e) => applyFilters({ date_from: e.target.value || undefined })}
                                        />
                                    </div>

                                    <div>
                                        <Label htmlFor="date_to">Date To</Label>
                                        <Input
                                            id="date_to"
                                            type="datetime-local"
                                            value={filters.date_to || ''}
                                            onChange={(e) => applyFilters({ date_to: e.target.value || undefined })}
                                        />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    )}

                    {/* Logs Table */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Token Operations ({logs.total.toLocaleString()})</CardTitle>
                            <CardDescription>
                                Audit trail of all token management operations.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Time</TableHead>
                                        <TableHead>Token</TableHead>
                                        <TableHead>Owner</TableHead>
                                        <TableHead>Action</TableHead>
                                        <TableHead>Performed By</TableHead>
                                        <TableHead>Reason</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {logs.data.map((log) => (
                                        <TableRow key={log.id}>
                                            <TableCell>
                                                <div className="text-sm">
                                                    {new Date(log.created_at).toLocaleString()}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div>
                                                    <div className="font-medium text-sm">{log.token_name}</div>
                                                    <div className="text-xs text-gray-500 font-mono">ID: {log.token_id}</div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div>
                                                    <div className="font-medium text-sm">{log.tokenable.name}</div>
                                                    <div className="text-xs text-gray-500">{log.tokenable.system_type}</div>
                                                </div>
                                            </TableCell>
                                            <TableCell>{getActionBadge(log.action)}</TableCell>
                                            <TableCell>
                                                {log.performed_by ? (
                                                    <div>
                                                        <div className="font-medium text-sm">{log.performed_by.name}</div>
                                                        <div className="text-xs text-gray-500">{log.performed_by.email}</div>
                                                    </div>
                                                ) : (
                                                    <span className="text-gray-400">System</span>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                {log.reason ? (
                                                    <div className="text-sm max-w-xs truncate" title={log.reason}>
                                                        {log.reason}
                                                    </div>
                                                ) : (
                                                    <span className="text-gray-400">-</span>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => setSelectedLog(log)}
                                                >
                                                    <Eye className="w-4 h-4" />
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>

                            {/* Pagination */}
                            <div className="flex items-center justify-between space-x-2 py-4">
                                <div className="text-sm text-muted-foreground">
                                    Showing {((logs.current_page - 1) * logs.per_page) + 1} to {Math.min(logs.current_page * logs.per_page, logs.total)} of {logs.total} results
                                </div>
                                <div className="flex space-x-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => applyFilters({ page: (logs.current_page - 1).toString() })}
                                        disabled={logs.current_page <= 1}
                                    >
                                        Previous
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => applyFilters({ page: (logs.current_page + 1).toString() })}
                                        disabled={logs.current_page >= logs.last_page}
                                    >
                                        Next
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>

            {/* Log Detail Modal */}
            <Dialog open={!!selectedLog} onOpenChange={() => setSelectedLog(null)}>
                <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Token Operation Details</DialogTitle>
                        <DialogDescription>
                            {selectedLog?.action} operation on token "{selectedLog?.token_name}"
                        </DialogDescription>
                    </DialogHeader>
                    {selectedLog && (
                        <div className="space-y-6">
                            {/* Basic Information */}
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label>Token Name</Label>
                                    <p className="font-medium">{selectedLog.token_name}</p>
                                </div>
                                <div>
                                    <Label>Token ID</Label>
                                    <p className="font-mono text-sm">{selectedLog.token_id}</p>
                                </div>
                                <div>
                                    <Label>Action</Label>
                                    <div>{getActionBadge(selectedLog.action)}</div>
                                </div>
                                <div>
                                    <Label>Timestamp</Label>
                                    <p className="text-sm">{new Date(selectedLog.created_at).toLocaleString()}</p>
                                </div>
                                <div>
                                    <Label>Token Owner</Label>
                                    <p className="text-sm">
                                        {selectedLog.tokenable.name} ({selectedLog.tokenable.system_type})
                                    </p>
                                </div>
                                <div>
                                    <Label>Performed By</Label>
                                    <p className="text-sm">
                                        {selectedLog.performed_by ? 
                                            `${selectedLog.performed_by.name} (${selectedLog.performed_by.email})` : 
                                            'System'
                                        }
                                    </p>
                                </div>
                            </div>

                            {/* Reason */}
                            {selectedLog.reason && (
                                <div>
                                    <Label>Reason</Label>
                                    <p className="text-sm bg-gray-50 p-3 rounded">{selectedLog.reason}</p>
                                </div>
                            )}

                            {/* IP and User Agent */}
                            {(selectedLog.ip_address || selectedLog.user_agent) && (
                                <div className="grid grid-cols-2 gap-4">
                                    {selectedLog.ip_address && (
                                        <div>
                                            <Label>IP Address</Label>
                                            <p className="font-mono text-sm">{selectedLog.ip_address}</p>
                                        </div>
                                    )}
                                    {selectedLog.user_agent && (
                                        <div>
                                            <Label>User Agent</Label>
                                            <p className="text-sm truncate" title={selectedLog.user_agent}>
                                                {selectedLog.user_agent}
                                            </p>
                                        </div>
                                    )}
                                </div>
                            )}

                            {/* Old Values */}
                            {selectedLog.old_values && Object.keys(selectedLog.old_values).length > 0 && (
                                <div>
                                    <Label>Previous Values</Label>
                                    <pre className="text-xs bg-gray-50 p-3 rounded overflow-x-auto">
                                        {formatJsonValue(selectedLog.old_values)}
                                    </pre>
                                </div>
                            )}

                            {/* New Values */}
                            {selectedLog.new_values && Object.keys(selectedLog.new_values).length > 0 && (
                                <div>
                                    <Label>New Values</Label>
                                    <pre className="text-xs bg-gray-50 p-3 rounded overflow-x-auto">
                                        {formatJsonValue(selectedLog.new_values)}
                                    </pre>
                                </div>
                            )}

                            {/* Metadata */}
                            {selectedLog.metadata && Object.keys(selectedLog.metadata).length > 0 && (
                                <div>
                                    <Label>Metadata</Label>
                                    <pre className="text-xs bg-gray-50 p-3 rounded overflow-x-auto">
                                        {formatJsonValue(selectedLog.metadata)}
                                    </pre>
                                </div>
                            )}
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </AuthenticatedLayout>
    );
}
