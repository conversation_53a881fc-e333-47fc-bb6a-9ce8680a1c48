import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout';
import { route } from 'ziggy-js';
import { <PERSON><PERSON> } from '@/Components/ui/button';
import { Input } from '@/Components/ui/input';
import { Label } from '@/Components/ui/label';
import { Badge } from '@/Components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/Components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/Components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/Components/ui/table';
import { Ta<PERSON>, TabsContent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/Components/ui/tabs';
import { Download, Eye, Filter, RefreshCw, AlertTriangle, CheckCircle, Clock, TrendingUp } from 'lucide-react';
import { toast } from 'sonner';

interface ApiLog {
    id: number;
    request_id: string;
    user: {
        id: number;
        name: string;
        email: string;
        system_type: string;
    } | null;
    method: string;
    endpoint: string;
    response_status: number;
    response_time_ms: number;
    ip_address: string;
    requested_at: string;
    error_type?: string;
    error_message?: string;
    token_name?: string;
}

interface ApiStats {
    total_requests: number;
    successful_requests: number;
    failed_requests: number;
    success_rate: number;
    status_codes: Record<string, number>;
    top_endpoints: Record<string, number>;
    avg_response_time_ms: number;
    hourly_stats: Record<string, number>;
}

interface Props {
    logs: {
        data: ApiLog[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    stats: ApiStats;
    filters: {
        user_id?: string;
        method?: string;
        endpoint?: string;
        status_code?: string;
        ip_address?: string;
        date_from?: string;
        date_to?: string;
        error_type?: string;
        min_response_time?: string;
        max_response_time?: string;
    };
}

export default function ApiAuditLogs({ logs, stats, filters }: Props) {
    const [showFilters, setShowFilters] = useState(false);
    const [selectedLog, setSelectedLog] = useState<ApiLog | null>(null);

    const handleExport = () => {
        const params = new URLSearchParams(filters as Record<string, string>);
        window.open(route('admin.audit.api-logs.export') + '?' + params.toString());
        toast.success('Export started');
    };

    const getStatusBadge = (status: number) => {
        if (status >= 200 && status < 300) {
            return <Badge className="bg-green-100 text-green-800">Success</Badge>;
        } else if (status >= 400 && status < 500) {
            return <Badge className="bg-yellow-100 text-yellow-800">Client Error</Badge>;
        } else if (status >= 500) {
            return <Badge className="bg-red-100 text-red-800">Server Error</Badge>;
        }
        return <Badge variant="secondary">{status}</Badge>;
    };

    const getResponseTimeColor = (time: number) => {
        if (time < 100) return 'text-green-600';
        if (time < 500) return 'text-yellow-600';
        return 'text-red-600';
    };

    const applyFilters = (newFilters: Partial<typeof filters>) => {
        router.get(route('admin.audit.api-logs'), 
            { ...filters, ...newFilters },
            { preserveState: true, replace: true }
        );
    };

    return (
        <AuthenticatedLayout
            header={
                <div className="flex justify-between items-center">
                    <h2 className="font-semibold text-xl text-gray-800 leading-tight">
                        API Audit Logs
                    </h2>
                    <div className="flex gap-2">
                        <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
                            <Filter className="w-4 h-4 mr-2" />
                            Filters
                        </Button>
                        <Button variant="outline" onClick={handleExport}>
                            <Download className="w-4 h-4 mr-2" />
                            Export CSV
                        </Button>
                        <Button variant="outline" onClick={() => router.reload()}>
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Refresh
                        </Button>
                    </div>
                </div>
            }
        >
            <Head title="API Audit Logs" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <Tabs defaultValue="logs" className="space-y-6">
                        <TabsList>
                            <TabsTrigger value="logs">Audit Logs</TabsTrigger>
                            <TabsTrigger value="stats">Statistics</TabsTrigger>
                        </TabsList>

                        <TabsContent value="stats" className="space-y-6">
                            {/* Statistics Overview */}
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <Card>
                                    <CardContent className="pt-6">
                                        <div className="flex items-center">
                                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                                            <div className="ml-2">
                                                <p className="text-sm font-medium leading-none">Total Requests</p>
                                                <p className="text-2xl font-bold">{stats.total_requests.toLocaleString()}</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                                <Card>
                                    <CardContent className="pt-6">
                                        <div className="flex items-center">
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            <div className="ml-2">
                                                <p className="text-sm font-medium leading-none">Success Rate</p>
                                                <p className="text-2xl font-bold text-green-600">{stats.success_rate}%</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                                <Card>
                                    <CardContent className="pt-6">
                                        <div className="flex items-center">
                                            <AlertTriangle className="h-4 w-4 text-red-600" />
                                            <div className="ml-2">
                                                <p className="text-sm font-medium leading-none">Failed Requests</p>
                                                <p className="text-2xl font-bold text-red-600">{stats.failed_requests.toLocaleString()}</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                                <Card>
                                    <CardContent className="pt-6">
                                        <div className="flex items-center">
                                            <Clock className="h-4 w-4 text-blue-600" />
                                            <div className="ml-2">
                                                <p className="text-sm font-medium leading-none">Avg Response Time</p>
                                                <p className="text-2xl font-bold text-blue-600">{stats.avg_response_time_ms.toFixed(0)}ms</p>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                            {/* Top Endpoints and Status Codes */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Top Endpoints</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-2">
                                            {Object.entries(stats.top_endpoints).slice(0, 10).map(([endpoint, count]) => (
                                                <div key={endpoint} className="flex justify-between items-center">
                                                    <span className="text-sm font-mono">{endpoint}</span>
                                                    <Badge variant="secondary">{count}</Badge>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle>Status Codes</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="space-y-2">
                                            {Object.entries(stats.status_codes).map(([status, count]) => (
                                                <div key={status} className="flex justify-between items-center">
                                                    <span className="text-sm">{status}</span>
                                                    <Badge variant="secondary">{count}</Badge>
                                                </div>
                                            ))}
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </TabsContent>

                        <TabsContent value="logs" className="space-y-6">
                            {/* Filters */}
                            {showFilters && (
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Filters</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <Label htmlFor="method">Method</Label>
                                                <Select value={filters.method || ''} onValueChange={(value) => applyFilters({ method: value || undefined })}>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="All methods" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="">All methods</SelectItem>
                                                        <SelectItem value="GET">GET</SelectItem>
                                                        <SelectItem value="POST">POST</SelectItem>
                                                        <SelectItem value="PUT">PUT</SelectItem>
                                                        <SelectItem value="DELETE">DELETE</SelectItem>
                                                        <SelectItem value="PATCH">PATCH</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div>
                                                <Label htmlFor="status_code">Status Code</Label>
                                                <Select value={filters.status_code || ''} onValueChange={(value) => applyFilters({ status_code: value || undefined })}>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="All status codes" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="">All status codes</SelectItem>
                                                        <SelectItem value="200">200 - OK</SelectItem>
                                                        <SelectItem value="201">201 - Created</SelectItem>
                                                        <SelectItem value="400">400 - Bad Request</SelectItem>
                                                        <SelectItem value="401">401 - Unauthorized</SelectItem>
                                                        <SelectItem value="403">403 - Forbidden</SelectItem>
                                                        <SelectItem value="404">404 - Not Found</SelectItem>
                                                        <SelectItem value="422">422 - Validation Error</SelectItem>
                                                        <SelectItem value="429">429 - Rate Limited</SelectItem>
                                                        <SelectItem value="500">500 - Server Error</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div>
                                                <Label htmlFor="endpoint">Endpoint</Label>
                                                <Input
                                                    id="endpoint"
                                                    placeholder="Filter by endpoint..."
                                                    value={filters.endpoint || ''}
                                                    onChange={(e) => applyFilters({ endpoint: e.target.value || undefined })}
                                                />
                                            </div>

                                            <div>
                                                <Label htmlFor="ip_address">IP Address</Label>
                                                <Input
                                                    id="ip_address"
                                                    placeholder="Filter by IP..."
                                                    value={filters.ip_address || ''}
                                                    onChange={(e) => applyFilters({ ip_address: e.target.value || undefined })}
                                                />
                                            </div>

                                            <div>
                                                <Label htmlFor="date_from">Date From</Label>
                                                <Input
                                                    id="date_from"
                                                    type="datetime-local"
                                                    value={filters.date_from || ''}
                                                    onChange={(e) => applyFilters({ date_from: e.target.value || undefined })}
                                                />
                                            </div>

                                            <div>
                                                <Label htmlFor="date_to">Date To</Label>
                                                <Input
                                                    id="date_to"
                                                    type="datetime-local"
                                                    value={filters.date_to || ''}
                                                    onChange={(e) => applyFilters({ date_to: e.target.value || undefined })}
                                                />
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            )}

                            {/* Logs Table */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>API Requests ({logs.total.toLocaleString()})</CardTitle>
                                    <CardDescription>
                                        Detailed log of all API requests and responses.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Time</TableHead>
                                                <TableHead>User</TableHead>
                                                <TableHead>Method</TableHead>
                                                <TableHead>Endpoint</TableHead>
                                                <TableHead>Status</TableHead>
                                                <TableHead>Response Time</TableHead>
                                                <TableHead>IP Address</TableHead>
                                                <TableHead>Actions</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {logs.data.map((log) => (
                                                <TableRow key={log.id}>
                                                    <TableCell>
                                                        <div className="text-sm">
                                                            {new Date(log.requested_at).toLocaleString()}
                                                        </div>
                                                    </TableCell>
                                                    <TableCell>
                                                        {log.user ? (
                                                            <div>
                                                                <div className="font-medium text-sm">{log.user.name}</div>
                                                                <div className="text-xs text-gray-500">{log.user.system_type}</div>
                                                            </div>
                                                        ) : (
                                                            <span className="text-gray-400">Anonymous</span>
                                                        )}
                                                    </TableCell>
                                                    <TableCell>
                                                        <Badge variant="outline">{log.method}</Badge>
                                                    </TableCell>
                                                    <TableCell>
                                                        <code className="text-sm">{log.endpoint}</code>
                                                    </TableCell>
                                                    <TableCell>{getStatusBadge(log.response_status)}</TableCell>
                                                    <TableCell>
                                                        <span className={`text-sm font-medium ${getResponseTimeColor(log.response_time_ms)}`}>
                                                            {log.response_time_ms.toFixed(0)}ms
                                                        </span>
                                                    </TableCell>
                                                    <TableCell>
                                                        <code className="text-sm">{log.ip_address}</code>
                                                    </TableCell>
                                                    <TableCell>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => setSelectedLog(log)}
                                                        >
                                                            <Eye className="w-4 h-4" />
                                                        </Button>
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>

                                    {/* Pagination */}
                                    <div className="flex items-center justify-between space-x-2 py-4">
                                        <div className="text-sm text-muted-foreground">
                                            Showing {((logs.current_page - 1) * logs.per_page) + 1} to {Math.min(logs.current_page * logs.per_page, logs.total)} of {logs.total} results
                                        </div>
                                        <div className="flex space-x-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => applyFilters({ page: (logs.current_page - 1).toString() })}
                                                disabled={logs.current_page <= 1}
                                            >
                                                Previous
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => applyFilters({ page: (logs.current_page + 1).toString() })}
                                                disabled={logs.current_page >= logs.last_page}
                                            >
                                                Next
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    </Tabs>
                </div>
            </div>

            {/* Log Detail Modal */}
            <Dialog open={!!selectedLog} onOpenChange={() => setSelectedLog(null)}>
                <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle>Request Details</DialogTitle>
                        <DialogDescription>
                            {selectedLog?.request_id}
                        </DialogDescription>
                    </DialogHeader>
                    {selectedLog && (
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <Label>Method & Endpoint</Label>
                                    <p className="font-mono text-sm">{selectedLog.method} {selectedLog.endpoint}</p>
                                </div>
                                <div>
                                    <Label>Status Code</Label>
                                    <p>{getStatusBadge(selectedLog.response_status)}</p>
                                </div>
                                <div>
                                    <Label>Response Time</Label>
                                    <p className={getResponseTimeColor(selectedLog.response_time_ms)}>
                                        {selectedLog.response_time_ms.toFixed(2)}ms
                                    </p>
                                </div>
                                <div>
                                    <Label>IP Address</Label>
                                    <p className="font-mono text-sm">{selectedLog.ip_address}</p>
                                </div>
                            </div>
                            
                            {selectedLog.error_message && (
                                <div>
                                    <Label>Error Message</Label>
                                    <p className="text-red-600 text-sm">{selectedLog.error_message}</p>
                                </div>
                            )}
                        </div>
                    )}
                </DialogContent>
            </Dialog>
        </AuthenticatedLayout>
    );
}
