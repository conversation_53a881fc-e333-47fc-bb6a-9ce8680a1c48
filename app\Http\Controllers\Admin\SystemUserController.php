<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class SystemUserController extends Controller
{
    /**
     * Display system users
     */
    use AuthorizesRequests;
    public function index(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $perPage = $request->input('per_page', 20);
        $search = $request->input('search');

        $systemUsers = User::systemUsers()
            ->withCount(['tokens', 'createdTickets'])
            ->when($search, function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('system_type', 'like', "%{$search}%");
            })
            ->orderBy('name')
            ->paginate($perPage);

        return Inertia::render('Admin/SystemUsers', [
            'systemUsers' => $systemUsers,
            'filters' => [
                'search' => $search,
                'per_page' => $perPage,
            ]
        ]);
    }

    /**
     * Create a new system user
     */
    public function store(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'system_type' => 'required|string|max:100',
            'system_description' => 'nullable|string|max:500',
            'rate_limit_tier' => 'required|in:standard,premium,enterprise',
            'rate_limit_per_minute' => 'nullable|integer|min:1|max:1000',
            'rate_limit_burst' => 'nullable|integer|min:1|max:100',
            'allowed_ips' => 'nullable|array',
            'allowed_ips.*' => 'ip',
        ]);

        try {
            // Set default rate limits based on tier
            $rateLimits = $this->getRateLimitDefaults($validated['rate_limit_tier']);

            $user = User::create([
                'name' => $validated['name'],
                'email' => $validated['email'],
                'password' => Hash::make(Str::random(40)), // Random password, not used for login
                'is_system_user' => true,
                'system_type' => $validated['system_type'],
                'system_description' => $validated['system_description'] ?? null,
                'rate_limit_tier' => $validated['rate_limit_tier'],
                'rate_limit_per_minute' => $validated['rate_limit_per_minute'] ?? $rateLimits['per_minute'],
                'rate_limit_burst' => $validated['rate_limit_burst'] ?? $rateLimits['burst'],
                'allowed_ips' => $validated['allowed_ips'] ?? null,
                'is_active' => true,
            ]);

            return back()->with('success', 'System user created successfully');

        } catch (\Exception $e) {
            \Log::error('Failed to create system user', [
                'error' => $e->getMessage(),
                'data' => $validated
            ]);
            return back()->withErrors(['error' => 'Failed to create system user: ' . $e->getMessage()]);
        }
    }

    /**
     * Update system user
     */
    public function update(Request $request, User $user)
    {
        $this->authorize('access-admin-dashboard');

        if (!$user->is_system_user) {
            return back()->withErrors(['error' => 'User is not a system user']);
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'system_type' => 'required|string|max:100',
            'system_description' => 'nullable|string|max:500',
            'rate_limit_tier' => 'required|in:standard,premium,enterprise',
            'rate_limit_per_minute' => 'nullable|integer|min:1|max:1000',
            'rate_limit_burst' => 'nullable|integer|min:1|max:100',
            'allowed_ips' => 'nullable|array',
            'allowed_ips.*' => 'ip',
            'is_active' => 'boolean',
        ]);

        try {
            $user->update($request->only([
                'name', 'email', 'system_type', 'system_description',
                'rate_limit_tier', 'rate_limit_per_minute', 'rate_limit_burst',
                'allowed_ips', 'is_active'
            ]));

            return back()->with('success', 'System user updated successfully');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update system user: ' . $e->getMessage()]);
        }
    }

    /**
     * Delete system user
     */
    public function destroy(User $user)
    {
        $this->authorize('access-admin-dashboard');

        if (!$user->is_system_user) {
            return back()->withErrors(['error' => 'User is not a system user']);
        }

        try {
            // Revoke all tokens first
            $user->tokens()->delete();
            
            // Delete the user
            $user->delete();

            return back()->with('success', 'System user deleted successfully');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to delete system user: ' . $e->getMessage()]);
        }
    }

    /**
     * Toggle system user active status
     */
    public function toggleStatus(User $user)
    {
        $this->authorize('access-admin-dashboard');

        if (!$user->is_system_user) {
            return back()->withErrors(['error' => 'User is not a system user']);
        }

        try {
            $user->update(['is_active' => !$user->is_active]);
            
            $status = $user->is_active ? 'activated' : 'deactivated';
            return back()->with('success', "System user {$status} successfully");

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to toggle user status: ' . $e->getMessage()]);
        }
    }

    /**
     * Get system user details
     */
    public function show(User $user)
    {
        $this->authorize('access-admin-dashboard');

        if (!$user->is_system_user) {
            return response()->json(['error' => 'User is not a system user'], 404);
        }

        $user->load(['tokens', 'createdTickets' => function ($query) {
            $query->latest()->limit(10);
        }]);

        return response()->json([
            'user' => $user,
            'stats' => [
                'total_tokens' => $user->tokens->count(),
                'active_tokens' => $user->tokens->where('is_active', true)->count(),
                'total_tickets' => $user->createdTickets->count(),
                'last_api_call' => $user->last_api_call_at,
            ]
        ]);
    }

    /**
     * Get system user statistics
     */
    public function getStats()
    {
        $this->authorize('access-admin-dashboard');

        $totalSystemUsers = User::systemUsers()->count();
        $activeSystemUsers = User::activeSystemUsers()->count();
        $inactiveSystemUsers = $totalSystemUsers - $activeSystemUsers;

        $usersByTier = User::systemUsers()
            ->selectRaw('rate_limit_tier, COUNT(*) as count')
            ->groupBy('rate_limit_tier')
            ->get()
            ->pluck('count', 'rate_limit_tier')
            ->toArray();

        $usersByType = User::systemUsers()
            ->selectRaw('system_type, COUNT(*) as count')
            ->groupBy('system_type')
            ->orderBy('count', 'desc')
            ->get()
            ->pluck('count', 'system_type')
            ->toArray();

        return response()->json([
            'total_system_users' => $totalSystemUsers,
            'active_system_users' => $activeSystemUsers,
            'inactive_system_users' => $inactiveSystemUsers,
            'users_by_tier' => $usersByTier,
            'users_by_type' => $usersByType,
        ]);
    }

    /**
     * Get default rate limits for tier
     */
    private function getRateLimitDefaults(string $tier): array
    {
        return match($tier) {
            'standard' => ['per_minute' => 60, 'burst' => 10],
            'premium' => ['per_minute' => 120, 'burst' => 20],
            'enterprise' => ['per_minute' => 300, 'burst' => 50],
            default => ['per_minute' => 60, 'burst' => 10],
        };
    }
}
