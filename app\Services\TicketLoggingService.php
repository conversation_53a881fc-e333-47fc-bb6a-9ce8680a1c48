<?php

namespace App\Services;

use App\Models\Ticket;
use App\Models\User;
use App\Models\AutomationRule;
use Illuminate\Support\Facades\Log;

class TicketLoggingService
{
    /**
     * Log ticket creation attempt
     */
    public function logTicketCreationAttempt(array $ticketData, bool $fromApi = false): void
    {
        Log::info('Ticket creation attempt started', [
            'title' => $ticketData['title'] ?? 'Unknown',
            'user_id' => $ticketData['user_id'] ?? null,
            'source' => $fromApi ? 'API' : 'Web Interface',
            'source_system' => $ticketData['source_system'] ?? null,
            'external_id' => $ticketData['external_id'] ?? null,
            'priority' => $ticketData['priority'] ?? 'medium',
            'department_id' => $ticketData['department_id'] ?? null,
        ]);
    }

    /**
     * Log successful ticket creation
     */
    public function logTicketCreationSuccess(Ticket $ticket, bool $fromApi = false): void
    {
        Log::info('Ticket created successfully', [
            'ticket_id' => $ticket->id,
            'title' => $ticket->title,
            'slug' => $ticket->slug,
            'user_id' => $ticket->user_id,
            'source' => $fromApi ? 'API' : 'Web Interface',
            'source_system' => $ticket->source_system,
            'external_id' => $ticket->external_id,
            'priority' => $ticket->priority,
            'department_id' => $ticket->department_id,
            'assignee_id' => $ticket->assignee_id,
            'automation_applied' => count($ticket->automation_applied ?? []),
        ]);
    }

    /**
     * Log ticket creation failure
     */
    public function logTicketCreationFailure(array $ticketData, string $error, bool $fromApi = false): void
    {
        Log::error('Ticket creation failed', [
            'title' => $ticketData['title'] ?? 'Unknown',
            'user_id' => $ticketData['user_id'] ?? null,
            'source' => $fromApi ? 'API' : 'Web Interface',
            'source_system' => $ticketData['source_system'] ?? null,
            'external_id' => $ticketData['external_id'] ?? null,
            'error' => $error,
            'ticket_data' => $ticketData,
        ]);
    }

    /**
     * Log automation rule application
     */
    public function logRuleApplication(Ticket $ticket, AutomationRule $rule, bool $matched): void
    {
        if ($matched) {
            Log::info('Automation rule applied to ticket', [
                'ticket_id' => $ticket->id,
                'rule_id' => $rule->id,
                'rule_name' => $rule->name,
                'rule_category_type' => $rule->category_type,
                'rule_priority' => $rule->assigned_priority,
                'rule_department_id' => $rule->assigned_department_id,
                'rule_user_id' => $rule->assigned_user_id,
                'execution_order' => $rule->execution_order,
            ]);
        } else {
            Log::debug('Automation rule did not match ticket', [
                'ticket_id' => $ticket->id,
                'rule_id' => $rule->id,
                'rule_name' => $rule->name,
            ]);
        }
    }

    /**
     * Log automation rule application failure
     */
    public function logRuleApplicationFailure(Ticket $ticket, AutomationRule $rule, string $error): void
    {
        Log::error('Failed to apply automation rule to ticket', [
            'ticket_id' => $ticket->id,
            'rule_id' => $rule->id,
            'rule_name' => $rule->name,
            'error' => $error,
        ]);
    }

    /**
     * Log notification dispatch attempt
     */
    public function logNotificationDispatchAttempt(Ticket $ticket, int $userCount): void
    {
        Log::info('Notification dispatch started', [
            'ticket_id' => $ticket->id,
            'ticket_title' => $ticket->title,
            'user_count' => $userCount,
            'source_system' => $ticket->source_system,
        ]);
    }

    /**
     * Log successful notification dispatch
     */
    public function logNotificationDispatchSuccess(Ticket $ticket, User $user, string $notificationType): void
    {
        Log::debug('Notification sent successfully', [
            'ticket_id' => $ticket->id,
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => $user->name,
            'notification_type' => $notificationType,
            'user_roles' => $user->roles->pluck('name')->toArray(),
            'user_departments' => $user->departments->pluck('name')->toArray(),
        ]);
    }

    /**
     * Log notification dispatch failure
     */
    public function logNotificationDispatchFailure(Ticket $ticket, User $user, string $error, string $notificationType): void
    {
        Log::error('Failed to send notification', [
            'ticket_id' => $ticket->id,
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => $user->name,
            'notification_type' => $notificationType,
            'error' => $error,
            'user_roles' => $user->roles->pluck('name')->toArray(),
            'user_departments' => $user->departments->pluck('name')->toArray(),
        ]);
    }

    /**
     * Log notification job completion
     */
    public function logNotificationJobCompletion(Ticket $ticket, int $sent, int $failed, int $total): void
    {
        Log::info('Notification job completed', [
            'ticket_id' => $ticket->id,
            'notifications_sent' => $sent,
            'notifications_failed' => $failed,
            'total_users' => $total,
            'success_rate' => $total > 0 ? round(($sent / $total) * 100, 2) : 0,
        ]);
    }

    /**
     * Log notification job failure
     */
    public function logNotificationJobFailure(Ticket $ticket, string $error, int $attempts): void
    {
        Log::error('Notification job failed', [
            'ticket_id' => $ticket->id,
            'error' => $error,
            'attempts' => $attempts,
            'max_attempts' => 3,
        ]);
    }

    /**
     * Log user notification eligibility check
     */
    public function logUserNotificationEligibility(Ticket $ticket, User $user, string $reason, bool $eligible): void
    {
        Log::debug('User notification eligibility check', [
            'ticket_id' => $ticket->id,
            'user_id' => $user->id,
            'user_email' => $user->email,
            'reason' => $reason,
            'eligible' => $eligible,
            'user_roles' => $user->roles->pluck('name')->toArray(),
            'user_departments' => $user->departments->pluck('name')->toArray(),
        ]);
    }

    /**
     * Log automation statistics
     */
    public function logAutomationStatistics(Ticket $ticket, array $appliedRules): void
    {
        Log::info('Ticket automation statistics', [
            'ticket_id' => $ticket->id,
            'total_rules_applied' => count($appliedRules),
            'applied_rules' => $appliedRules,
            'final_priority' => $ticket->priority,
            'final_department_id' => $ticket->department_id,
            'final_assignee_id' => $ticket->assignee_id,
            'final_category_type' => $ticket->category_type,
        ]);
    }
}
