<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ticket_external_refs', function (Blueprint $table) {
            $table->id();
            $table->string('ticket_id'); // Changed to string to match ULID
            $table->string('source_system');
            $table->string('external_id');
            $table->timestamps();

            // Ensure uniqueness of external_id per source_system
            $table->unique(['source_system', 'external_id']);
            $table->index('ticket_id');

            // Add foreign key constraint manually for string ULID
            $table->foreign('ticket_id')->references('id')->on('tickets')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_external_refs');
    }
};
