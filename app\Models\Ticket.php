<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUlids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;
use Laravel\Scout\Searchable;

class Ticket extends Model
{
    use HasFactory, HasUlids, Notifiable, Searchable, SoftDeletes;

    protected $fillable = [
        'title',
        'content',
        'user_id',
        'is_published',
        'slug',
        'image',
        'product_id',
        'product_name',
        'department_id',
        'status',
        'priority',
        'assignee_id',
        'category_type',
        'priority_score',
        'automation_applied',
        'auto_assigned_at',
        'auto_assigned_by_rule_id',
        'requester_name',
        'requester_email',
        'source_system',
        'external_id',
        'payload',
        'category_id',
    ];

    // <PERSON>uan hệ với user (creator - system user or regular user)
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Alias for user() relationship - represents the creator (System User)
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'ticket_tag', 'ticket_id', 'tag_id');
    }

    // Quan hệ với categories
    public function categories()
    {
        return $this->belongsToMany(Category::class, 'category_ticket');
    }

    // Quan hệ với department
    public function department()
    {
        return $this->belongsTo(Departments::class);
    }

    // Quan hệ với assignee
    public function assignee()
    {
        return $this->belongsTo(User::class, 'assignee_id');
    }

    // Quan hệ với automation rule
    public function autoAssignedByRule()
    {
        return $this->belongsTo(AutomationRule::class, 'auto_assigned_by_rule_id');
    }

    // Quan hệ với ticket tag priorities
    public function tagPriorities()
    {
        return $this->hasMany(TicketTagPriority::class);
    }

    // Quan hệ với comments
    public function comments()
    {
        return $this->hasMany(Comments::class);
    }

    // Quan hệ với upvotes
    public function upvotes()
    {
        return $this->hasMany(TicketUpvote::class);
    }

    // Đếm số lượng tickets
    public static function countTickets()
    {
        return self::count();
    }

    // Kiểm tra xem user đã upvote ticket chưa
    public function isUpvotedBy($userId)
    {
        return $this->upvotes()->where('user_id', $userId)->exists();
    }

    // Lấy đoạn trích nội dung (excerpt)
    public function getExcerpt()
    {
        return Str::limit(strip_tags($this->content), 150);
    }

    // Định dạng dữ liệu ticket cho list view
    public function toFormattedArray()
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'content' => $this->getExcerpt(),
            'slug' => $this->slug,
            'status' => $this->status ?? 'open',
            'priority' => $this->priority ?? 'medium',
            'upvote_count' => $this->upvotes_count ?? 0,
            'has_upvote' => auth()->check() ? $this->isUpvotedBy(auth()->id()) : false,
            'is_published' => $this->is_published,
            'created_at' => $this->created_at->diffForHumans(),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
            'user' => $this->user ? [
                'id' => $this->user->id,
                'name' => $this->user->name,
                'email' => $this->user->email,
                'profile_photo_path' => $this->user->profile_photo_url
            ] : null,
            'assignee' => $this->assignee ? [
                'id' => $this->assignee->id,
                'name' => $this->assignee->name,
                'email' => $this->assignee->email,
                'profile_photo_path' => $this->assignee->profile_photo_url
            ] : null,
            'department' => $this->department ? [
                'id' => $this->department->id,
                'name' => $this->department->name,
            ] : null,
            'tags' => $this->tags->map(function ($tag) {
                return [
                    'id' => $tag->id,
                    'name' => $tag->name,
                ];
            })->toArray(),
            'categories' => $this->categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'title' => $category->title,
                    'slug' => $category->slug,
                ];
            })->toArray(),
            'comments' => $this->comments,
            'comments_count' => $this->comments_count ?? 0,
            'has_upvote' => auth()->check() ? $this->isUpvotedBy(auth()->id()) : false,
        ];
    }

    // Định dạng dữ liệu ticket cho detail view (với comments đầy đủ)
    public function toDetailArray()
    {
        $baseArray = $this->toFormattedArray();
        $baseArray['comments'] = $this->getFormattedComments();
        return $baseArray;
    }

    // Phương thức lấy danh sách tickets với tìm kiếm, phân trang và sắp xếp
    public static function getTickets($search = '', $perPage = 6, $sort = 'latest', $tag = null)
    {
        $query = self::query()
            ->where('is_published', true)
            ->with(['user', 'tags', 'categories'])
            ->withCount('upvotes');

        if ($search) {
            $query->where('title', 'like', "%{$search}%")
                ->orWhere('content', 'like', "%{$search}%");
        }

        if ($tag) {
            $query->whereHas('tags', function ($q) use ($tag) {
                $q->where('tags.id', $tag);
            });
        }

        if ($sort === 'latest') {
            $query->orderByRaw("FIELD(priority, 'urgent', 'high', 'medium', 'low')")
                  ->latest();
        } elseif ($sort === 'upvotes') {
            $query->orderBy('upvotes_count', 'desc');
        } elseif ($sort === 'priority') {
            $query->orderByRaw("FIELD(priority, 'urgent', 'high', 'medium', 'low')")
                  ->latest();
        }

        return $query->paginate($perPage);
    }

    // Kiểm tra trùng lặp ticket
    public static function existsByTitleOrSlug($title, $slug)
    {
        return self::where('title', $title)->orWhere('slug', $slug)->exists();
    }

    // Lấy ticket theo slug với các thông tin liên quan
    public static function getTicketBySlug($slug)
    {
        return self::with(['user:id,name,profile_photo_path', 'categories:id,title,slug', 'tags:id,name'])
            ->withCount(['upvotes', 'comments'])
            ->where('slug', $slug)
            ->firstOrFail();
    }

    // Lấy comments với nested replies
    public function getFormattedComments()
    {
        return $this->comments()
            ->whereNull('parent_id')
            ->with([
                'user:id,name,profile_photo_path',
                'user.roles:id,name',
                'user.departments:id,name',
                'allReplies.user:id,name,profile_photo_path',
                'allReplies.user.roles:id,name',
                'allReplies.user.departments:id,name',
            ])
            ->latest()
            ->get()
            ->map(function ($comment) {
                return $this->formatComment($comment);
            });
    }

    // Định dạng comment
    protected function formatComment($comment)
    {
        return [
            'id' => (string) $comment->id,
            'user' => [
                'id' => $comment->user->id,
                'name' => $comment->user->name,
                'profile_photo_path' => $comment->user->profile_photo_url,
                'roles' => $comment->user->getRoleNames(),
                'departments' => $comment->user->departments->pluck('name'),
            ],
            'content' => $comment->comment,
            'created_at' => $comment->created_at->format('Y-m-d H:i:s'),
            'parent_id' => $comment->parent_id ? (string) $comment->parent_id : null,
            'is_hr_response' => $comment->is_hr_response ?? false,
            'replies' => $comment->allReplies->map(function ($reply) {
                return $this->formatComment($reply);
            }),
        ];
    }

    // Scope lấy ticket theo category slug
    public function scopeByCategorySlug(Builder $query, $categorySlug)
    {
        return $query->whereHas('categories', function ($q) use ($categorySlug) {
            $q->where('slug', $categorySlug);
        });
    }

    public function scopeByTagsSlug(Builder $query, $tagSlug)
    {
        return $query->whereHas('tags', function ($q) use ($tagSlug) {
            $q->where('slug', $tagSlug);
        });
    }

    // Scope tìm kiếm ticket
    public function scopeSearch(Builder $query, $keyword)
    {
        return $query->where('title', 'like', "%{$keyword}%")
            ->orWhere('content', 'like', "%{$keyword}%");
    }

    // Lấy dữ liệu cho tìm kiếm Scout
    public function toSearchableArray()
    {
        return [
            'title' => $this->title,
            'content' => $this->content,
        ];
    }

    public function receivesBroadcastNotificationsOn()
    {
        return 'users.'.$this->id;
    }

    /**
     * Cast attributes
     */
    protected $casts = [
        'automation_applied' => 'array',
        'auto_assigned_at' => 'datetime',
        'priority_score' => 'integer',
        'is_published' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'payload' => 'array',
    ];

    /**
     * Priority levels with their display names
     */
    public const PRIORITY_LEVELS = [
        'low' => 'Thấp',
        'medium' => 'Trung bình',
        'high' => 'Cao',
        'urgent' => 'Khẩn cấp',
    ];

    /**
     * Status levels with their display names
     */
    public const STATUS_LEVELS = [
        'open' => 'Mở',
        'in_progress' => 'Đang xử lý',
        'resolved' => 'Đã giải quyết',
        'closed' => 'Đã đóng',
    ];

    /**
     * Category types with their display names
     */
    public const CATEGORY_TYPES = [
        'technical' => 'Kỹ thuật',
        'payment' => 'Thanh toán',
        'consultation' => 'Tư vấn',
        'general' => 'Tổng quát',
    ];

    /**
     * Get human readable priority
     */
    public function getPriorityNameAttribute(): string
    {
        return self::PRIORITY_LEVELS[$this->priority] ?? $this->priority;
    }

    /**
     * Get human readable status
     */
    public function getStatusNameAttribute(): string
    {
        return self::STATUS_LEVELS[$this->status] ?? $this->status;
    }

    /**
     * Get human readable category type
     */
    public function getCategoryTypeNameAttribute(): string
    {
        return self::CATEGORY_TYPES[$this->category_type] ?? $this->category_type;
    }

    /**
     * Calculate priority score based on various factors
     */
    public function calculatePriorityScore(): int
    {
        $score = TicketTagPriority::getPriorityScore($this->priority);

        // Adjust based on tag priorities
        $tagPriorityBonus = $this->tagPriorities()->avg('priority_score') ?? 0;
        $score = ($score + $tagPriorityBonus) / 2;

        // Adjust based on age (older tickets get higher priority)
        $ageInHours = $this->created_at->diffInHours(now());
        if ($ageInHours > 24) {
            $score += min(20, $ageInHours / 24 * 5); // Max 20 points bonus
        }

        return min(100, max(0, (int) $score));
    }

    /**
     * Update priority score
     */
    public function updatePriorityScore(): void
    {
        $this->update(['priority_score' => $this->calculatePriorityScore()]);
    }

    /**
     * Scope for filtering by priority
     */
    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope for filtering by status
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for filtering by category type
     */
    public function scopeByCategoryType($query, string $categoryType)
    {
        return $query->where('category_type', $categoryType);
    }

    /**
     * Scope for filtering by department
     */
    public function scopeByDepartment($query, $departmentId)
    {
        return $query->where('department_id', $departmentId);
    }

    /**
     * Scope for filtering by assignee
     */
    public function scopeByAssignee($query, $userId)
    {
        return $query->where('assignee_id', $userId);
    }

    /**
     * Scope for ordering by priority score
     */
    public function scopeOrderByPriority($query, string $direction = 'desc')
    {
        return $query->orderBy('priority_score', $direction);
    }

    /**
     * Check if ticket is auto-assigned
     */
    public function isAutoAssigned(): bool
    {
        return ! is_null($this->auto_assigned_at);
    }

    /**
     * Get formatted automation history
     */
    public function getAutomationHistory(): array
    {
        return $this->automation_applied ?? [];
    }
}
