import React from 'react';
import { Button } from '@/Components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/Components/ui/select';

interface PaginationData {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
  prev_page_url: string | null;
  next_page_url: string | null;
}

interface TicketTablePaginationProps {
  pagination: PaginationData;
  isLoading: boolean;
  onPageChange: (url: string | null) => void;
  onPerPageChange: (perPage: string) => void;
}

export default function TicketTablePagination({
  pagination,
  isLoading,
  onPageChange,
  onPerPageChange,
}: TicketTablePaginationProps) {
  const {
    current_page,
    last_page,
    per_page,
    total,
    from,
    to,
    prev_page_url,
    next_page_url,
  } = pagination;

  return (
    <div className="flex items-center justify-between px-4 py-3 border-t">
      {/* Left side - Info */}
      <div className="flex items-center gap-4">
        <div className="text-sm text-muted-foreground">
          Hiển thị {from} đến {to} của {total} kết quả
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Hiển thị:</span>
          <Select value={per_page.toString()} onValueChange={onPerPageChange}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-muted-foreground">mục</span>
        </div>
      </div>

      {/* Right side - Navigation */}
      <div className="flex items-center gap-2">
        <div className="text-sm text-muted-foreground">
          Trang {current_page} của {last_page}
        </div>
        
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="icon"
            onClick={() => onPageChange(prev_page_url)}
            disabled={!prev_page_url || isLoading}
            className="h-8 w-8"
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">Previous page</span>
          </Button>
          
          <Button
            variant="outline"
            size="icon"
            onClick={() => onPageChange(next_page_url)}
            disabled={!next_page_url || isLoading}
            className="h-8 w-8"
          >
            <ChevronRight className="h-4 w-4" />
            <span className="sr-only">Next page</span>
          </Button>
        </div>
      </div>
    </div>
  );
}
