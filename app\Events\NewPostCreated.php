<?php

namespace App\Events;

use App\Models\Ticket;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NewPostCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Ticket $post;

    public function __construct(Ticket $post)
    {
        $this->post = $post;
    }

    // Event này chỉ để trigger listener, không broadcast trực tiếp
    // Listener sẽ handle việc gửi notification
}

class PostNotificationBroadcast implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public array $notificationData;

    public function __construct(array $notificationData)
    {
        $this->notificationData = $notificationData;
    }

    public function broadcastOn(): Channel
    {
        return new Channel('notifications');
    }

    public function broadcastAs(): string
    {
        return 'new-post-notification';
    }

    public function broadcastWith(): array
    {
        return $this->notificationData;
    }
}
