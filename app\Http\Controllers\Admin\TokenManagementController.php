<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\TokenManagementService;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Laravel\Sanctum\PersonalAccessToken;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class TokenManagementController extends Controller
{


    use AuthorizesRequests;
    
    public function __construct(
        private TokenManagementService $tokenService
    ) {}

    /**
     * Display token management dashboard
     */
    public function index(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $perPage = $request->input('per_page', 20);
        $search = $request->input('search');

        // Get system users with their tokens
        $systemUsers = User::systemUsers()
            ->with(['tokens' => function ($query) {
                $query->orderBy('created_at', 'desc');
            }])
            ->when($search, function ($query, $search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('system_type', 'like', "%{$search}%");
            })
            ->orderBy('name')
            ->paginate($perPage);

        // Get token statistics
        $tokenStats = $this->tokenService->getTokenStatistics();

        return Inertia::render('Admin/TokenManagement', [
            'systemUsers' => $systemUsers,
            'tokenStats' => $tokenStats,
            'filters' => [
                'search' => $search,
                'per_page' => $perPage,
            ]
        ]);
    }

    /**
     * Create a new token
     */
    public function store(Request $request)
    {
        $this->authorize('access-admin-dashboard');

        $request->validate([
            'user_id' => 'required|exists:users,id',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'expires_at' => 'nullable|date|after:now',
            'token_type' => 'required|in:api,webhook',
            'allowed_ips' => 'nullable|array',
            'allowed_ips.*' => 'ip',
        ]);

        $user = User::findOrFail($request->user_id);

        if (!$user->isActiveSystemUser()) {
            return back()->withErrors(['user_id' => 'User must be an active system user']);
        }

        try {
            $result = $this->tokenService->createToken(
                $user,
                $request->name,
                ['*'], // Default abilities
                $request->description,
                $request->expires_at,
                $request->token_type,
                $request->allowed_ips,
                auth()->id()
            );

            return back()->with([
                'success' => 'Token created successfully',
                'token' => $result['token'], // Show token once
            ]);

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to create token: ' . $e->getMessage()]);
        }
    }

    /**
     * Update token settings
     */
    public function update(Request $request, PersonalAccessToken $token)
    {
        $this->authorize('access-admin-dashboard');

        $request->validate([
            'description' => 'nullable|string|max:500',
            'expires_at' => 'nullable|date',
            'is_active' => 'boolean',
            'allowed_ips' => 'nullable|array',
            'allowed_ips.*' => 'ip',
        ]);

        try {
            $this->tokenService->updateToken(
                $token,
                $request->only(['description', 'expires_at', 'is_active', 'allowed_ips']),
                auth()->id()
            );

            return back()->with('success', 'Token updated successfully');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to update token: ' . $e->getMessage()]);
        }
    }

    /**
     * Revoke a token
     */
    public function destroy(Request $request, PersonalAccessToken $token)
    {
        $this->authorize('access-admin-dashboard');

        $request->validate([
            'reason' => 'nullable|string|max:255',
        ]);

        try {
            $this->tokenService->revokeToken(
                $token,
                $request->reason ?? 'Revoked via admin dashboard',
                auth()->id()
            );

            return back()->with('success', 'Token revoked successfully');

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to revoke token: ' . $e->getMessage()]);
        }
    }

    /**
     * Rotate a token
     */
    public function rotate(Request $request, PersonalAccessToken $token)
    {
        $this->authorize('access-admin-dashboard');

        $request->validate([
            'new_name' => 'nullable|string|max:255',
            'reason' => 'nullable|string|max:255',
        ]);

        try {
            $result = $this->tokenService->rotateToken(
                $token,
                $request->new_name,
                $request->reason ?? 'Token rotation via admin dashboard',
                auth()->id()
            );

            return back()->with([
                'success' => 'Token rotated successfully',
                'token' => $result['token'], // Show new token once
            ]);

        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'Failed to rotate token: ' . $e->getMessage()]);
        }
    }

    /**
     * Get token details
     */
    public function show(PersonalAccessToken $token)
    {
        $this->authorize('access-admin-dashboard');

        $tokenData = [
            'id' => $token->id,
            'name' => $token->name,
            'description' => $token->description,
            'token_type' => $token->token_type,
            'is_active' => $token->is_active,
            'expires_at' => $token->expires_at,
            'last_used_at' => $token->last_used_at,
            'usage_count' => $token->usage_count,
            'allowed_ips' => $token->allowed_ips,
            'created_at' => $token->created_at,
            'user' => [
                'id' => $token->tokenable->id,
                'name' => $token->tokenable->name,
                'email' => $token->tokenable->email,
                'system_type' => $token->tokenable->system_type,
            ],
        ];

        return response()->json($tokenData);
    }

    /**
     * Get system users for token creation
     */
    public function getSystemUsers()
    {
        $this->authorize('access-admin-dashboard');

        $users = User::activeSystemUsers()
            ->select(['id', 'name', 'email', 'system_type'])
            ->orderBy('name')
            ->get();

        return response()->json($users);
    }
}
