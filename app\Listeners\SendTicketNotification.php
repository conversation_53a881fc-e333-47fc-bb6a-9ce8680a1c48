<?php

namespace App\Listeners;

use App\Events\NewTicketCreated;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class SendTicketNotification
{
    /**
     * Handle the event.
     */
    public function handle(NewTicketCreated $event): void
    {
        $ticket = $event->ticket;
        
        try {
            // Get users who should receive ticket notifications
            $notifiableUsers = $this->getNotifiableUsers($ticket);
            
            if ($notifiableUsers->isEmpty()) {
                Log::warning('No users found to notify for ticket creation', [
                    'ticket_id' => $ticket->id,
                    'ticket_title' => $ticket->title,
                ]);
                return;
            }
            
            // Tạo notification data cho database
            $notificationDataForDB = [
                'ticket_id' => $ticket->id,
                'title' => $ticket->title,
                'message' => "New ticket created: {$ticket->title}",
                'slug' => $ticket->slug,
                'priority' => $ticket->priority,
                'status' => $ticket->status,
                'name' => $ticket->user->name ?? 'Unknown User',
                'user_email' => $ticket->user->email ?? '',
                'profile_photo_url' => $ticket->user->profile_photo_path
                    ? asset('storage/'.$ticket->user->profile_photo_path)
                    : null,
                'department_name' => $ticket->department->name ?? 'No Department',
                'categories' => $ticket->categories->pluck('title')->toArray(),
                'tags' => $ticket->tags->pluck('name')->toArray(),
                'type_notification' => 'ticket',
            ];

            // Tạo 1 notification ID duy nhất cho broadcast
            $broadcastNotificationId = \Illuminate\Support\Str::uuid();

            // Lưu vào database cho từng admin user
            foreach ($notifiableUsers as $user) {
                $user->notifications()->create([
                    'id' => \Illuminate\Support\Str::uuid(), // Mỗi user có notification riêng trong DB
                    'type' => 'App\\Notifications\\NewTicketNotification',
                    'data' => $notificationDataForDB,
                    'read_at' => null,
                ]);
            }

            // Broadcast 1 notification duy nhất với ID riêng (không trùng với DB)
            $broadcastNotification = [
                'id' => $broadcastNotificationId, // ID riêng cho broadcast
                'data' => $notificationDataForDB,
                'type' => 'NewTicketNotification',
                'read_at' => null,
                'created_at' => $ticket->created_at->diffForHumans(),
            ];

            event(new \App\Events\TicketNotificationBroadcast($broadcastNotification));

            Log::info('Ticket creation notifications sent', [
                'ticket_id' => $ticket->id,
                'ticket_title' => $ticket->title,
                'notified_users_count' => $notifiableUsers->count(),
                'notified_user_ids' => $notifiableUsers->pluck('id')->toArray(),
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send ticket creation notifications', [
                'ticket_id' => $ticket->id,
                'ticket_title' => $ticket->title,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
    
    /**
     * Get users who should receive ticket notifications
     */
    private function getNotifiableUsers($ticket)
    {
        // Chỉ gửi notification cho admin users để tránh duplicate
        return User::role('Admin' )->where('is_active', '!=', false)->get();
    }
}
