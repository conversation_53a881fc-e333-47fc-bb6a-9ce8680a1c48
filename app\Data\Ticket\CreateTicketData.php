<?php

namespace App\Data\Ticket;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Rule;
use Spatie\LaravelData\Data;

class CreateTicketData extends Data
{
    public function __construct(
        #[Rule('required|string|max:255')]
        public string $title,

        #[Rule('required|string')]
        public string $content,

        #[Rule('required|bool')]
        public bool $is_published,

        #[Rule('nullable|array')]
        public array $categories,

        #[Rule('nullable|array')]
        public array $tags,
    ) {}
}
