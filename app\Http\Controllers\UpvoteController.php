<?php

namespace App\Http\Controllers;

use App\Models\Ticket;
use App\Models\TicketUpvote;
use Illuminate\Support\Facades\Auth;

class UpvoteController extends Controller
{
    //
    public function upvote(string $id)
    {
        $user = Auth::user();
        $ticket = Ticket::findOrFail($id);

        // Kiểm tra nếu user đã upvote
        if ($user == null) {
            return redirect('/login');
        }

        $alreadyUpvote = TicketUpvote::where('ticket_id', $ticket->id)
            ->where('user_id', $user->id)
            ->exists();
        if ($alreadyUpvote) {
            // Nếu đã upvote, thì xóa upvote
            TicketUpvote::where('ticket_id', $ticket->id)
                ->where('user_id', $user->id)
                ->delete();

            return redirect()->back()->with('message', 'Upvote removed.');
        }

        // Nếu chưa upvote, thì thêm upvote
        TicketUpvote::create([
            'ticket_id' => $ticket->id,
            'user_id' => $user->id,
        ]);

        return redirect()->back()->with('message', 'Upvote added.');
    }
}
