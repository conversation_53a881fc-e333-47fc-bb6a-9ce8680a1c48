// Main component
export { default as AdvancedTicketTable } from './AdvancedTicketTableRefactored';

// Sub-components
export { default as TicketTableHeader } from './TicketTableHeader';
export { default as TicketTableFilters } from './TicketTableFilters';
export { default as TicketTableBulkActions } from './TicketTableBulkActions';
export { default as TicketTableRow } from './TicketTableRow';
export { default as TicketTablePagination } from './TicketTablePagination';

// Types (if needed)
export interface Ticket {
  id: number;
  title: string;
  status: string;
  priority: string;
  created_at: string;
  assignee?: {
    id: number;
    name: string;
    profile_photo_url?: string;
  };
  department?: {
    id: number;
    name: string;
  };
  comments_count: number;
}

export interface AssignableUser {
  id: number;
  name: string;
  email: string;
  profile_photo_url?: string;
}

export interface PaginationData {
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
  prev_page_url: string | null;
  next_page_url: string | null;
  data: Ticket[];
}
