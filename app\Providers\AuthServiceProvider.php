<?php

namespace App\Providers;

use App\Models\Category;
use App\Models\Comments;
use App\Models\Departments;
use App\Models\Ticket;
use App\Models\Tag;
use App\Models\User;
use App\Policies\CategoryPolicy;
use App\Policies\CommentPolicy;
use App\Policies\DepartmentPolicy;
use App\Policies\TicketPolicy;
use App\Policies\TagPolicy;
use App\Policies\UserPolicy;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    protected $policies = [
        Ticket::class => TicketPolicy::class,
        User::class => UserPolicy::class,
        Comments::class => CommentPolicy::class,
        Category::class => CategoryPolicy::class,
        Departments::class => DepartmentPolicy::class,
        Tag::class => TagPolicy::class,
    ];

    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot()
    {
        //        // Register the policies
        //        $this->registerPolicies();

        Gate::define('delete-comment', function (User $user, Comments $comment) {
            return $user->id === $comment->user_id;
        });
    }
}
