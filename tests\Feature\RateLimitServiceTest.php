<?php

namespace Tests\Feature;

use App\Models\User;
use App\Services\RateLimitService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class RateLimitServiceTest extends TestCase
{
    use RefreshDatabase;

    private RateLimitService $rateLimitService;
    private User $systemUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->rateLimitService = app(RateLimitService::class);
        
        $this->systemUser = User::factory()->create([
            'is_system_user' => true,
            'system_type' => 'payment',
            'is_active' => true,
            'rate_limit_tier' => 'standard',
            'rate_limit_per_minute' => 60,
            'rate_limit_burst' => 10,
        ]);
    }

    public function test_allows_request_for_active_system_user()
    {
        $result = $this->rateLimitService->canMakeRequest($this->systemUser);

        $this->assertTrue($result['allowed']);
        $this->assertNull($result['reason']);
    }

    public function test_denies_request_for_inactive_system_user()
    {
        $this->systemUser->update(['is_active' => false]);

        $result = $this->rateLimitService->canMakeRequest($this->systemUser);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('User account is inactive', $result['reason']);
    }

    public function test_allows_request_for_regular_user()
    {
        $regularUser = User::factory()->create(['is_system_user' => false]);

        $result = $this->rateLimitService->canMakeRequest($regularUser);

        $this->assertTrue($result['allowed']);
    }

    public function test_records_successful_request()
    {
        $this->rateLimitService->recordRequest($this->systemUser);

        $today = now()->format('Y-m-d');
        $dailyKey = "rate_limit:daily:{$this->systemUser->id}:{$today}";
        
        $this->assertEquals(1, Cache::get($dailyKey, 0));
    }

    public function test_records_violation()
    {
        $this->rateLimitService->recordViolation($this->systemUser, 'minute');

        $violationKey = "rate_limit:violations:{$this->systemUser->id}";
        $this->assertEquals(1, Cache::get($violationKey, 0));
    }

    public function test_applies_gradual_backoff_after_violations()
    {
        // Record multiple violations to trigger backoff
        for ($i = 0; $i < 6; $i++) {
            $this->rateLimitService->recordViolation($this->systemUser, 'minute');
        }

        $backoffKey = "rate_limit:backoff:{$this->systemUser->id}";
        $backoffUntil = Cache::get($backoffKey);
        
        $this->assertNotNull($backoffUntil);
        $this->assertGreaterThan(now()->timestamp, $backoffUntil);
    }

    public function test_gets_rate_limit_status()
    {
        $status = $this->rateLimitService->getRateLimitStatus($this->systemUser);

        $this->assertEquals('standard', $status['tier']);
        $this->assertArrayHasKey('limits', $status);
        $this->assertArrayHasKey('usage', $status);
        $this->assertArrayHasKey('reset_times', $status);
        
        $this->assertEquals(60, $status['limits']['per_minute']);
        $this->assertEquals(10, $status['limits']['burst']);
    }

    public function test_daily_limit_enforcement()
    {
        // Set a low daily limit for testing
        $today = now()->format('Y-m-d');
        $dailyKey = "rate_limit:daily:{$this->systemUser->id}:{$today}";
        Cache::put($dailyKey, 10000, now()->endOfDay()); // Simulate hitting daily limit

        $result = $this->rateLimitService->canMakeRequest($this->systemUser);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('Daily rate limit exceeded', $result['reason']);
        $this->assertEquals('daily', $result['limit_type']);
    }

    public function test_premium_tier_has_higher_limits()
    {
        $premiumUser = User::factory()->create([
            'is_system_user' => true,
            'is_active' => true,
            'rate_limit_tier' => 'premium',
        ]);

        $status = $this->rateLimitService->getRateLimitStatus($premiumUser);

        $this->assertEquals('premium', $status['tier']);
        $this->assertEquals(120, $status['limits']['per_minute']);
        $this->assertEquals(20, $status['limits']['burst']);
    }

    public function test_enterprise_tier_has_highest_limits()
    {
        $enterpriseUser = User::factory()->create([
            'is_system_user' => true,
            'is_active' => true,
            'rate_limit_tier' => 'enterprise',
        ]);

        $status = $this->rateLimitService->getRateLimitStatus($enterpriseUser);

        $this->assertEquals('enterprise', $status['tier']);
        $this->assertEquals(300, $status['limits']['per_minute']);
        $this->assertEquals(50, $status['limits']['burst']);
    }

    public function test_custom_rate_limits_override_tier_defaults()
    {
        $customUser = User::factory()->create([
            'is_system_user' => true,
            'is_active' => true,
            'rate_limit_tier' => 'standard',
            'rate_limit_per_minute' => 200, // Custom override
            'rate_limit_burst' => 25, // Custom override
        ]);

        $status = $this->rateLimitService->getRateLimitStatus($customUser);

        $this->assertEquals(200, $status['limits']['per_minute']);
        $this->assertEquals(25, $status['limits']['burst']);
    }

    public function test_backoff_period_prevents_requests()
    {
        // Set backoff period
        $backoffKey = "rate_limit:backoff:{$this->systemUser->id}";
        $backoffUntil = now()->addMinutes(5)->timestamp;
        Cache::put($backoffKey, $backoffUntil, now()->addMinutes(10));

        $result = $this->rateLimitService->canMakeRequest($this->systemUser);

        $this->assertFalse($result['allowed']);
        $this->assertEquals('Rate limit backoff period active', $result['reason']);
        $this->assertEquals('backoff', $result['limit_type']);
    }

    public function test_successful_request_resets_violations()
    {
        // Record some violations
        $this->rateLimitService->recordViolation($this->systemUser, 'minute');
        $this->rateLimitService->recordViolation($this->systemUser, 'minute');

        $violationKey = "rate_limit:violations:{$this->systemUser->id}";
        $this->assertEquals(2, Cache::get($violationKey, 0));

        // Record successful request
        $this->rateLimitService->recordRequest($this->systemUser);

        // Violations should be reset
        $this->assertEquals(0, Cache::get($violationKey, 0));
    }
}
