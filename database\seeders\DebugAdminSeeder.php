<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Post;
use Illuminate\Database\Seeder;

class DebugAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('=== DEBUG ADMIN USER ===');
        
        $user = User::where('email', '<EMAIL>')->first();
        
        if (!$user) {
            $this->command->error('Admin user not found!');
            return;
        }
        
        $this->command->info("User: {$user->name} ({$user->email})");
        $this->command->info("Roles: " . $user->roles->pluck('name')->join(', '));
        $this->command->info("isAdmin(): " . ($user->isAdmin() ? 'true' : 'false'));
        $this->command->info("hasRole('admin'): " . ($user->hasRole('admin') ? 'true' : 'false'));
        $this->command->info("hasRole('Admin'): " . ($user->hasRole('Admin') ? 'true' : 'false'));
        
        $this->command->info('=== POSTS COUNT ===');
        $totalPosts = Post::count();
        $this->command->info("Total posts in database: {$totalPosts}");
        
        if ($totalPosts === 0) {
            $this->command->info('Creating some test posts...');
            
            // Tạo một vài posts test
            for ($i = 1; $i <= 5; $i++) {
                Post::create([
                    'title' => "Test Ticket {$i}",
                    'content' => "This is test ticket content {$i}",
                    'user_id' => $user->id,
                    'slug' => "test-ticket-{$i}",
                    'status' => 'open',
                    'is_published' => true,
                ]);
            }
            
            $this->command->info('Created 5 test posts');
        }
        
        $this->command->info('=== DEPARTMENTS ===');
        $departments = $user->departments;
        if ($departments->count() > 0) {
            $this->command->info("User departments: " . $departments->pluck('name')->join(', '));
        } else {
            $this->command->info("User has no departments assigned");
        }
    }
}
