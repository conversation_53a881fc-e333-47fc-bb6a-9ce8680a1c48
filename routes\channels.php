<?php

use Illuminate\Support\Facades\Broadcast;

// User-specific channels
Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('user.{id}', function ($user, $id) {
    return $user && (int) $user->id === (int) $id;
});

// Notification channels
Broadcast::channel('notifications.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

Broadcast::channel('notifications-comment.{userId}', function ($user, $userId) {
    return (int) $user->id === (int) $userId;
});

// Post and ticket channels
Broadcast::channel('post.{postId}', function ($user, $postId) {
    return true; // Allow all users to listen to this channel
});

Broadcast::channel('ticket.{ticketId}', function ($user, $ticketId) {
    return true; // Allow all users to listen to ticket comments
});

// Department channel
Broadcast::channel('department.{departmentId}', function ($user, $departmentId) {
    // Kiểm tra xem người dùng có thuộc phòng ban này không
    return $user->departments()->where('departments.id', $departmentId)->exists();
});

// Public channels for general notifications
Broadcast::channel('notifications', function ($user) {
    return true; // Allow all authenticated users
});