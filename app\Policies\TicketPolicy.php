<?php

namespace App\Policies;

use App\Models\Ticket;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class TicketPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any tickets.
     */
    public function viewAny(User $user): bool
    {
        // Super Admin, Admin, Employee có thể xem tất cả tickets
        return $user->can('view-any-tickets');
    }

    /**
     * Determine whether the user can view public tickets.
     */
    public function viewPublic(?User $user): bool
    {
        // Tất cả mọi người có thể xem public tickets, kể cả guest
        if (!$user) {
            return true;
        }
        return $user->can('view-public-tickets');
    }

    /**
     * Determine whether the user can view the specific ticket.
     */
    public function view(?User $user, Ticket $ticket): bool
    {
        // Public tickets - ai cũng có thể xem
        if ($ticket->is_published) {
            return true;
        }

        // Nếu không đăng nhập thì không thể xem private tickets
        if (!$user) {
            return false;
        }

        // Admin/Employee có thể xem tất cả tickets
        if ($user->can('view-any-tickets')) {
            return true;
        }

        // Customer chỉ có thể xem tickets của mình
        if ($user->can('view-own-tickets')) {
            return $user->id === $ticket->user_id;
        }

        return false;
    }

    /**
     * Determine whether the user can create tickets.
     * Ai cũng có thể tạo tickets - không cần phân quyền
     */
    public function create(?User $user): bool
    {
        // Ai cũng có thể tạo tickets, kể cả guest users
        return true;
    }

    /**
     * Determine whether the user can update the ticket.
     */
    public function update(User $user, Ticket $ticket): bool
    {
        // Admin/Employee có thể update bất kỳ ticket nào
        if ($user->can('update-any-tickets')) {
            return true;
        }

        // Customer chỉ có thể update ticket của mình
        if ($user->can('update-own-tickets')) {
            return $user->id === $ticket->user_id;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the ticket.
     */
    public function delete(User $user, Ticket $ticket): bool
    {
        // Super Admin/Admin có thể xóa bất kỳ ticket nào
        if ($user->can('delete-any-tickets')) {
            return true;
        }

        // Customer chỉ có thể xóa ticket của mình
        if ($user->can('delete-own-tickets')) {
            return $user->id === $ticket->user_id;
        }

        return false;
    }

    /**
     * Determine whether the user can update ticket status.
     */
    public function updateStatus(User $user, Ticket $ticket): bool
    {
        return $user->can('update-status-tickets');
    }

    /**
     * Determine whether the user can restore the ticket.
     */
    public function restore(User $user, Ticket $ticket): bool
    {
        // Chỉ Super Admin/Admin có thể restore
        return $user->can('delete-any-tickets');
    }

    /**
     * Determine whether the user can permanently delete the ticket.
     */
    public function forceDelete(User $user, Ticket $ticket): bool
    {
        // Chỉ Super Admin có thể force delete
        return $user->hasRole('Super Admin');
    }

    /**
     * Determine whether the user can receive notifications for this ticket.
     */
    public function receiveNotification(User $user, Ticket $ticket): bool
    {
        // Public tickets - ai cũng có thể nhận notification
        if ($ticket->is_published) {
            return true;
        }

        // Private tickets - chỉ admin và chủ sở hữu
        return $user->can('view-any-tickets') || $user->id === $ticket->user_id;
    }
}
