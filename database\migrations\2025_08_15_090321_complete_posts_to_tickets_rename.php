<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Rename remaining tables if they exist
        if (Schema::hasTable('post_upvotes') && !Schema::hasTable('ticket_upvotes')) {
            Schema::rename('post_upvotes', 'ticket_upvotes');
            Schema::table('ticket_upvotes', function (Blueprint $table) {
                if (Schema::hasColumn('ticket_upvotes', 'post_id')) {
                    $table->renameColumn('post_id', 'ticket_id');
                }
            });
        }

        if (Schema::hasTable('post_tag') && !Schema::hasTable('ticket_tag')) {
            Schema::rename('post_tag', 'ticket_tag');
            Schema::table('ticket_tag', function (Blueprint $table) {
                if (Schema::hasColumn('ticket_tag', 'post_id')) {
                    $table->renameColumn('post_id', 'ticket_id');
                }
            });
        }

        if (Schema::hasTable('category_post') && !Schema::hasTable('category_ticket')) {
            Schema::rename('category_post', 'category_ticket');
            Schema::table('category_ticket', function (Blueprint $table) {
                if (Schema::hasColumn('category_ticket', 'post_id')) {
                    $table->renameColumn('post_id', 'ticket_id');
                }
            });
        }

        if (Schema::hasTable('post_tag_priorities') && !Schema::hasTable('ticket_tag_priorities')) {
            Schema::rename('post_tag_priorities', 'ticket_tag_priorities');
            Schema::table('ticket_tag_priorities', function (Blueprint $table) {
                if (Schema::hasColumn('ticket_tag_priorities', 'post_id')) {
                    $table->renameColumn('post_id', 'ticket_id');
                }
            });
        }

        // Update comments table if needed
        if (Schema::hasColumn('comments', 'post_id')) {
            Schema::table('comments', function (Blueprint $table) {
                $table->renameColumn('post_id', 'ticket_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reverse the changes
        if (Schema::hasColumn('comments', 'ticket_id')) {
            Schema::table('comments', function (Blueprint $table) {
                $table->renameColumn('ticket_id', 'post_id');
            });
        }

        if (Schema::hasTable('ticket_tag_priorities')) {
            Schema::table('ticket_tag_priorities', function (Blueprint $table) {
                if (Schema::hasColumn('ticket_tag_priorities', 'ticket_id')) {
                    $table->renameColumn('ticket_id', 'post_id');
                }
            });
            Schema::rename('ticket_tag_priorities', 'post_tag_priorities');
        }

        if (Schema::hasTable('category_ticket')) {
            Schema::table('category_ticket', function (Blueprint $table) {
                if (Schema::hasColumn('category_ticket', 'ticket_id')) {
                    $table->renameColumn('ticket_id', 'post_id');
                }
            });
            Schema::rename('category_ticket', 'category_post');
        }

        if (Schema::hasTable('ticket_tag')) {
            Schema::table('ticket_tag', function (Blueprint $table) {
                if (Schema::hasColumn('ticket_tag', 'ticket_id')) {
                    $table->renameColumn('ticket_id', 'post_id');
                }
            });
            Schema::rename('ticket_tag', 'post_tag');
        }

        if (Schema::hasTable('ticket_upvotes')) {
            Schema::table('ticket_upvotes', function (Blueprint $table) {
                if (Schema::hasColumn('ticket_upvotes', 'ticket_id')) {
                    $table->renameColumn('ticket_id', 'post_id');
                }
            });
            Schema::rename('ticket_upvotes', 'post_upvotes');
        }
    }
};
