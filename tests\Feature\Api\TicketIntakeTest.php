<?php

namespace Tests\Feature\Api;

use App\Models\User;
use App\Models\Ticket;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class TicketIntakeTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private User $systemUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a system user for testing
        $this->systemUser = User::factory()->create([
            'is_system_user' => true,
            'system_type' => 'payment',
            'is_active' => true,
            'rate_limit_tier' => 'standard',
            'rate_limit_per_minute' => 60,
            'rate_limit_burst' => 10,
        ]);
    }

    public function test_can_create_ticket_with_valid_data()
    {
        Sanctum::actingAs($this->systemUser);

        $ticketData = [
            'title' => 'Payment Issue',
            'content' => 'Customer cannot complete payment',
            'requester_name' => '<PERSON>',
            'requester_email' => '<EMAIL>',
            'priority' => 'high',
            'external_id' => 'PAY-123456',
            'tags' => ['payment', 'urgent'],
        ];

        $response = $this->postJson('/api/v1/tickets', $ticketData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'ticket_id',
                        'ticket_url',
                        'status',
                        'ticket' => [
                            'id',
                            'title',
                            'priority',
                            'status',
                            'creator',
                            'requester',
                            'tags'
                        ]
                    ]
                ]);

        $this->assertDatabaseHas('tickets', [
            'title' => 'Payment Issue',
            'user_id' => $this->systemUser->id,
            'requester_email' => '<EMAIL>',
            'external_id' => 'PAY-123456',
            'source_system' => 'payment',
        ]);

        $this->assertDatabaseHas('ticket_external_refs', [
            'source_system' => 'payment',
            'external_id' => 'PAY-123456',
        ]);
    }

    public function test_prevents_duplicate_tickets()
    {
        Sanctum::actingAs($this->systemUser);

        $ticketData = [
            'title' => 'Payment Issue',
            'content' => 'Customer cannot complete payment',
            'requester_name' => 'John Doe',
            'requester_email' => '<EMAIL>',
            'external_id' => 'PAY-123456',
        ];

        // Create first ticket
        $this->postJson('/api/v1/tickets', $ticketData)->assertStatus(201);

        // Try to create duplicate
        $response = $this->postJson('/api/v1/tickets', $ticketData);

        $response->assertStatus(409)
                ->assertJson([
                    'success' => false,
                    'data' => ['status' => 'duplicate']
                ]);
    }

    public function test_validates_required_fields()
    {
        Sanctum::actingAs($this->systemUser);

        $response = $this->postJson('/api/v1/tickets', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors([
                    'title',
                    'content',
                    'requester_name',
                    'requester_email'
                ]);
    }

    public function test_validates_email_format()
    {
        Sanctum::actingAs($this->systemUser);

        $response = $this->postJson('/api/v1/tickets', [
            'title' => 'Test',
            'content' => 'Test content',
            'requester_name' => 'John Doe',
            'requester_email' => 'invalid-email',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['requester_email']);
    }

    public function test_validates_priority_values()
    {
        Sanctum::actingAs($this->systemUser);

        $response = $this->postJson('/api/v1/tickets', [
            'title' => 'Test',
            'content' => 'Test content',
            'requester_name' => 'John Doe',
            'requester_email' => '<EMAIL>',
            'priority' => 'invalid',
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['priority']);
    }

    public function test_requires_system_user_authentication()
    {
        $regularUser = User::factory()->create(['is_system_user' => false]);
        Sanctum::actingAs($regularUser);

        $response = $this->postJson('/api/v1/tickets', [
            'title' => 'Test',
            'content' => 'Test content',
            'requester_name' => 'John Doe',
            'requester_email' => '<EMAIL>',
        ]);

        $response->assertStatus(403);
    }

    public function test_can_retrieve_created_ticket()
    {
        Sanctum::actingAs($this->systemUser);

        $ticket = Ticket::factory()->create([
            'user_id' => $this->systemUser->id,
            'title' => 'Test Ticket',
        ]);

        $response = $this->getJson("/api/v1/tickets/{$ticket->id}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'ticket' => [
                            'id',
                            'title',
                            'content',
                            'status',
                            'creator',
                            'requester'
                        ]
                    ]
                ]);
    }

    public function test_cannot_retrieve_other_system_user_tickets()
    {
        $otherSystemUser = User::factory()->create([
            'is_system_user' => true,
            'is_active' => true,
        ]);

        $ticket = Ticket::factory()->create([
            'user_id' => $otherSystemUser->id,
        ]);

        Sanctum::actingAs($this->systemUser);

        $response = $this->getJson("/api/v1/tickets/{$ticket->id}");

        $response->assertStatus(404);
    }

    public function test_can_list_own_tickets()
    {
        Sanctum::actingAs($this->systemUser);

        // Create tickets for this system user
        Ticket::factory()->count(3)->create([
            'user_id' => $this->systemUser->id,
        ]);

        // Create ticket for another user (should not appear)
        $otherUser = User::factory()->create(['is_system_user' => true]);
        Ticket::factory()->create(['user_id' => $otherUser->id]);

        $response = $this->getJson('/api/v1/tickets');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'tickets',
                        'pagination'
                    ]
                ]);

        $tickets = $response->json('data.tickets');
        $this->assertCount(3, $tickets);
    }

    public function test_can_filter_tickets_by_status()
    {
        Sanctum::actingAs($this->systemUser);

        Ticket::factory()->create([
            'user_id' => $this->systemUser->id,
            'status' => 'open',
        ]);

        Ticket::factory()->create([
            'user_id' => $this->systemUser->id,
            'status' => 'closed',
        ]);

        $response = $this->getJson('/api/v1/tickets?status=open');

        $response->assertStatus(200);
        $tickets = $response->json('data.tickets');
        $this->assertCount(1, $tickets);
        $this->assertEquals('open', $tickets[0]['status']);
    }

    public function test_can_get_statistics()
    {
        Sanctum::actingAs($this->systemUser);

        Ticket::factory()->count(2)->create([
            'user_id' => $this->systemUser->id,
            'priority' => 'high',
        ]);

        Ticket::factory()->create([
            'user_id' => $this->systemUser->id,
            'priority' => 'low',
        ]);

        $response = $this->getJson('/api/v1/tickets/statistics');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'total_tickets',
                        'by_priority',
                        'by_status'
                    ]
                ]);

        $data = $response->json('data');
        $this->assertEquals(3, $data['total_tickets']);
        $this->assertEquals(2, $data['by_priority']['high']);
        $this->assertEquals(1, $data['by_priority']['low']);
    }
}
