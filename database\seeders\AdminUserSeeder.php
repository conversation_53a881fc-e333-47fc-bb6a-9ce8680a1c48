<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Tạo admin user
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );

        // Gán role Admin cho user
        $adminRole = Role::findByName('Admin');
        if ($adminRole && !$adminUser->hasRole('Admin')) {
            $adminUser->assignRole('Admin');
        }

        $this->command->info('Admin user created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: password123');
        
        // Tạo Manager user
        $managerUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Manager User',
                'password' => Hash::make('manager123'),
                'email_verified_at' => now(),
            ]
        );

        // Gán role Manager cho user
        $managerRole = Role::findByName('Manager');
        if ($managerRole && !$managerUser->hasRole('Manager')) {
            $managerUser->assignRole('Manager');
        }

        $this->command->info('Manager user created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: manager123');

        // Tạo Employee user để test
        $employeeUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Employee User',
                'password' => Hash::make('employee123'),
                'email_verified_at' => now(),
            ]
        );

        // Gán role Employee cho user
        $employeeRole = Role::findByName('Employee');
        if ($employeeRole && !$employeeUser->hasRole('Employee')) {
            $employeeUser->assignRole('Employee');
        }

        $this->command->info('Employee user created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: employee123');

        // Tạo Customer user để test
        $customerUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Customer User',
                'password' => Hash::make('customer123'),
                'email_verified_at' => now(),
            ]
        );

        // Gán role Customer cho user
        $customerRole = Role::findByName('Customer');
        if ($customerRole && !$customerUser->hasRole('Customer')) {
            $customerUser->assignRole('Customer');
        }

        $this->command->info('Customer user created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: customer123');
    }
}