<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Ticket;
use App\Services\AdminService;
use Illuminate\Database\Seeder;
use Illuminate\Http\Request;

class TestAdminPostsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('=== TESTING ADMIN POSTS ACCESS ===');
        
        $adminUser = User::where('email', '<EMAIL>')->first();
        
        if (!$adminUser) {
            $this->command->error('Admin user not found!');
            return;
        }
        
        $this->command->info("Testing with user: {$adminUser->name} ({$adminUser->email})");
        $this->command->info("User roles: " . $adminUser->roles->pluck('name')->join(', '));
        $this->command->info("isAdmin(): " . ($adminUser->isAdmin() ? 'true' : 'false'));
        
        // Test permissions
        $this->command->info('=== PERMISSIONS TEST ===');
        $permissions = [
            'access-admin-dashboard',
            'view-any-posts',
            'view-public-posts',
            'create-posts',
            'update-any-posts',
            'delete-any-posts'
        ];
        
        foreach ($permissions as $permission) {
            $hasPermission = $adminUser->can($permission);
            $this->command->info("  {$permission}: " . ($hasPermission ? '✓' : '✗'));
        }
        
        // Check posts count
        $this->command->info('=== POSTS COUNT ===');
        $totalPosts = Post::count();
        $this->command->info("Total posts in database: {$totalPosts}");
        
        if ($totalPosts === 0) {
            $this->command->info('Creating test posts...');
            
            // Create test posts
            for ($i = 1; $i <= 10; $i++) {
                Post::create([
                    'title' => "Test Ticket {$i}",
                    'content' => "This is test ticket content {$i}",
                    'user_id' => $adminUser->id,
                    'slug' => "test-ticket-{$i}",
                    'status' => 'open',
                    'is_published' => true,
                    'priority' => ['low', 'medium', 'high', 'urgent'][array_rand(['low', 'medium', 'high', 'urgent'])],
                ]);
            }
            
            $this->command->info('Created 10 test posts');
            $totalPosts = Post::count();
        }
        
        // Test AdminService
        $this->command->info('=== ADMIN SERVICE TEST ===');
        
        // Simulate login as admin
        auth()->login($adminUser);
        
        $adminService = new AdminService();
        
        // Test getDashboardData
        $dashboardData = $adminService->getDashboardData($adminUser);
        $this->command->info("Dashboard posts count: " . count($dashboardData['posts']));
        $this->command->info("Total posts from service: " . $dashboardData['totalPosts']);
        
        // Test getAllPosts
        $request = new Request();
        $postsData = $adminService->getAllPosts($request, 10);
        $this->command->info("getAllPosts returned: " . count($postsData['data']) . " posts");
        
        if (count($postsData['data']) > 0) {
            $this->command->info("First post: " . $postsData['data'][0]['title']);
        }
        
        $this->command->info('=== DEPARTMENTS CHECK ===');
        $departments = $adminUser->departments;
        if ($departments->count() > 0) {
            $this->command->info("Admin user departments: " . $departments->pluck('name')->join(', '));
        } else {
            $this->command->info("Admin user has no departments (this is normal for admin)");
        }
        
        $this->command->info('=== TEST COMPLETED ===');
    }
}
