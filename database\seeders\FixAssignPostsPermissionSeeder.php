<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class FixAssignPostsPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('=== FIXING ASSIGN-POSTS PERMISSION ===');
        
        // 1. Đảm bảo permission assign-posts tồn tại
        $assignPostsPermission = Permission::firstOrCreate([
            'name' => 'assign-posts',
            'guard_name' => 'web'
        ]);
        $this->command->info('✅ Permission assign-posts exists');
        
        // 2. Đ<PERSON>m bảo Admin role có permission assign-posts
        $adminRole = Role::where('name', 'Admin')->first();
        if ($adminRole) {
            if (!$adminRole->hasPermissionTo('assign-posts')) {
                $adminRole->givePermissionTo('assign-posts');
                $this->command->info('✅ Gave assign-posts permission to Admin role');
            } else {
                $this->command->info('✅ Admin role already has assign-posts permission');
            }
        } else {
            $this->command->error('❌ Admin role not found!');
            return;
        }
        
        // 3. Đảm bảo Manager role có permission assign-posts
        $managerRole = Role::where('name', 'Manager')->first();
        if ($managerRole) {
            if (!$managerRole->hasPermissionTo('assign-posts')) {
                $managerRole->givePermissionTo('assign-posts');
                $this->command->info('✅ Gave assign-posts permission to Manager role');
            } else {
                $this->command->info('✅ Manager role already has assign-posts permission');
            }
        }
        
        // 4. Kiểm tra admin user
        $adminUser = User::where('email', '<EMAIL>')->first();
        if ($adminUser) {
            // Đảm bảo có role Admin
            if (!$adminUser->hasRole('Admin')) {
                $adminUser->assignRole('Admin');
                $this->command->info('✅ Assigned Admin role to admin user');
            }
            
            // Test permission
            $canAssign = $adminUser->can('assign-posts');
            $this->command->info("Admin user can assign-posts: " . ($canAssign ? '✅ YES' : '❌ NO'));
            
            if (!$canAssign) {
                // Force refresh permissions
                $adminUser->refresh();
                app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
                
                $canAssignAfterRefresh = $adminUser->can('assign-posts');
                $this->command->info("After refresh: " . ($canAssignAfterRefresh ? '✅ YES' : '❌ NO'));
            }
        } else {
            $this->command->error('❌ Admin user not found!');
        }
        
        // 5. Clear cache
        $this->command->info('=== CLEARING CACHE ===');
        \Artisan::call('cache:clear');
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();
        $this->command->info('✅ Cache cleared');
        
        $this->command->info('=== NEXT STEPS ===');
        $this->command->info('1. Refresh browser page');
        $this->command->info('2. Check /test-assign route');
        $this->command->info('3. Check browser console for AbilityContext logs');
        $this->command->info('4. canAssignPosts should now be true');
    }
}
